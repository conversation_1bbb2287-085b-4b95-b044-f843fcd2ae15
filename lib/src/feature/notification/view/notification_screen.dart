import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/notification/view/notification_wm.dart';
import 'package:sba/src/feature/notification/widget/notification_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/notifications/model/notification_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

class NotificationScreen extends ElementaryWidget<INotificationWidgetModel> {
  const NotificationScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultNotificationWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(INotificationWidgetModel wm) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: wm.refresh,
        notificationPredicate: (n) => n.depth == 1,
        child: NestedScrollView(
          headerSliverBuilder: (context, _) => [
            SliverTitle(
              text: context.l10n.navigation_notification,
              padding: UISpacing.defaultElementPaddingWithoutBottom,
            ),
          ],
          body: StateNotifierBuilder(
            listenableState: wm.notificationState,
            builder: (context, data) => SmartListView(
              data: data,
              skeletonData: List.filled(5, NotificationData.fake()),
              padding: const EdgeInsets.only(
                top: UISpacing.xxl,
                bottom: UISpacing.xxl,
              ),
              loading: data == null,
              emptyText: context.l10n.empty_notification,
              separator: const Divider(),
              builder: (context, element) => NotificationTile(data: element),
            ),
          ),
        ),
      ),
    );
  }
}
