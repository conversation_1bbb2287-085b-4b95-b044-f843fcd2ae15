import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/notification/view/notification_model.dart';
import 'package:sba/src/feature/notification/view/notification_screen.dart';
import 'package:sba/src/repository/notifications/model/notification_data.dart';

abstract interface class INotificationWidgetModel implements IWidgetModel {
  StateNotifier<List<NotificationData>> get notificationState;

  Future<void> refresh();
}

NotificationWidgetModel defaultNotificationWidgetModelFactory(
  BuildContext context,
) {
  return NotificationWidgetModel(
    NotificationModel(
      errorHandler: get(),
      repository: get(),
    ),
  );
}

class NotificationWidgetModel
    extends WidgetModel<NotificationScreen, NotificationModel>
    implements INotificationWidgetModel {
  NotificationWidgetModel(super.model);

  final StateNotifier<List<NotificationData>> _notificationState =
      StateNotifier();
  final CompositeSubscription _sub = CompositeSubscription();

  @override
  void initWidgetModel() {
    _sub.add(
      model.notifications
          .asyncMap((result) async {
            if (result is Failure) {
              await context.showGeneralErrorDialog(failure: result as Failure);
            }

            return result;
          })
          .map((e) => e.maybeValue)
          .map(
            (data) => data?.sorted(
              (a, b) => b.created.compareTo(a.created),
            ),
          )
          .listen(_notificationState.accept),
    );
    super.initWidgetModel();
  }

  @override
  Future<void> refresh() => model.refresh();

  @override
  void dispose() {
    model.seeAllNotifications();
    _sub.dispose();
    super.dispose();
  }

  @override
  StateNotifier<List<NotificationData>> get notificationState =>
      _notificationState;
}
