import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/repository/notifications/model/notification_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class NotificationTile extends StatelessWidget {
  const NotificationTile({required this.data, super.key});

  final NotificationData data;

  @override
  Widget build(BuildContext context) {
    return Container(
      color:
          data.seen ? Colors.transparent : context.theme.colorScheme.secondary,
      padding: const EdgeInsets.symmetric(vertical: UISpacing.l),
      child: Row(
        children: [
          const Gap(UISpacing.l),
          Visibility(
            visible: !data.seen,
            maintainState: true,
            maintainAnimation: true,
            maintainSize: true,
            child: Icon(
              Icons.circle,
              size: 10,
              color: context.theme.colorScheme.primary,
            ),
          ),
          const Gap(UISpacing.m),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  data.title,
                  style: context.textTheme.headlineMedium,
                ),
                const Gap(UISpacing.s),
                Text(
                  data.text,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: data.seen ? UIColors.paragraph : UIColors.heading,
                  ),
                ),
                const Gap(UISpacing.xs),
                Text(
                  data.created.formatTimeAgo(context),
                  maxLines: 1,
                  style: UITypography.caption
                      .copyWith(color: UIColors.menuForeground),
                ),
              ],
            ),
          ),
          const Gap(UISpacing.xxl),
        ],
      ),
    );
  }
}
