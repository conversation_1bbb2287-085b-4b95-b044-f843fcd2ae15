import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/legal_help/view/legal_help_wm.dart';
import 'package:sba/src/feature/legal_help/widget/cases_tab.dart';
import 'package:sba/src/feature/legal_help/widget/help_tab.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

enum _Tabs {
  cases,
  help;
}

extension _TabExtension on _Tabs {
  String localizedName(BuildContext context) => switch (this) {
        _Tabs.cases => context.l10n.legal_help_tab_cases,
        _Tabs.help => context.l10n.legal_help_tab_questions,
      };
}

class LegalHelpScreen extends ElementaryWidget<ILegalHelpWidgetModel> {
  const LegalHelpScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultLegalHelpWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(ILegalHelpWidgetModel wm) {
    return DefaultTabController(
      length: _Tabs.values.length,
      child: Scaffold(
        body: NestedScrollView(
          headerSliverBuilder: (context, _) => [
            SliverTitle(
              text: context.l10n.navigation_legal_help,
              padding: UISpacing.defaultElementPaddingWithoutBottom,
            ),
            const SliverGap(UISpacing.m),
            PinnedHeaderSliver(
              child: Container(
                padding: UISpacing.defaultElementHorizontalPadding,
                color: context.theme.colorScheme.surface,
                child: TabBar(
                  tabs: _Tabs.values
                      .map(
                        (it) => Text(
                          it.localizedName(context),
                          textAlign: TextAlign.center,
                        ),
                      )
                      .toList(),
                ),
              ),
            ),
          ],
          body: TabBarView(
            children: [
              StateNotifierBuilder(
                listenableState: wm.questions,
                builder: (context, data) => CasesTab(
                  key: PageStorageKey(_Tabs.cases.name),
                  data: data,
                ),
              ),
              HelpTab(
                key: PageStorageKey(_Tabs.help.name),
                onSendTap: (q) => wm.onSendLegalQuestionTap(question: q),
                onCallTap: wm.onCallSupportCenterTap,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
