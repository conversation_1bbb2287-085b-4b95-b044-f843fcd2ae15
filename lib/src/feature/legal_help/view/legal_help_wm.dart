import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/environment.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/external_launcher/external_launcher.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/legal_help/view/legal_help_model.dart';
import 'package:sba/src/feature/legal_help/view/legal_help_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/contact/model/questions.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';

abstract interface class ILegalHelpWidgetModel implements IWidgetModel {
  StateNotifier<List<QuestionGroup>?> get questions;

  Future<void> onSendLegalQuestionTap({required String question});

  Future<void> onCallSupportCenterTap();
}

LegalHelpWidgetModel defaultLegalHelpWidgetModelFactory(BuildContext context) {
  return LegalHelpWidgetModel(
    LegalHelpModel(contactRepository: get(), errorHandler: get()),
  );
}

class LegalHelpWidgetModel extends WidgetModel<LegalHelpScreen, LegalHelpModel>
    implements ILegalHelpWidgetModel {
  LegalHelpWidgetModel(super.model);

  final _questions = StateNotifier<List<QuestionGroup>?>();

  @override
  void initWidgetModel() {
    _loadData();
    super.initWidgetModel();
  }

  void _loadData() async {
    final result = await model.questions;
    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
    }

    _questions.accept(result.maybeValue);
  }

  @override
  StateNotifier<List<QuestionGroup>?> get questions => _questions;

  @override
  Future<void> onCallSupportCenterTap() async {
    final result = await ExternalLauncher.call(Environment().contactNumber);
    if (!result) {
      await context.showMessageDialog(
        builder: (context) => MessageDialog.error(
          context: context,
          text: context.l10n.error_call,
        ),
      );
      return;
    }
  }

  @override
  Future<void> onSendLegalQuestionTap({required String question}) async {
    await context.showLoadingDialog();
    final result = await model.sendLegalQuestion(question);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    await context.showMessageDialog(
      builder: (context) => MessageDialog.info(
        type: MessageDialogType.success,
        title: context.l10n.legal_help_send_message_success_title,
        text: context.l10n.legal_help_send_message_success_text,
        actionText: context.l10n.action_ok,
      ),
    );
  }
}
