import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/contact/contact_repository.dart';
import 'package:sba/src/repository/contact/model/questions.dart';

class LegalHelpModel extends ElementaryModel {
  LegalHelpModel({
    required ContactRepository contactRepository,
    super.errorHandler,
  }) : _contactRepository = contactRepository;

  final ContactRepository _contactRepository;

  Future<Result<void>> sendLegalQuestion(String question) =>
      _contactRepository.sendLegalQuestion(question);

  Future<Result<List<QuestionGroup>>> get questions =>
      _contactRepository.getFAQ();
}
