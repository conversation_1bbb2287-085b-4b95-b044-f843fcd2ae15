import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class HelpTab extends StatelessWidget {
  HelpTab({required this.onSendTap, required this.onCallTap, super.key});

  final ValueChanged<String> onSendTap;
  final VoidCallback onCallTap;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final String _question = 'question';

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      key: key,
      padding: UISpacing.defaultScreenPadding,
      child: FormBuilder(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.l10n.legal_help_send_question_section,
              style: context.textTheme.headlineMedium,
            ),
            const Gap(UISpacing.l),
            FormBuilderTextField(
              name: _question,
              maxLines: null,
              minLines: 4,
              keyboardType: TextInputType.multiline,
              decoration: InputDecoration(
                labelText: context.l10n.form_question,
                hintText: context.l10n.form_question_hint,
              ),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
            const Gap(UISpacing.l),
            FilledButton(
              onPressed: _onSubmit,
              child: Text(context.l10n.action_send),
            ),
            const Gap(UISpacing.xxl),
            Text(
              context.l10n.legal_help_call_for_help_section,
              style: context.textTheme.headlineMedium,
            ),
            const Gap(UISpacing.l),
            FilledButton(
              onPressed: onCallTap,
              child: Text(context.l10n.action_call),
            ),
          ],
        ),
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSendTap(_formKey.currentState!.value[_question] as String);
      _formKey.currentState!.reset();
    }
  }
}
