import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/iterable_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/contact/model/questions.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/html_view.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

class CasesTab extends StatelessWidget {
  const CasesTab({super.key, this.data});

  final List<QuestionGroup>? data;

  @override
  Widget build(BuildContext context) {
    return SmartListView(
      key: key,
      loading: data == null,
      data: data,
      skeletonData: List.filled(2, QuestionGroup.fake()),
      emptyText: context.l10n.empty_common,
      padding: UISpacing.defaultElementPaddingWithoutTop,
      builder: (context, data) => Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Gap(UISpacing.xxl),
          Text(
            data.name,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          ...data.questions
              .mapIndexed(
                (i, e) => ExpansionTile(
                  key: PageStorageKey(e),
                  title: Text(
                    e.question,
                    style: context.textTheme.headlineSmall,
                  ),
                  children: [
                    HtmlView(html: e.answer),
                  ],
                ),
              )
              .map((e) => e as Widget)
              .intersperse(
                const Divider(),
              ),
        ],
      ),
    );
  }
}
