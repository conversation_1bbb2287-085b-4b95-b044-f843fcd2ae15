import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/toll/view/toll_wm.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';
import 'package:sba/src/ui/widget/navigation_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class TollScreen extends ElementaryWidget<ITollWidgetModel> {
  const TollScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultTollWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(ITollWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_toll,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: Builder(
          builder: (context) => CustomScrollView(
            slivers: [
              SliverPadding(
                padding: UISpacing.defaultScreenPadding,
                sliver: SliverGrid.count(
                  crossAxisCount: 2,
                  mainAxisSpacing: UISpacing.s,
                  crossAxisSpacing: UISpacing.s,
                  childAspectRatio: 17 / 10,
                  children: [
                    NavigationBox(
                      icon: Assets.icons.searchCheck.svg(),
                      text: context.l10n.navigation_toll_check,
                      onTap: wm.onCheckTap,
                    ),
                    NavigationBox(
                      icon: Assets.icons.addShoppingCart.svg(),
                      text: context.l10n.navigation_toll_buy,
                      onTap: wm.onBuyTap,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
