import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/toll/buy/model/toll_buy_step.dart';
import 'package:sba/src/feature/toll/buy/toll_buy_model.dart';
import 'package:sba/src/feature/toll/buy/toll_buy_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/toll/model/toll_category.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/toll/model/toll_data.dart';
import 'package:sba/src/repository/toll/model/toll_product.dart';
import 'package:sba/src/repository/toll/model/toll_register_data.dart';
import 'package:sba/src/repository/toll/model/toll_status.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:sba/src/ui/widget/message_box.dart';

typedef TollDataRecord = ({
  TollCountry country,
  String plateNumber,
  DateTime date,
  String email
});

abstract interface class ITollBuyWidgetModel implements IWidgetModel {
  StateNotifier<TollBuyStep> get step;

  StateNotifier<TollRegisterData> get data;

  StateNotifier<List<TollProduct>> get products;

  StateNotifier<List<VehicleData>> get vehicles;

  StateNotifier<List<TollCountry>> get countries;

  StateNotifier<UserData> get user;

  StateNotifier<Widget> get message;

  void onBack();

  void onCategoryTap(TollCategory category);

  void onProductTap(TollProduct product);

  void onDataTap(TollDataRecord record);

  void onPaymentTap();

  void onPayTap();
}

TollBuyWidgetModel defaultTollBuyWidgetModelFactory(BuildContext context) {
  return TollBuyWidgetModel(
    TollBuyModel(
      tollRepository: get(),
      vehicleRepository: get(),
      userRepository: get(),
      errorHandler: get(),
    ),
  );
}

class TollBuyWidgetModel extends WidgetModel<TollBuyScreen, TollBuyModel>
    implements ITollBuyWidgetModel {
  TollBuyWidgetModel(super.model);

  final _step = StateNotifier<TollBuyStep>(initValue: TollBuyStep.vehicleType);
  final _products = StateNotifier<List<TollProduct>>();
  final _countries = StateNotifier<List<TollCountry>>();
  final _vehicles = StateNotifier<List<VehicleData>>();
  final _user = StateNotifier<UserData>();
  final _data = StateNotifier<TollRegisterData>(
    initValue: TollRegisterData.empty(),
  );
  final _message = StateNotifier<Widget>();
  TollData? _tollData;

  @override
  void initWidgetModel() {
    _loadData();
    super.initWidgetModel();
  }

  void _loadData() async {
    _products.accept(await model.products);
    _vehicles.accept(await model.vehicles);
    _user.accept(await model.user);
    _countries.accept(await model.countries);
  }

  @override
  void onBack() {
    final previous = _step.value?.previous;

    if (previous == null) {
      return;
    }

    _step.accept(previous);
  }

  @override
  void onCategoryTap(TollCategory category) {
    _data.accept(
      _data.value?.changeCategory(category),
    );

    _onNext();
  }

  @override
  void onProductTap(TollProduct product) {
    _data.accept(_data.value?.copyWith(selectedProduct: product));
    _onNext();
  }

  @override
  void onDataTap(TollDataRecord record) async {
    _data.accept(
      _data.value?.copyWith(
        country: record.country,
        plateNumber: record.plateNumber,
        startDate: record.date,
        email: record.email,
      ),
    );

    await context.showMessageDialog(
      builder: (context) => _buildConfirmDialog(
        context,
        _data.value!,
        _onNext,
      ),
    );
  }

  @override
  void onPaymentTap() async {
    await context.showLoadingDialog();
    final result = await model.registerToll(_data.value!);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      return;
    }

    _tollData = result.maybeValue;
    _message.accept(_buildPaymentMessage());

    _onNext();
  }

  @override
  void onPayTap() async {
    if (_tollData == null) return;

    if (_tollData?.state == TollStatus.active) return;

    await context.showLoadingDialog();
    final activateResult = await model.activateToll(_tollData!);
    context.hideLoadingDialog();

    if (activateResult is Failure) {
      await context.showGeneralErrorDialog(failure: activateResult as Failure);
      return;
    }

    _tollData = activateResult.maybeValue;

    _message.accept(_buildPaymentMessage());
  }

  MessageDialog _buildConfirmDialog(
    BuildContext context,
    TollRegisterData data,
    VoidCallback onAction,
  ) =>
      MessageDialog.action(
        type: MessageDialogType.info,
        title: context.l10n.toll_confirm_message_title,
        text: '''
          ${context.l10n.toll_message_plate_number_label} ${data.plateNumber}
          ${context.l10n.toll_message_country_label} ${data.country?.name}
          ${context.l10n.toll_message_type_label} ${data.selectedProduct?.description}
          ${context.l10n.toll_message_valid_label} ${data.startDate?.formatDate(context)}
          ${context.l10n.toll_message_validity_label} ${data.selectedProduct?.validityType}
          ${context.l10n.toll_message_price_label} ${data.selectedProduct?.price.formatCurrency(context)}
          ${context.l10n.toll_message_email_label} ${data.email}
          ''',
        centerText: false,
        primaryActionText: context.l10n.action_confirm,
        secondaryActionText: context.l10n.action_cancel,
        primaryAction: onAction,
      );

  MessageBox _buildPaymentMessage() {
    return _tollData?.state != TollStatus.active
        ? MessageBox(
            type: MessageBoxType.info,
            hasIcon: false,
            title: context.l10n
                .toll_info_message_title(_data.value?.plateNumber ?? ''),
            text: context.l10n.toll_info_message_text,
            actionBuilder: (context, color) => [
              FilledButton(
                onPressed: onPayTap,
                style: FilledButton.styleFrom(backgroundColor: color),
                child: Text(
                  context.l10n.action_pay,
                ),
              ),
            ],
          )
        : MessageBox(
            type: MessageBoxType.success,
            hasIcon: false,
            title: context.l10n.toll_success_message_title,
            text: context.l10n
                .toll_success_message_text(_data.value?.email ?? ''),
            textWidget: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LabelWithText(
                  label: context.l10n.toll_message_plate_number_label,
                  text: _data.value?.plateNumber ?? '',
                ),
                LabelWithText(
                  label: context.l10n.toll_message_valid_label,
                  text: _tollData?.from.formatDateAndTime(context) ?? '',
                ),
                LabelWithText(
                  label: context.l10n.toll_message_valid_to_label,
                  text: _tollData?.to.formatDateAndTime(context) ?? '',
                ),
                LabelWithText(
                  label: context.l10n.toll_message_validity_label,
                  text: _data.value?.selectedProduct?.validityType ?? '',
                ),
                LabelWithText(
                  label: context.l10n.toll_message_paid_label,
                  text: _data.value?.selectedProduct?.price
                          .formatCurrency(context) ??
                      '',
                ),
              ],
            ),
          );
  }

  void _onNext() {
    final next = _step.value?.next;

    if (next == null) return;

    _step.accept(next);
  }

  @override
  StateNotifier<TollBuyStep> get step => _step;

  @override
  StateNotifier<TollRegisterData> get data => _data;

  @override
  StateNotifier<List<TollProduct>> get products => _products;

  @override
  StateNotifier<List<TollCountry>> get countries => _countries;

  @override
  StateNotifier<List<VehicleData>> get vehicles => _vehicles;

  @override
  StateNotifier<UserData> get user => _user;

  @override
  StateNotifier<Widget> get message => _message;
}
