import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/toll/model/toll_data.dart';
import 'package:sba/src/repository/toll/model/toll_product.dart';
import 'package:sba/src/repository/toll/model/toll_register_data.dart';
import 'package:sba/src/repository/toll/toll_repository.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class TollBuyModel extends ElementaryModel {
  TollBuyModel({
    required TollRepository tollRepository,
    required VehicleRepository vehicleRepository,
    required UserRepository userRepository,
    super.errorHandler,
  })  : _tollRepository = tollRepository,
        _vehicleRepository = vehicleRepository,
        _userRepository = userRepository;

  final TollRepository _tollRepository;

  final VehicleRepository _vehicleRepository;

  final UserRepository _userRepository;

  Future<List<TollProduct>?> get products =>
      _tollRepository.getProducts().then((e) => e.maybeValue);

  Future<List<TollCountry>?> get countries =>
      _tollRepository.getCountries().then((e) => e.maybeValue);

  Future<List<VehicleData>?> get vehicles =>
      _vehicleRepository.getVehicles();

  Future<UserData?> get user => _userRepository.user.first;

  Future<Result<TollData>> registerToll(TollRegisterData data) =>
      _tollRepository.registerToll(data);

  Future<Result<TollData>> activateToll(TollData data) =>
      _tollRepository.activateToll(data);
}
