import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/toll/buy/model/toll_buy_step.dart';
import 'package:sba/src/feature/toll/buy/toll_buy_wm.dart';
import 'package:sba/src/feature/toll/buy/widget/data_form.dart';
import 'package:sba/src/feature/toll/buy/widget/payment_page.dart';
import 'package:sba/src/feature/toll/buy/widget/payment_type_form.dart';
import 'package:sba/src/feature/toll/buy/widget/toll_type_form.dart';
import 'package:sba/src/feature/toll/buy/widget/vehicle_type_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/animated_linear_indicator.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/state_notifier_listener.dart';

class TollBuyScreen extends ElementaryWidget<ITollBuyWidgetModel> {
  TollBuyScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultTollBuyWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final _controller = PageController();

  @override
  Widget build(ITollBuyWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_toll_buy,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
          const SliverGap(UISpacing.m),
          PinnedHeaderSliver(
            child: Container(
              padding: UISpacing.defaultElementHorizontalPadding,
              color: context.theme.colorScheme.surface,
              child: StateNotifierBuilder(
                listenableState: wm.step,
                builder: (context, data) => AnimatedLinearIndicator(
                  progress: data?.progress ?? 0,
                ),
              ),
            ),
          ),
        ],
        body: StateNotifierListener(
          listenableState: wm.step,
          onChange: (step) {
            if (!_controller.hasClients) return;

            _controller.animateToPage(
              step?.index ?? 0,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
          child: PageView.builder(
            physics: const NeverScrollableScrollPhysics(),
            controller: _controller,
            itemCount: TollBuyStep.values.length,
            itemBuilder: (context, i) {
              final step = TollBuyStep.values[i];

              final page = switch (step) {
                TollBuyStep.vehicleType => StateNotifierBuilder(
                    listenableState: wm.data,
                    builder: (context, data) => VehicleTypeForm(
                      initialData: data,
                      onSubmitClick: wm.onCategoryTap,
                    ),
                  ),
                TollBuyStep.type => DoubleSourceBuilder(
                    firstSource: wm.products,
                    secondSource: wm.data,
                    builder: (context, products, data) => TollTypeForm(
                      products: products,
                      initialData: data,
                      onSubmitClick: wm.onProductTap,
                      onBack: wm.onBack,
                    ),
                  ),
                TollBuyStep.data => MultiListenerRebuilder(
                    listenableList: [
                      wm.countries,
                      wm.vehicles,
                      wm.data,
                      wm.user,
                    ],
                    builder: (context) => DataForm(
                      countries: wm.countries.value,
                      vehicles: wm.vehicles.value,
                      initialData: wm.data.value,
                      user: wm.user.value,
                      onSubmitClick: wm.onDataTap,
                      onBack: wm.onBack,
                    ),
                  ),
                TollBuyStep.paymentType => PaymentTypeForm(
                    onSubmitClick: wm.onPaymentTap,
                    onBack: wm.onBack,
                  ),
                TollBuyStep.payment => StateNotifierBuilder(
                    listenableState: wm.message,
                    builder: (context, data) => PaymentPage(
                      message: data,
                    ),
                  ),
              };

              return SingleChildScrollView(
                padding: UISpacing.defaultScreenPadding,
                child: page,
              );
            },
          ),
        ),
      ),
    );
  }
}
