import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/toll/model/toll_product.dart';
import 'package:sba/src/repository/toll/model/toll_register_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_option_picker.dart';
import 'package:sba/src/ui/widget/list_tile_box.dart';
import 'package:sba/src/ui/widget/value_listener.dart';

class TollTypeForm extends StatelessWidget {
  TollTypeForm({
    required this.onSubmitClick,
    required this.onBack,
    super.key,
    this.products,
    this.initialData,
  });

  final ValueChanged<TollProduct> onSubmitClick;

  final List<TollProduct>? products;

  final TollRegisterData? initialData;
  final VoidCallback onBack;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final ValueNotifier<bool> _buttonEnabled = ValueNotifier(false);
  static const ({String type}) _keys = (
    type: 'type',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      onChanged: () {
        _buttonEnabled.value = _formKey.currentState?.isValid ?? false;
      },
      child: ValueListener(
        value: initialData,
        onChange: (_) {
          _buttonEnabled.value = _formKey.currentState?.isValid ?? false;
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              context.l10n.toll_type_label,
              style: context.textTheme.headlineMedium,
            ),
            const Gap(UISpacing.l),
            FormBuilderOptionPicker<TollProduct>(
              name: _keys.type,
              initialValue: initialData?.selectedProduct,
              data:
                  products
                      ?.where(
                        (e) =>
                            e.vehicleType == initialData?.selectedCategory?.key,
                      )
                      .toList() ??
                  [],
              elementBuilder: (type, selected) => ListTileBox(
                title: type.validityType,
                trailing: Text(
                  type.price.formatCurrency(context),
                  style: UITypography.tileTrailing.copyWith(
                    color: context.theme.colorScheme.primary,
                  ),
                ),
                selected: selected,
              ),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
            const Gap(UISpacing.xl),
            ValueListenableBuilder(
              valueListenable: _buttonEnabled,
              builder: (context, enabled, _) {
                return FilledButton(
                  onPressed: enabled ? _onSubmit : null,
                  child: Text(context.l10n.action_continue),
                );
              },
            ),
            const Gap(UISpacing.m),
            FilledButton(
              onPressed: onBack,
              child: Text(context.l10n.action_back),
            ),
          ],
        ),
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmitClick(
        _formKey.currentState!.value[_keys.type] as TollProduct,
      );
    }
  }
}
