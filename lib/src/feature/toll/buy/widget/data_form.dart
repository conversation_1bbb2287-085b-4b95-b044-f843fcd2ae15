import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/toll/buy/toll_buy_wm.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/toll/model/toll_register_data.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_vehicle_picker.dart';
import 'package:sba/src/ui/widget/form_builder_visibility.dart';

class DataForm extends StatelessWidget {
  DataForm({
    required this.onSubmitClick,
    required this.onBack,
    super.key,
    this.countries,
    this.initialData,
    this.vehicles,
    this.user,
  });

  final ValueChanged<TollDataRecord> onSubmitClick;
  final VoidCallback onBack;
  final List<TollCountry>? countries;
  final List<VehicleData>? vehicles;
  final TollRegisterData? initialData;
  final UserData? user;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  final ValueNotifier<bool> _showPlateNumber = ValueNotifier(false);

  static const ({
    String country,
    String date,
    String email,
    String plateNumber,
    String vehicle,
  })
  _keys = (
    date: 'date',
    country: 'country',
    vehicle: 'vehicle',
    plateNumber: 'plateNumber',
    email: 'email',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.l10n.toll_period_subtitle,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          Text(
            context.l10n.toll_period_hint,
            style: context.textTheme.bodySmall,
          ),
          const Gap(UISpacing.l),
          FormBuilderDateTimePicker(
            name: _keys.date,
            initialValue: initialData?.startDate,
            locale: context.locale,
            textInputAction: TextInputAction.next,
            firstDate: DateTime.now(),
            lastDate: DateTime.now().add(
              const Duration(days: 29),
            ),
            inputType: InputType.date,
            decoration: InputDecoration(
              labelText: context.l10n.form_start_date,
              hintText: context.l10n.form_start_date_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xxl),
          Text(
            context.l10n.toll_vehicle_data_subtitle,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<TollCountry>(
            name: _keys.country,
            initialValue: initialData?.country,
            decoration: InputDecoration(
              labelText: context.l10n.form_country_vehicle,
              hintText: context.l10n.form_country_hint,
            ),
            items:
                countries
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.name),
                      ),
                    )
                    .toList() ??
                [],
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderVehiclePicker(
            name: _keys.vehicle,
            plateNumber: initialData?.plateNumber,
            vehicles: vehicles,
            onOtherSelected: (e) => _showPlateNumber.value = e,
          ),
          FormBuilderVisibility(
            visible: _showPlateNumber,
            padding: const EdgeInsets.only(top: UISpacing.l),
            child: FormBuilderTextField(
              name: _keys.plateNumber,
              decoration: InputDecoration(
                labelText: context.l10n.form_plate_number,
                hintText: context.l10n.form_plate_number_hint,
              ),
              textInputAction: TextInputAction.next,
              validator: FormBuilderValidators.licensePlate(
                errorText: context.l10n.form_validation_license_plate,
              ),
            ),
          ),
          const Gap(UISpacing.xxl),
          Text(
            context.l10n.toll_email_subtitle,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          Text(
            context.l10n.toll_email_hint,
            style: context.textTheme.bodySmall,
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.email,
            initialValue: initialData?.email ?? user?.email,
            keyboardType: TextInputType.emailAddress,
            autocorrect: false,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_email,
              hintText: context.l10n.form_email_hint,
            ),
            validator: FormBuilderValidators.email(
              errorText: context.l10n.form_validation_email,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_continue),
          ),
          const Gap(UISpacing.m),
          FilledButton(
            onPressed: onBack,
            child: Text(context.l10n.action_back),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmitClick(
        (
          country: _formKey.currentState!.value[_keys.country] as TollCountry,
          date: _formKey.currentState!.value[_keys.date] as DateTime,
          plateNumber:
              (_formKey.currentState!.value[_keys.vehicle] as VehicleData?)
                  ?.plateNumber ??
              _formKey.currentState!.value[_keys.plateNumber] as String,
          email: _formKey.currentState!.value[_keys.email] as String,
        ),
      );
    }
  }
}
