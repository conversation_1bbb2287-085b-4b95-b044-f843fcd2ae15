import 'package:flutter/material.dart';
import 'package:sba/src/common/extension/iterable_extension.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/toll/model/toll_check_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/label_box.dart';

class TollCheckResult extends StatelessWidget {
  const TollCheckResult({required this.data, super.key});

  final TollCheckData data;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _TollCheckitem(
          title: context.l10n.toll_id_label,
          text: data.id,
        ),
        _TollCheckitem(
          title: context.l10n.toll_type_label,
          text: data.type,
        ),
        _TollCheckitem(
          title: context.l10n.toll_start_date_label,
          text: data.start,
        ),
        _TollCheckitem(
          title: context.l10n.toll_valid_date_label,
          text: data.end,
        ),
        _TollCheckitem(
          title: context.l10n.toll_amount_label,
          text: data.amount.formatCurrency(context, symbol: data.currency),
        ),
        _TollCheckitem(
          title: context.l10n.toll_status_label,
          child: data.valid
              ? LabelBox(
                  label: context.l10n.toll_status_active,
                  type: LabelBoxType.success,
                )
              : LabelBox(
                  label: context.l10n.toll_status_inactive,
                  type: LabelBoxType.error,
                ),
        ),
      ].intersperseWidget(const Divider()).toList(),
    );
  }
}

class _TollCheckitem extends StatelessWidget {
  const _TollCheckitem({required this.title, this.text, this.child});

  final String title;
  final String? text;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: UISpacing.m),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        spacing: UISpacing.s,
        children: [
          Text(
            title,
            style: context.textTheme.bodyMedium,
            maxLines: 1,
          ),
          if (text != null)
            Text(
              text!,
              style: context.textTheme.bodyMedium
                  ?.copyWith(fontWeight: UIFontWeight.semiBold),
              maxLines: 1,
            ),
          if (child != null) child!,
        ],
      ),
    );
  }
}
