import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/toll/check/toll_check_wm.dart';
import 'package:sba/src/feature/toll/check/widget/toll_check_form.dart';
import 'package:sba/src/feature/toll/check/widget/toll_check_result.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class TollCheckScreen extends ElementaryWidget<ITollCheckWidgetModel> {
  const TollCheckScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultTollCheckWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(ITollCheckWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_toll_check,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: Builder(
          builder: (context) {
            return SingleChildScrollView(
              padding: UISpacing.defaultScreenPadding,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DoubleSourceBuilder(
                    firstSource: wm.countries,
                    secondSource: wm.vehicles,
                    builder: (context, countries, vehicles) => TollCheckForm(
                      countries: countries,
                      vehicles: vehicles,
                      onSubmit: wm.onCheck,
                    ),
                  ),
                  const Gap(UISpacing.xxl),
                  EntityStateNotifierBuilder(
                    listenableEntityState: wm.checkState,
                    loadingBuilder: (_, _) => const SizedBox.shrink(),
                    builder: (context, data) => data == null || !data.valid
                        ? Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: UISpacing.xxl,
                            ),
                            child: Text(
                              context.l10n.empty_toll_without_vehicle,
                              style: context.textTheme.bodyMedium,
                            ),
                          )
                        : TollCheckResult(data: data),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
