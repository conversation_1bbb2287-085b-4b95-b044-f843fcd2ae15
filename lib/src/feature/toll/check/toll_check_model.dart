import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/toll/model/toll_check_data.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/toll/toll_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

typedef TollCheckRecord = ({TollCountry country, String plateNumber});

class TollCheckModel extends ElementaryModel {
  TollCheckModel({
    required TollRepository tollRepository,
    required VehicleRepository vehicleRepository,
    super.errorHandler,
  })  : _tollRepository = tollRepository,
        _vehicleRepository = vehicleRepository;

  final TollRepository _tollRepository;
  final VehicleRepository _vehicleRepository;

  Future<List<TollCountry>?> get countries =>
      _tollRepository.getCountries().then((e) => e.maybeValue);

  Future<List<VehicleData>?> get vehicles =>
      _vehicleRepository.getVehicles();

  Future<Result<TollCheckData?>> tollCheck(TollCheckRecord record) =>
      _tollRepository.checkToll(
        country: record.country,
        plateNumber: record.plateNumber,
      );
}
