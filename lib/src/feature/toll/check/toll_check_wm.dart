import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/toll/check/toll_check_model.dart';
import 'package:sba/src/feature/toll/check/toll_check_screen.dart';
import 'package:sba/src/repository/toll/model/toll_check_data.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';

abstract interface class ITollCheckWidgetModel implements IWidgetModel {
  void onCheck(TollCheckRecord record);

  StateNotifier<List<TollCountry>> get countries;
  StateNotifier<List<VehicleData>> get vehicles;

  EntityStateNotifier<TollCheckData?> get checkState;
}

TollCheckWidgetModel defaultTollCheckWidgetModelFactory(BuildContext context) {
  return TollCheckWidgetModel(
    TollCheckModel(
      tollRepository: get(),
      vehicleRepository: get(),
      errorHandler: get(),
    ),
  );
}

class TollCheckWidgetModel extends WidgetModel<TollCheckScreen, TollCheckModel>
    implements ITollCheckWidgetModel {
  TollCheckWidgetModel(super.model);

  final _checkState = EntityStateNotifier<TollCheckData?>(
    EntityState.loading(),
  );

  final _countries = StateNotifier<List<TollCountry>>();
  final _vehicles = StateNotifier<List<VehicleData>>();

  @override
  void initWidgetModel() {
    _loadData();
    super.initWidgetModel();
  }

  void _loadData() async {
    _countries.accept(await model.countries);
    _vehicles.accept(await model.vehicles);
  }

  @override
  void onCheck(TollCheckRecord record) async {
    _checkState.loading();
    await context.showLoadingDialog();
    final result = await model.tollCheck(record);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      return;
    }

    final data = result.maybeValue;

    _checkState.content(data);
  }

  @override
  StateNotifier<List<TollCountry>> get countries => _countries;

  @override
  StateNotifier<List<VehicleData>> get vehicles => _vehicles;

  @override
  EntityStateNotifier<TollCheckData?> get checkState => _checkState;
}
