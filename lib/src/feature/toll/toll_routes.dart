part of '../main/main_router.dart';

class TollRoute extends GoRouteData with _$TollRoute {
  const TollRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const TollScreen();
  }
}

class CheckTollRoute extends GoRouteData with _$CheckTollRoute {
  const CheckTollRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const TollCheckScreen();
  }
}

class BuyTollRoute extends GoRouteData with _$BuyTollRoute {
  const BuyTollRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return TollBuyScreen();
  }
}
