import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/external_launcher/external_launcher.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/road_assistance/view/road_assistance_model.dart';
import 'package:sba/src/feature/road_assistance/view/road_assistance_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';

abstract interface class IRoadAssistanceWidgetModel implements IWidgetModel {
  StateNotifier<List<RoadAssistanceRequest>?> get requests;

  Future<void> refresh();

  void onDeleteRequestTap(RoadAssistanceRequest data);

  void onTrackTap(RoadAssistanceRequest data);

  void onRequestTap();

  void onRequestForOtherTap();
}

RoadAssistanceWidgetModel defaultRoadAssistanceWidgetModelFactory(
  BuildContext context,
) {
  return RoadAssistanceWidgetModel(
    RoadAssistanceModel(
      errorHandler: get(),
      repository: get(),
    ),
  );
}

class RoadAssistanceWidgetModel
    extends WidgetModel<RoadAssistanceScreen, RoadAssistanceModel>
    implements IRoadAssistanceWidgetModel {
  RoadAssistanceWidgetModel(super.model);

  final _sub = CompositeSubscription();
  final _requests = StateNotifier<List<RoadAssistanceRequest>?>();

  @override
  void initWidgetModel() {
    _sub.add(
      model.requests
          .asyncMap((result) async {
            if (result is Failure) {
              await context.showGeneralErrorDialog(failure: result as Failure);
            }
            return result;
          })
          .map((e) => e.maybeValue)
          .map((e) => e?.sorted((a, b) => b.id!.compareTo(a.id!)))
          .listen(_requests.accept),
    );
    super.initWidgetModel();
  }

  @override
  void dispose() {
    _sub.dispose();
    super.dispose();
  }

  @override
  Future<void> refresh() => model.refresh();

  @override
  void onRequestForOtherTap() {
    const RequestRoadAssistanceOtherRoute().go(context);
  }

  @override
  void onRequestTap() {
    const RequestRoadAssistanceRoute().go(context);
  }

  @override
  void onDeleteRequestTap(RoadAssistanceRequest data) {
    Future<void> cancel(RoadAssistanceRequest data) async {
      await context.showLoadingDialog();
      final result = await model.cancelRequest(data);
      context.hideLoadingDialog();

      if (result is Failure) {
        await context.showGeneralErrorDialog(failure: result);
      }
    }

    context.showMessageDialog(
      builder: (context) => MessageDialog.action(
        type: MessageDialogType.error,
        title: context.l10n.road_assistance_cancel_message_title,
        text: context.l10n.road_assistance_cancel_message_text(
          data.plateNumber,
        ),
        primaryActionText: context.l10n.action_yes,
        secondaryActionText: context.l10n.action_cancel,
        primaryAction: () => cancel(data),
      ),
    );
  }

  @override
  void onTrackTap(RoadAssistanceRequest data) => ExternalLauncher.uri(
    data.smartViewUrl ?? '',
  );

  @override
  StateNotifier<List<RoadAssistanceRequest>?> get requests => _requests;
}
