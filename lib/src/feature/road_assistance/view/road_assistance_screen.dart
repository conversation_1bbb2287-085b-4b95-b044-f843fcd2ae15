import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/road_assistance/view/road_assistance_wm.dart';
import 'package:sba/src/feature/road_assistance/view/widget/request_tile.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/navigation_box.dart';
import 'package:sba/src/ui/widget/sliver_subtitle.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

class RoadAssistanceScreen
    extends ElementaryWidget<IRoadAssistanceWidgetModel> {
  const RoadAssistanceScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultRoadAssistanceWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IRoadAssistanceWidgetModel wm) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: wm.refresh,
        notificationPredicate: (n) => n.depth == 1,
        child: NestedScrollView(
          headerSliverBuilder: (context, _) => [
            SliverTitle(
              text: context.l10n.navigation_road_assistance,
              padding: UISpacing.defaultScreenPadding,
            ),
          ],
          body: Builder(
            builder: (context) {
              return CustomScrollView(
                slivers: [
                  SliverSubtitle(
                    text: context.l10n.road_assistance_services_section,
                    padding: UISpacing.defaultElementHorizontalPadding,
                  ),
                  const SliverGap(UISpacing.l),
                  SliverPadding(
                    padding: UISpacing.defaultElementHorizontalPadding,
                    sliver: SliverGrid.count(
                      crossAxisCount: 2,
                      mainAxisSpacing: UISpacing.s,
                      crossAxisSpacing: UISpacing.s,
                      childAspectRatio: 17 / 10,
                      children: [
                        NavigationBox(
                          icon: Assets.icons.person.svg(),
                          text: context.l10n.navigation_road_assistance_request,
                          onTap: wm.onRequestTap,
                        ),
                        NavigationBox(
                          icon: Assets.icons.personAdd.svg(),
                          text: context
                              .l10n
                              .navigation_road_assistance_request_other,
                          onTap: wm.onRequestForOtherTap,
                        ),
                      ],
                    ),
                  ),
                  const SliverGap(UISpacing.xxl),
                  StateNotifierBuilder(
                    listenableState: wm.requests,
                    builder: (context, data) => SliverVisibility(
                      visible: data?.isNotEmpty ?? false,
                      sliver: SliverSubtitle(
                        text: context.l10n.road_assistance_requests_section,
                        padding: UISpacing.defaultElementHorizontalPadding,
                      ),
                    ),
                  ),
                  const SliverGap(UISpacing.l),
                  StateNotifierBuilder(
                    listenableState: wm.requests,
                    builder: (context, data) => SmartListView.sliver(
                      loading: data == null,
                      data: data,
                      skeletonData: List.filled(
                        2,
                        RoadAssistanceRequest.fake(),
                      ),
                      separator: const Gap(UISpacing.m),
                      padding: UISpacing.defaultElementPaddingWithoutTop,
                      builder: (context, item) => RequestTile(
                        data: item,
                        onDeleteTap: () => wm.onDeleteRequestTap(item),
                        onTrackTap: () => wm.onTrackTap(item),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
