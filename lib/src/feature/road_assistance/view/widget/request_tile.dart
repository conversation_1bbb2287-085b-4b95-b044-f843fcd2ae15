import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/repository/road_assistance/model/types/road_assistance_request_status_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class RequestTile extends StatelessWidget {
  const RequestTile({
    required this.data,
    this.onDeleteTap,
    this.onTrackTap,
    super.key,
  });

  final RoadAssistanceRequest data;
  final VoidCallback? onDeleteTap;
  final VoidCallback? onTrackTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(UISpacing.m),
      decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            spacing: UISpacing.m,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Skeleton.replace(
                width: 40,
                height: 40,
                child: Assets.icons.carCrash.svg(
                  colorFilter: ColorFilter.mode(
                    context.theme.colorScheme.primary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      context.l10n
                          .road_assistance_tile_request_number(data.id ?? 0),
                      style: context.textTheme.headlineMedium,
                    ),
                    const Gap(UISpacing.m),
                    LabelWithText(
                      label: context.l10n.road_assistance_tile_vehicle,
                      text: data.plateNumber,
                    ),
                    const Gap(UISpacing.s),
                    if (data.reason != null)
                      LabelWithText(
                        label: context.l10n.road_assistance_tile_reason,
                        text: data.reason!.reason,
                      ),
                    if (data.reason != null) const Gap(UISpacing.s),
                    if (data.driverName != null)
                      LabelWithText(
                        label: context.l10n.road_assistance_tile_driver,
                        text: data.driverName!,
                      ),
                    if (data.driverName != null) const Gap(UISpacing.s),
                    if (data.created != null)
                      LabelWithText(
                        label: context.l10n.road_assistance_tile_submitDate,
                        text: data.created!.formatDateAndTime(context),
                      ),
                    if (data.created != null) const Gap(UISpacing.s),
                    if (data.estimatedArrivalTime != null)
                      LabelWithText(
                        label: context.l10n.road_assistance_tile_arrivalDate,
                        text: data.estimatedArrivalTime!.formatTime(context),
                      ),
                    if (data.estimatedArrivalTime != null)
                      const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.road_assistance_tile_status,
                      text: data.status?.localizedName(context) ?? '',
                      textColor: data.status?.color,
                    ),
                  ],
                ),
              ),
              Skeleton.leaf(
                child: Visibility(
                  visible: data.status?.isActive ?? false,
                  child: IconButton(
                    onPressed: onDeleteTap,
                    icon: Assets.icons.delete.svg(),
                  ),
                ),
              ),
            ],
          ),
          if (data.smartViewUrl != null) const Gap(UISpacing.ll),
          if (data.smartViewUrl != null)
            FilledButton(
              onPressed: onTrackTap,
              child: Text(context.l10n.action_track),
            ),
        ],
      ),
    );
  }
}
