import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/road_assistance/request_other/request_road_assistance_for_other_wm.dart';
import 'package:sba/src/feature/road_assistance/request_other/widget/request_assistance_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class RequestRoadAssistanceForOtherScreen
    extends ElementaryWidget<IRequestRoadAssistanceForOtherWidgetModel> {
  const RequestRoadAssistanceForOtherScreen({
    Key? key,
    WidgetModelFactory wmFactory =
        defaultRequestRoadAssistanceForOtherWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IRequestRoadAssistanceForOtherWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_road_assistance_request_other,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: DoubleSourceBuilder(
            firstSource: wm.brands,
            secondSource: wm.reasons,
            builder: (context, brands, reasons) => RequestAssistanceForm(
              brands: brands,
              reasons: reasons,
              onSubmitClick: wm.onSubmit,
            ),
          ),
        ),
      ),
    );
  }
}
