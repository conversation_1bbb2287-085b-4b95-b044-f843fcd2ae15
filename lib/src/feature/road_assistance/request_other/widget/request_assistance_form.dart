import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/road_assistance_reason.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/repository/road_assistance/model/types/car_drive_type.dart';
import 'package:sba/src/repository/road_assistance/model/types/car_transmission_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class RequestAssistanceForm extends StatelessWidget {
  RequestAssistanceForm({
    required this.onSubmitClick,
    super.key,
    this.brands,
    this.reasons,
  });

  final ValueChanged<RoadAssistanceRequest> onSubmitClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final List<CarBrand>? brands;
  final List<RoadAssistanceReason>? reasons;
  final ValueNotifier<CarBrand?> _selectedBrand = ValueNotifier(null);

  static const ({
    String brand,
    String drive,
    String model,
    String name,
    String phone,
    String plate,
    String reason,
    String transimission,
  })
  _keys = (
    name: 'name',
    phone: 'phone',
    reason: 'reason',
    brand: 'brand',
    model: 'model',
    plate: 'plate',
    transimission: 'transmission',
    drive: 'drive',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderTextField(
            name: _keys.name,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_driver_name,
              hintText: context.l10n.form_driver_name_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.phone,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_driver_phone,
              hintText: context.l10n.form_driver_phone_hint,
            ),
            validator: FormBuilderValidators.phoneNumber(
              errorText: context.l10n.form_validation_phone,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<RoadAssistanceReason>(
            name: _keys.reason,
            decoration: InputDecoration(
              labelText: context.l10n.form_failure_type,
              hintText: context.l10n.form_failure_type_hint,
            ),
            items:
                reasons
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.reason),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<CarBrand>(
            name: _keys.brand,
            decoration: InputDecoration(
              labelText: context.l10n.form_brand,
              hintText: context.l10n.form_brand_hint,
            ),
            items:
                brands
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.name),
                      ),
                    )
                    .toList() ??
                List.empty(),
            onChanged: (d) => _selectedBrand.value = d,
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          ValueListenableBuilder(
            valueListenable: _selectedBrand,
            builder: (context, brand, _) => FormBuilderDropdown<CarModel>(
              name: _keys.model,
              decoration: InputDecoration(
                labelText: context.l10n.form_model,
                hintText: context.l10n.form_model_hint,
              ),
              items:
                  brand?.models
                      .map(
                        (it) => DropdownMenuItem(
                          value: it,
                          child: Text(it.name),
                        ),
                      )
                      .toList() ??
                  List.empty(),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.plate,
            decoration: InputDecoration(
              labelText: context.l10n.form_plate_number,
              hintText: context.l10n.form_plate_number_hint,
            ),
            textInputAction: TextInputAction.next,
            validator: FormBuilderValidators.licensePlate(
              errorText: context.l10n.form_validation_license_plate,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<CarTransmissionType>(
            name: _keys.transimission,
            decoration: InputDecoration(
              labelText: context.l10n.form_transmission,
              hintText: context.l10n.form_transmission_hint,
            ),
            items: CarTransmissionType.valid
                .map(
                  (it) => DropdownMenuItem(
                    value: it,
                    child: Text(it.localizedName(context)),
                  ),
                )
                .toList(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<CarDriveType>(
            name: _keys.drive,
            decoration: InputDecoration(
              labelText: context.l10n.form_drive,
              hintText: context.l10n.form_drive_hint,
            ),
            items: CarDriveType.valid
                .map(
                  (it) => DropdownMenuItem(
                    value: it,
                    child: Text(it.localizedName(context)),
                  ),
                )
                .toList(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          Text(
            context.l10n.road_assistance_request_other_caption,
            style: context.textTheme.bodySmall,
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_send_request),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmitClick(
        RoadAssistanceRequest(
          driverName: _formKey.currentState!.value[_keys.name] as String,
          driverPhone: _formKey.currentState!.value[_keys.phone] as String,
          reason:
              _formKey.currentState!.value[_keys.reason]
                  as RoadAssistanceReason,
          carBrand: _formKey.currentState!.value[_keys.brand] as CarBrand,
          carModel: _formKey.currentState!.value[_keys.model] as CarModel,
          plateNumber: _formKey.currentState!.value[_keys.plate] as String,
          carTransmissionType:
              _formKey.currentState!.value[_keys.transimission]
                  as CarTransmissionType,
          carDriveType:
              _formKey.currentState!.value[_keys.drive] as CarDriveType,
        ),
      );
    }
  }
}
