import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/road_assistance_reason.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/repository/road_assistance/road_assistance_repository.dart';

class RequestRoadAssistanceForOtherModel extends ElementaryModel {
  RequestRoadAssistanceForOtherModel({
    required GeneralRepository generalRepository,
    required RoadAssistanceRepository repository,
    super.errorHandler,
  })  : _generalRepository = generalRepository,
        _repository = repository;

  final GeneralRepository _generalRepository;
  final RoadAssistanceRepository _repository;

  Future<List<CarBrand>?> get brands =>
      _generalRepository.getCarBrands().then((e) => e.maybeValue);

  Future<List<RoadAssistanceReason>?> get reasons =>
      _generalRepository.getRoadAssistanceReasons().then((e) => e.maybeValue);

  Future<Result<void>> createRequest(RoadAssistanceRequest data) =>
      _repository.addRequestForOtherDriver(data);
}
