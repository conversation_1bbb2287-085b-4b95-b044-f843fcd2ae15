import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/road_assistance/request/request_road_assistance_wm.dart';
import 'package:sba/src/feature/road_assistance/request/widget/request_assistance_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/open_street_map.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class RequestRoadAssistanceScreen
    extends ElementaryWidget<IRequestRoadAssistanceWidgetModel> {
  const RequestRoadAssistanceScreen({
    Key? key,
    WidgetModelFactory wmFactory =
        defaultRequestRoadAssistanceWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IRequestRoadAssistanceWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_road_assistance_request,
            padding: UISpacing.defaultScreenPadding,
          ),
        ],
        body: Builder(
          builder: (context) {
            return CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: UISpacing.defaultElementHorizontalPadding,
                    child: StateNotifierBuilder(
                      listenableState: wm.preferredLocation,
                      builder: (context, data) => OpenStreetMap(
                        markers: data != null
                            ? [MarkerData(data: data, coordinates: data)]
                            : null,
                        onMarkerTap: (_) {},
                        animateMapBoundsToFitMarkers: data != null,
                        showCurrentLocation: data == null,
                        onCurrentLocationUpdate: wm.onCurrentLocationChange,
                        enableInteractions: false,
                      ),
                    ),
                  ),
                ),
                const SliverGap(UISpacing.l),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: UISpacing.defaultElementHorizontalPadding,
                    child: FilledButton(
                      onPressed: wm.onPickOtherLocation,
                      child: Text(context.l10n.action_choose_coordinates),
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: UISpacing.defaultScreenPadding,
                    child: MultiListenerRebuilder(
                      listenableList: [
                        wm.user,
                        wm.brands,
                        wm.reasons,
                        wm.vehicles,
                      ],
                      builder: (_) => RequestAssistanceForm(
                        userData: wm.user.value,
                        brands: wm.brands.value,
                        vehicles: wm.vehicles.value,
                        reasons: wm.reasons.value,
                        onSubmitClick: wm.onSubmit,
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
