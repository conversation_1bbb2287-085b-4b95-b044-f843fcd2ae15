import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/road_assistance_reason.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/repository/road_assistance/road_assistance_repository.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class RequestRoadAssistanceModel extends ElementaryModel {
  RequestRoadAssistanceModel({
    required GeneralRepository generalRepository,
    required UserRepository userRepository,
    required VehicleRepository vehicleRepository,
    required RoadAssistanceRepository repository,
    super.errorHandler,
  })  : _generalRepository = generalRepository,
        _userRepository = userRepository,
        _vehicleRepository = vehicleRepository,
        _repository = repository;

  final GeneralRepository _generalRepository;
  final UserRepository _userRepository;
  final VehicleRepository _vehicleRepository;
  final RoadAssistanceRepository _repository;

  Future<List<CarBrand>?> get brands =>
      _generalRepository.getCarBrands().then((e) => e.maybeValue);

  Future<List<RoadAssistanceReason>?> get reasons =>
      _generalRepository.getRoadAssistanceReasons().then((e) => e.maybeValue);

  Future<List<VehicleData>?> get vehicles => _vehicleRepository.getVehicles();

  Future<UserData?> get user => _userRepository.getUser();

  Future<Result<void>> createRequest(RoadAssistanceRequest data) =>
      _repository.addRequestForCurrentUser(data);
}
