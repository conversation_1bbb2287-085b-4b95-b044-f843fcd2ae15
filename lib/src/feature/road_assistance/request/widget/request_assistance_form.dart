import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/road_assistance_reason.dart';
import 'package:sba/src/repository/road_assistance/model/road_assistance_request.dart';
import 'package:sba/src/repository/road_assistance/model/types/car_drive_type.dart';
import 'package:sba/src/repository/road_assistance/model/types/car_transmission_type.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_multi_visibility.dart';
import 'package:sba/src/ui/widget/form_builder_vehicle_picker.dart';

class RequestAssistanceForm extends StatelessWidget {
  RequestAssistanceForm({
    required this.onSubmitClick,
    super.key,
    this.userData,
    this.vehicles,
    this.reasons,
    this.brands,
  });

  final ValueChanged<RoadAssistanceRequest> onSubmitClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final ValueNotifier<bool> _showOtherVehicle = ValueNotifier(false);
  final ValueNotifier<CarBrand?> _selectedBrand = ValueNotifier(null);
  final UserData? userData;
  final List<VehicleData>? vehicles;
  final List<RoadAssistanceReason>? reasons;
  final List<CarBrand>? brands;

  static const ({String brand, String drive, String driverName, String driverPhone, String info, String model, String plate, String reason, String transimission, String vehicle}) _keys = (
    reason: 'reason',
    vehicle: 'vehicle',
    brand: 'brand',
    model: 'model',
    plate: 'plate',
    transimission: 'transmission',
    drive: 'drive',
    info: 'info',
    driverName: 'driverName',
    driverPhone: 'driverPhone',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.l10n.road_assistance_vehicle_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<RoadAssistanceReason>(
            name: _keys.reason,
            decoration: InputDecoration(
              labelText: context.l10n.form_failure_type,
              hintText: context.l10n.form_failure_type_hint,
            ),
            items: reasons
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.reason),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderVehiclePicker(
            name: _keys.vehicle,
            vehicles: vehicles,
            onOtherSelected: (e) => _showOtherVehicle.value = e,
          ),
          FormBuilderMultiVisibility(
            visible: _showOtherVehicle,
            padding: const EdgeInsets.only(top: UISpacing.l),
            children: [
              FormBuilderDropdown<CarBrand>(
                name: _keys.brand,
                decoration: InputDecoration(
                  labelText: context.l10n.form_brand,
                  hintText: context.l10n.form_brand_hint,
                ),
                items: brands
                        ?.map(
                          (it) => DropdownMenuItem(
                            value: it,
                            child: Text(it.name),
                          ),
                        )
                        .toList() ??
                    List.empty(),
                onChanged: (d) => _selectedBrand.value = d,
                validator: FormBuilderValidators.required(
                  errorText: context.l10n.form_validation_required,
                ),
              ),
              const Gap(UISpacing.l),
              ValueListenableBuilder(
                valueListenable: _selectedBrand,
                builder: (context, brand, _) => FormBuilderDropdown<CarModel>(
                  name: _keys.model,
                  decoration: InputDecoration(
                    labelText: context.l10n.form_model,
                    hintText: context.l10n.form_model_hint,
                  ),
                  items: brand?.models
                          .map(
                            (it) => DropdownMenuItem(
                              value: it,
                              child: Text(it.name),
                            ),
                          )
                          .toList() ??
                      List.empty(),
                  validator: FormBuilderValidators.required(
                    errorText: context.l10n.form_validation_required,
                  ),
                ),
              ),
              const Gap(UISpacing.l),
              FormBuilderTextField(
                name: _keys.plate,
                decoration: InputDecoration(
                  labelText: context.l10n.form_plate_number,
                  hintText: context.l10n.form_plate_number_hint,
                ),
                textInputAction: TextInputAction.next,
                validator: FormBuilderValidators.licensePlate(
                  errorText: context.l10n.form_validation_license_plate,
                ),
              ),
            ],
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<CarTransmissionType>(
            name: _keys.transimission,
            decoration: InputDecoration(
              labelText: context.l10n.form_transmission,
              hintText: context.l10n.form_transmission_hint,
            ),
            items: CarTransmissionType.valid
                .map(
                  (it) => DropdownMenuItem(
                    value: it,
                    child: Text(it.localizedName(context)),
                  ),
                )
                .toList(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<CarDriveType>(
            name: _keys.drive,
            decoration: InputDecoration(
              labelText: context.l10n.form_drive,
              hintText: context.l10n.form_drive_hint,
            ),
            items: CarDriveType.valid
                .map(
                  (it) => DropdownMenuItem(
                    value: it,
                    child: Text(it.localizedName(context)),
                  ),
                )
                .toList(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.info,
            maxLines: null,
            minLines: 4,
            keyboardType: TextInputType.multiline,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_info,
              hintText: context.l10n.form_info_hint,
            ),
          ),
          const Gap(UISpacing.xxl),
          Text(
            context.l10n.road_assistance_driver_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.driverName,
            initialValue: userData?.unsafeFullName,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_driver_name,
              hintText: context.l10n.form_driver_name_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.driverPhone,
            initialValue: userData?.phone,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_driver_phone,
              hintText: context.l10n.form_driver_phone_hint,
            ),
            validator: FormBuilderValidators.phoneNumber(
              errorText: context.l10n.form_validation_phone,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_send_request),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      final vehicle =
          _formKey.currentState!.value[_keys.vehicle] as VehicleData?;

      onSubmitClick(
        RoadAssistanceRequest(
          reason: _formKey.currentState!.value[_keys.reason]
              as RoadAssistanceReason,
          carBrand: vehicle?.brand ??
              _formKey.currentState!.value[_keys.brand] as CarBrand,
          carModel: vehicle?.model ??
              _formKey.currentState!.value[_keys.model] as CarModel,
          plateNumber: vehicle?.plateNumber ??
              _formKey.currentState!.value[_keys.plate] as String,
          carTransmissionType: _formKey.currentState!.value[_keys.transimission]
              as CarTransmissionType,
          carDriveType:
              _formKey.currentState!.value[_keys.drive] as CarDriveType,
          driverName: _formKey.currentState!.value[_keys.driverName] as String,
          driverPhone:
              _formKey.currentState!.value[_keys.driverPhone] as String,
          note: _formKey.currentState!.value[_keys.info] as String?,
        ),
      );
    }
  }
}
