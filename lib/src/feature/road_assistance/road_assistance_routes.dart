part of '../main/main_router.dart';

class RoadAssistanceRoute extends GoRouteData with _$RoadAssistanceRoute {
  const RoadAssistanceRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const RoadAssistanceScreen();
  }
}

class RequestRoadAssistanceRoute extends GoRouteData
    with _$RequestRoadAssistanceRoute {
  const RequestRoadAssistanceRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const RequestRoadAssistanceScreen();
  }
}

class RequestRoadAssistanceOtherRoute extends GoRouteData
    with _$RequestRoadAssistanceOtherRoute {
  const RequestRoadAssistanceOtherRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const RequestRoadAssistanceForOtherScreen();
  }
}
