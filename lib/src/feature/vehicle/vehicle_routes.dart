part of '../main/main_router.dart';

class <PERSON><PERSON>ehiclesRoute extends GoRouteData with _$MyVehiclesRoute {
  const MyVehiclesRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const VehiclesScreen();
  }
}

class NewVehicleRoute extends GoRouteData with _$NewVehicleRoute {
  const NewVehicleRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const EditVehicleScreen();
  }
}

class EditVehicleRoute extends GoRouteData with _$EditVehicleRoute {
  const EditVehicleRoute({required this.id});

  final int id;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditVehicleScreen(
      args: EditVehicleScreenArgs(id: id),
    );
  }
}
