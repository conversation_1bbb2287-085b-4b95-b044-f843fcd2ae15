import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/vehicle/edit/edit_vehicle_wm.dart';
import 'package:sba/src/feature/vehicle/edit/widget/vehicle_form.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class EditVehicleScreenArgs {
  const EditVehicleScreenArgs({required this.id});

  final int id;
}

class EditVehicleScreen extends ElementaryWidget<IEditVehicleWidgetModel> {
  const EditVehicleScreen({
    this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultEditVehicleWidgetModelFactory,
  }) : super(wmFactory, key: key);
  final EditVehicleScreenArgs? args;

  @override
  Widget build(IEditVehicleWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          StateNotifierBuilder(
            listenableState: wm.title,
            builder: (context, title) => SliverTitle(
              text: title ?? '',
              padding: UISpacing.defaultElementPaddingWithoutBottom,
            ),
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: TripleSourceBuilder(
            firstSource: wm.brands,
            secondSource: wm.places,
            thirdSource: wm.vehicle,
            builder: (context, brands, places, vehicle) => VehicleForm(
              onSubmitClick: wm.onSubmitTap,
              onDeleteClick: wm.onDeleteTap,
              brands: brands,
              cities: places,
              initialData: vehicle,
            ),
          ),
        ),
      ),
    );
  }
}
