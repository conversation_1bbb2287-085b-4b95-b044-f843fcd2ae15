import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_configured_typeahead.dart';

class VehicleForm extends StatelessWidget {
  VehicleForm({
    required this.onSubmitClick,
    required this.onDeleteClick,
    super.key,
    this.cities,
    this.brands,
    this.initialData,
  });

  final ValueChanged<VehicleData> onSubmitClick;
  final ValueChanged<VehicleData> onDeleteClick;

  final List<CarBrand>? brands;
  final List<Place>? cities;
  final VehicleData? initialData;
  final _formKey = GlobalKey<FormBuilderState>();
  final ValueNotifier<CarBrand?> _selectedBrand = ValueNotifier(null);

  static const ({
    String address,
    String brand,
    String city,
    String model,
    String plate,
  })
  _keys = (
    brand: 'brand',
    model: 'model',
    plate: 'plate',
    city: 'city',
    address: 'address',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.l10n.vehicle_edit_information_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<CarBrand>(
            name: _keys.brand,
            initialValue: initialData?.brand,
            decoration: InputDecoration(
              labelText: context.l10n.form_brand,
              hintText: context.l10n.form_brand_hint,
            ),
            items:
                brands
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.name),
                      ),
                    )
                    .toList() ??
                List.empty(),
            onChanged: (d) => _selectedBrand.value = d,
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          ValueListenableBuilder(
            valueListenable: _selectedBrand,
            builder: (context, brand, _) => FormBuilderDropdown<CarModel>(
              name: _keys.model,
              initialValue: brand != null ? null : initialData?.model,
              decoration: InputDecoration(
                labelText: context.l10n.form_model,
                hintText: context.l10n.form_model_hint,
              ),
              items:
                  (brand ?? initialData?.brand)?.models
                      .map(
                        (it) => DropdownMenuItem(
                          value: it,
                          child: Text(it.name),
                        ),
                      )
                      .toList() ??
                  List.empty(),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.plate,
            initialValue: initialData?.plateNumber,
            decoration: InputDecoration(
              labelText: context.l10n.form_plate_number,
              hintText: context.l10n.form_plate_number_hint,
            ),
            textInputAction: TextInputAction.next,
            validator: FormBuilderValidators.licensePlate(
              errorText: context.l10n.form_validation_license_plate,
            ),
          ),
          const Gap(UISpacing.xxl),
          Text(
            context.l10n.vehicle_edit_place_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderConfiguredTypeahead<Place>(
            name: _keys.city,
            initialValue: initialData?.city,
            data: cities,
            filter: (p, suggestion) => p.name.containsIgnoreCase(suggestion),
            textTransformer: (p) => p.formattedText,
            decoration: InputDecoration(
              labelText: context.l10n.form_city,
              hintText: context.l10n.form_city_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.address,
            initialValue: initialData?.address,
            keyboardType: TextInputType.streetAddress,
            textInputAction: TextInputAction.done,
            decoration: InputDecoration(
              labelText: context.l10n.form_address,
              hintText: context.l10n.form_address_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(
              initialData != null
                  ? context.l10n.action_edit
                  : context.l10n.action_add,
            ),
          ),
          if (initialData != null) const Gap(UISpacing.m),
          if (initialData != null)
            FilledButton(
              onPressed: () => onDeleteClick(initialData!),
              style: FilledButton.styleFrom(
                backgroundColor: context.theme.colorScheme.error,
              ),
              child: Text(context.l10n.action_delete),
            ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      final data = initialData ?? VehicleData.empty();

      onSubmitClick(
        data.copyWith(
          brand: _formKey.currentState!.value[_keys.brand] as CarBrand,
          model: _formKey.currentState!.value[_keys.model] as CarModel,
          plateNumber: _formKey.currentState!.value[_keys.plate] as String,
          city: _formKey.currentState!.value[_keys.city] as Place,
          address: _formKey.currentState!.value[_keys.address] as String,
        ),
      );
    }
  }
}
