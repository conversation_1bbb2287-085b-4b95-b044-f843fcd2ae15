import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class EditVehicleModel extends ElementaryModel {
  EditVehicleModel({
    required VehicleRepository vehicleRepository,
    required GeneralRepository generalRepository,
    super.errorHandler,
  })  : _vehicleRepository = vehicleRepository,
        _generalRepository = generalRepository;

  final VehicleRepository _vehicleRepository;
  final GeneralRepository _generalRepository;

  Future<VehicleData?> vehicleForId(int id) =>
      _vehicleRepository.getVehicle(id);

  Future<List<Place>?> getPlaces() =>
      _generalRepository.getPlaces().then((e) => e.maybeValue);

  Future<List<CarBrand>?> getBrands() =>
      _generalRepository.getCarBrands().then((e) => e.maybeValue);

  Future<Result<void>> createVehicle(VehicleData data) =>
      _vehicleRepository.createVehicle(data);

  Future<Result<void>> updateVehicle(VehicleData data) =>
      _vehicleRepository.updateVehicle(data);

  Future<Result<void>> deleteVehicle(VehicleData data) =>
      _vehicleRepository.deleteVehicle(data);
}
