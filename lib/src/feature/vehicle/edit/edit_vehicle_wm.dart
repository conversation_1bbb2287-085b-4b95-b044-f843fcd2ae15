import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/vehicle/edit/edit_vehicle_model.dart';
import 'package:sba/src/feature/vehicle/edit/edit_vehicle_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:toastification/toastification.dart';

abstract interface class IEditVehicleWidgetModel implements IWidgetModel {
  StateNotifier<String> get title;

  StateNotifier<VehicleData?> get vehicle;

  StateNotifier<List<CarBrand>?> get brands;

  StateNotifier<List<Place>?> get places;

  void onSubmitTap(VehicleData data);

  void onDeleteTap(VehicleData data);
}

EditVehicleWidgetModel defaultEditVehicleWidgetModelFactory(
  BuildContext context,
) {
  return EditVehicleWidgetModel(
    EditVehicleModel(
      vehicleRepository: get(),
      generalRepository: get(),
    ),
  );
}

class EditVehicleWidgetModel
    extends WidgetModel<EditVehicleScreen, EditVehicleModel>
    implements IEditVehicleWidgetModel {
  EditVehicleWidgetModel(super.model);

  final StateNotifier<String> _title = StateNotifier();
  final StateNotifier<VehicleData?> _vehicle = StateNotifier();
  final StateNotifier<List<Place>> _places = StateNotifier();
  final StateNotifier<List<CarBrand>> _brands = StateNotifier();

  @override
  void initWidgetModel() async {
    _title.accept(
      widget.args == null
          ? context.l10n.navigation_vehicles_new
          : context.l10n.navigation_vehicles_edit,
    );

    _places.accept(await model.getPlaces());
    _brands.accept(await model.getBrands());

    if (widget.args != null) {
      _vehicle.accept(await model.vehicleForId(widget.args!.id));
    }
    super.initWidgetModel();
  }

  @override
  void onDeleteTap(VehicleData data) async {
    Future<void> delete(VehicleData data) async {
      await context.showLoadingDialog();
      final result = await model.deleteVehicle(data);
      context.hideLoadingDialog();

      if (result is Failure) {
        await context.showGeneralErrorDialog(failure: result);
        return;
      }

      context.showToast(
        type: ToastificationType.success,
        title: context.l10n.message_success_edit,
      );

      await context.router.popRoute();
    }

    await context.showMessageDialog(
      builder: (BuildContext context) => MessageDialog.action(
        type: MessageDialogType.error,
        title: context.l10n.message_delete_vehicle_title,
        text: context.l10n.message_delete_vehicle_text(data.plateNumber),
        primaryActionText: context.l10n.action_delete,
        secondaryActionText: context.l10n.action_cancel,
        primaryAction: () => delete(data),
      ),
    );
  }

  @override
  Future<void> onSubmitTap(VehicleData data) async {
    await context.showLoadingDialog();
    final result = data.id != null
        ? await model.updateVehicle(data)
        : await model.createVehicle(data);

    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context.showToast(
      type: ToastificationType.success,
      title: data.id == null
          ? context.l10n.message_success_create
          : context.l10n.message_success_edit,
    );

    await context.router.popRoute();
  }

  @override
  StateNotifier<String> get title => _title;

  @override
  StateNotifier<List<Place>?> get places => _places;

  @override
  StateNotifier<List<CarBrand>?> get brands => _brands;

  @override
  StateNotifier<VehicleData?> get vehicle => _vehicle;
}
