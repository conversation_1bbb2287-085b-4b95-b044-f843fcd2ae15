import 'package:flutter/material.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/uri_image.dart';

class VehicleTile extends StatelessWidget {
  const VehicleTile({required this.data, super.key, this.onTap});

  final VehicleData data;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Card(
        clipBehavior: Clip.hardEdge,
        child: Stack(
          alignment: Alignment.center,
          children: [
            UriImage(
              imageUri: data.imageUri,
              placeholder: Assets.images.carPlaceholder.path,
              color: data.imageUri == null
                  ? Colors.transparent
                  : UIColors.imageMask,
              fit: BoxFit.cover,
              size: Size.infinite,
              blendMode: BlendMode.darken,
            ),
            Padding(
              padding: const EdgeInsets.all(UISpacing.l),
              child: Text(
                '${data.brandName} ${data.modelName}',
                style: context.textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                ),
                maxLines: 2,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
