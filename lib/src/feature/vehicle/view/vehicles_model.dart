import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class VehiclesModel extends ElementaryModel {
  VehiclesModel({
    required VehicleRepository vehicleRepository,
    super.errorHandler,
  }) : _vehicleRepository = vehicleRepository;

  final VehicleRepository _vehicleRepository;

  Future<void> refresh() => _vehicleRepository.refresh();

  Stream<Result<List<VehicleData>>> get vehicles => _vehicleRepository.vehicles;
}
