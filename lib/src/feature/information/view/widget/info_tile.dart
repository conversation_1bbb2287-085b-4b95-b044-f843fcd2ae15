import 'package:flutter/material.dart';
import 'package:sba/src/repository/content/model/info.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/html_view.dart';

class InfoTile extends StatelessWidget {
  const InfoTile({
    required this.data,
    super.key,
  });

  final Info data;

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      title: Text(
        data.title,
        style: context.textTheme.headlineSmall,
      ),
      children: [
        HtmlView(html: data.content),
      ],
    );
  }
}
