import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/information/view/information_model.dart';
import 'package:sba/src/feature/information/view/information_screen.dart';
import 'package:sba/src/repository/content/model/info.dart';

abstract interface class IInformationWidgetModel implements IWidgetModel {
  StateNotifier<List<Info>> get info;
}

InformationWidgetModel defaultInformationWidgetModelFactory(
  BuildContext context,
) {
  return InformationWidgetModel(
    InformationModel(
      contentRepository: get(),
    ),
  );
}

class InformationWidgetModel
    extends WidgetModel<InformationScreen, InformationModel>
    implements IInformationWidgetModel {
  InformationWidgetModel(super.model);

  final _info = StateNotifier<List<Info>>();

  @override
  void initWidgetModel() {
    _loadData();
    super.initWidgetModel();
  }

  void _loadData() async {
    final result = await model.information;
    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
    }

    _info.accept(result.maybeValue);
  }

  @override
  StateNotifier<List<Info>> get info => _info;
}
