import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/information/view/information_wm.dart';
import 'package:sba/src/feature/information/view/widget/info_tile.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/content/model/info.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

class InformationScreen extends ElementaryWidget<IInformationWidgetModel> {
  const InformationScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultInformationWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IInformationWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_information,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
          const SliverGap(UISpacing.m),
          SliverToBoxAdapter(
            child: Padding(
              padding: UISpacing.defaultElementHorizontalPadding,
              child: Assets.images.subscriptionPartners.image(),
            ),
          ),
        ],
        body: StateNotifierBuilder(
          listenableState: wm.info,
          builder: (context, data) => SmartListView(
            padding: UISpacing.defaultScreenPadding,
            loading: data == null,
            data: data,
            emptyText: context.l10n.empty_common,
            skeletonData: List.filled(5, Info.fake()),
            builder: (context, data) => InfoTile(data: data),
          ),
        ),
      ),
    );
  }
}
