import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/content/content_repository.dart';
import 'package:sba/src/repository/content/model/info.dart';

class InformationModel extends ElementaryModel {
  InformationModel({
    required ContentRepository contentRepository,
    super.errorHandler,
  }) : _contentRepository = contentRepository;

  final ContentRepository _contentRepository;

  Future<Result<List<Info>>> get information => _contentRepository.getInfo();
}
