import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/mot/view/mot_service_model.dart';
import 'package:sba/src/feature/service_book/mot/view/mot_service_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/ui/widget/message_box.dart';

abstract interface class IMotServiceWidgetModel implements IWidgetModel {
  StateNotifier<Widget?> get warningMessage;

  EntityStateNotifier<List<AnnualInspectionData>> get inspections;

  void onMotTap();

  void onAdd();

  void onEdit(AnnualInspectionData data);
}

MotServiceWidgetModel defaultMotServiceWidgetModelFactory(
  BuildContext context,
) {
  return MotServiceWidgetModel(
    MotServiceModel(
      repository: get(),
      vehicleRepository: get(),
      errorHandler: get(),
    ),
  );
}

class MotServiceWidgetModel
    extends WidgetModel<MotServiceScreen, MotServiceModel>
    implements IMotServiceWidgetModel {
  MotServiceWidgetModel(super.model);

  final _warningMessage = StateNotifier<Widget?>();
  final _inspections = EntityStateNotifier<List<AnnualInspectionData>>();

  @override
  void initWidgetModel() {
    _syncData();
    super.initWidgetModel();
  }

  void _syncData() async {
    _inspections.loading();
    final result = await model.getVehicleService(widget.args.vehicleId);

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      await context.router.popRoute();
      return;
    }

    final sorted = result.maybeValue!.sorted(
      (a, b) => b.date.compareTo(a.date),
    );

    _inspections.content(sorted);

    final ins = sorted.firstOrNull;
    final aboutToExpire =
        ins?.end.isLessThan(const Duration(days: 30)) ?? false;
    final vehicle = await model.getVehicleWithId(widget.args.vehicleId);

    _warningMessage.accept(
      aboutToExpire
          ? MessageBox(
              title: context.l10n.message_mot_expiring_title,
              text: context.l10n.message_mot_expiring_text(
                vehicle?.plateNumber ?? '',
              ),
              type: MessageBoxType.error,
              actionBuilder: (context, color) => [
                TextButton(
                  onPressed: onMotTap,
                  style: TextButton.styleFrom(foregroundColor: color),
                  child: Text(
                    context.l10n.action_reserve_mot,
                  ),
                ),
              ],
            )
          : null,
    );
  }

  @override
  void onAdd() async {
    await EditAnnualInspectionRoute(
      vehicleId: widget.args.vehicleId,
    ).push<void>(context);

    _syncData();
  }

  @override
  void onEdit(AnnualInspectionData data) async {
    await EditAnnualInspectionRoute(
      vehicleId: widget.args.vehicleId,
      $extra: data,
    ).push<void>(context);

    _syncData();
  }

  @override
  void onMotTap() => const MotRoute().go(context);

  @override
  EntityStateNotifier<List<AnnualInspectionData>> get inspections =>
      _inspections;

  @override
  StateNotifier<Widget?> get warningMessage => _warningMessage;
}
