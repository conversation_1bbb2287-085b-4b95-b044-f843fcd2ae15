import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/service_book/mot/view/mot_service_wm.dart';
import 'package:sba/src/feature/service_book/mot/view/widget/mot_service_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

final class MotServiceScreenArgs {
  MotServiceScreenArgs({required this.vehicleId});

  final int vehicleId;
}

class MotServiceScreen extends ElementaryWidget<IMotServiceWidgetModel> {
  const MotServiceScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultMotServiceWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final MotServiceScreenArgs args;

  @override
  Widget build(IMotServiceWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          EntityStateNotifierBuilder(
            listenableEntityState: wm.inspections,
            builder: (context, data) => SliverTitle(
              text: context.l10n.navigation_book_mot,
              padding: UISpacing.defaultElementPaddingWithoutBottom,
              action: data?.isEmpty ?? true
                  ? null
                  : FloatingActionButton.small(
                      onPressed: wm.onAdd,
                      child: const Icon(Icons.add),
                    ),
            ),
          ),
        ],
        body: CustomScrollView(
          slivers: [
            StateNotifierBuilder(
              listenableState: wm.warningMessage,
              builder: (context, message) => SliverToBoxAdapter(
                child: message != null
                    ? Padding(
                        padding: UISpacing.defaultElementPaddingWithoutBottom,
                        child: message,
                      )
                    : const SizedBox.shrink(),
              ),
            ),
            EntityStateNotifierBuilder(
              listenableEntityState: wm.inspections,
              builder: (context, data) => SmartListView.sliver(
                loading: data == null,
                data: data,
                skeletonData: List.filled(1, AnnualInspectionData.fake()),
                emptyText: '',
                padding: UISpacing.defaultScreenPadding,
                separator: const Gap(UISpacing.ll),
                showActionWhenData: false,
                action: ActionBox(
                  icon: Icons.add,
                  text: context.l10n.action_add,
                  onTap: wm.onAdd,
                ),
                builder: (context, data) => MotServiceTile(
                  data: data,
                  onEdit: () => wm.onEdit(data),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
