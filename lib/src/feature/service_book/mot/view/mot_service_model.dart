import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class MotServiceModel extends ElementaryModel {
  MotServiceModel({
    required ServiceBookRepository repository,
    required VehicleRepository vehicleRepository,
    super.errorHandler,
  })  : _repository = repository,
        _vehicleRepository = vehicleRepository;

  final ServiceBookRepository _repository;
  final VehicleRepository _vehicleRepository;

  Future<Result<List<AnnualInspectionData>>> getVehicleService(int vehicleId) =>
      _repository.getAnnualInspectionForVehicle(vehicleId: vehicleId);

  Future<VehicleData?> getVehicleWithId(int vehicleId) =>
      _vehicleRepository.getVehicle(vehicleId);
}
