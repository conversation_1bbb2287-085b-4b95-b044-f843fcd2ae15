import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class EditMotServiceModel extends ElementaryModel {
  EditMotServiceModel({
    required ServiceBookRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<void>> createOrUpdate(AnnualInspectionData data) =>
      _repository.crudAnnualInspection(data);

  Future<Result<void>> deleteAnnualInspection(AnnualInspectionData data) =>
      _repository.crudAnnualInspection(data, delete: true);
}
