import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class MotServiceForm extends StatelessWidget {
  MotServiceForm({
    required this.onSubmitClick,
    required this.onDeleteClick,
    super.key,
    this.initialValue,
  });

  final AnnualInspectionData? initialValue;
  final ValueChanged<AnnualInspectionData> onSubmitClick;
  final ValueChanged<AnnualInspectionData> onDeleteClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  static const ({String date, String duration, String mileage, String note, String price, String station}) _keys = (
    date: 'date',
    station: 'station',
    duration: 'duration',
    mileage: 'mileage',
    price: 'price',
    note: 'note',
  );

  static const _durationValues = [
    6,
    12,
    24,
    36,
  ];

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderTextField(
            name: _keys.station,
            initialValue: initialValue?.station,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_station,
              hintText: context.l10n.form_station_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDateTimePicker(
            name: _keys.date,
            initialValue: initialValue?.date,
            locale: context.locale,
            textInputAction: TextInputAction.next,
            inputType: InputType.date,
            decoration: InputDecoration(
              labelText: context.l10n.form_date,
              hintText: context.l10n.form_date_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<int>(
            name: _keys.duration,
            items: _durationValues
                .map(
                  (e) => DropdownMenuItem(
                    value: e,
                    child: Text(context.l10n.service_book_months(e)),
                  ),
                )
                .toList(),
            initialValue: initialValue?.durationMonths ?? 12,
            decoration: InputDecoration(
              labelText: context.l10n.form_valid_duration,
              hintText: context.l10n.form_valid_duration_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.mileage,
            initialValue: initialValue?.mileage.toString(),
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.round(),
            decoration: InputDecoration(
              labelText: context.l10n.form_odometer,
              hintText: context.l10n.form_odometer_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.price,
            initialValue: initialValue?.price.toString(),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.toDouble(),
            decoration: InputDecoration(
              labelText: context.l10n.form_price,
              hintText: context.l10n.form_price_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.note,
            initialValue: initialValue?.notes,
            maxLines: null,
            minLines: 4,
            keyboardType: TextInputType.multiline,
            decoration: InputDecoration(
              labelText: context.l10n.form_note,
              hintText: context.l10n.form_note_hint,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(
              initialValue != null
                  ? context.l10n.action_edit
                  : context.l10n.action_add,
            ),
          ),
          if (initialValue != null) const Gap(UISpacing.m),
          if (initialValue != null)
            FilledButton(
              onPressed: () => onDeleteClick(initialValue!),
              style: FilledButton.styleFrom(
                backgroundColor: context.theme.colorScheme.error,
              ),
              child: Text(context.l10n.action_delete),
            ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      final data = initialValue ?? AnnualInspectionData.empty();

      onSubmitClick(
        data.copyWith(
          station: _formKey.currentState!.value[_keys.station] as String,
          date: _formKey.currentState!.value[_keys.date] as DateTime,
          durationMonths: _formKey.currentState!.value[_keys.duration] as int,
          mileage: _formKey.currentState!.value[_keys.mileage] as int,
          price: _formKey.currentState!.value[_keys.price] as double,
          notes: _formKey.currentState!.value[_keys.note] as String?,
        ),
      );
    }
  }
}
