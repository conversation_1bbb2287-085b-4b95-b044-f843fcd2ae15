import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/mot/edit/edit_mot_service_wm.dart';
import 'package:sba/src/feature/service_book/mot/edit/widget/mot_service_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class EditMotServiceScreenArgs {
  EditMotServiceScreenArgs({required this.vehicleId, required this.data});

  final int? vehicleId;
  final AnnualInspectionData? data;
}

class EditMotServiceScreen
    extends ElementaryWidget<IEditMotServiceWidgetModel> {
  const EditMotServiceScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultEditMotServiceWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final EditMotServiceScreenArgs args;

  @override
  Widget build(IEditMotServiceWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_mot,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: StateNotifierBuilder(
            listenableState: wm.initialValue,
            builder: (context, data) => MotServiceForm(
              initialValue: data,
              onSubmitClick: wm.onSubmit,
              onDeleteClick: wm.onDelete,
            ),
          ),
        ),
      ),
    );
  }
}
