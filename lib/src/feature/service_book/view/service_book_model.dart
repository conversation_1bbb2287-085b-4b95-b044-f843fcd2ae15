import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class ServiceBookModel extends ElementaryModel {
  ServiceBookModel({
    required VehicleRepository vehicleRepository,
    required ServiceBookRepository serviceRepository,
    super.errorHandler,
  })  : _vehicleRepository = vehicleRepository,
        _serviceBookRepository = serviceRepository;

  final VehicleRepository _vehicleRepository;
  final ServiceBookRepository _serviceBookRepository;

  Stream<Result<List<VehicleData>>> get vehicles => _vehicleRepository.vehicles;

  Future<AnnualInspectionData?> lastInspection({
    required VehicleData vehicle,
  }) async {
    final result = await _serviceBookRepository.getAnnualInspectionForVehicle(
      vehicleId: vehicle.id!,
    );

    return result.maybeValue
        ?.sorted((a, b) => b.date.compareTo(a.date))
        .firstOrNull;
  }
}
