import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/main/home/<USER>/vehicle_tile.dart';
import 'package:sba/src/feature/service_book/view/service_book_wm.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/list_tile_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_carousel.dart';

class ServiceBookScreen extends ElementaryWidget<IServiceBookWidgetModel> {
  const ServiceBookScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultServiceBookWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IServiceBookWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_service_book,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: CustomScrollView(
          slivers: [
            const SliverGap(UISpacing.xxl),
            StateNotifierBuilder(
              listenableState: wm.vehicles,
              builder: (context, data) => SliverToBoxAdapter(
                child: data?.isEmpty ?? false
                    ? Padding(
                        padding: UISpacing.defaultElementPaddingWithoutTop,
                        child: ActionBox(
                          icon: Icons.add,
                          text: context.l10n.action_add_vehicle,
                          onTap: wm.onAddVehicleTap,
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
            ),
            StateNotifierBuilder(
              listenableState: wm.vehicles,
              builder: (context, data) => SmartCarousel<VehicleData>.sliver(
                loading: data == null,
                skeletonData: [VehicleData.fake()],
                data: data ?? [],
                height: 224,
                padding: const EdgeInsets.only(bottom: UISpacing.xxl),
                builder: (context, data) => VehicleTile(data: data),
                onSelectionChange: wm.onVehicleChange,
              ),
            ),
            const SliverGap(UISpacing.xl),
            SliverToBoxAdapter(
              child: StateNotifierBuilder(
                listenableState: wm.selectedVehicle,
                builder: (context, vehicle) {
                  return vehicle != null
                      ? Padding(
                          padding: UISpacing.defaultElementHorizontalPadding,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            spacing: UISpacing.s,
                            children: [
                              ListTileBox.navigation(
                                icon: Assets.icons.build.svg(),
                                title: context.l10n.navigation_book_service,
                                onTap: () => wm.onServiceTap(vehicle),
                              ),
                              StateNotifierBuilder(
                                listenableState: wm.annualServiceWarning,
                                builder: (context, data) =>
                                    ListTileBox.navigation(
                                  icon: Assets.icons.warehouse.svg(),
                                  title: context.l10n.navigation_book_mot,
                                  warning: data ?? false,
                                  onTap: () => wm.onMotTap(vehicle),
                                ),
                              ),
                              ListTileBox.navigation(
                                icon: Assets.icons.localGasStation.svg(),
                                title: context.l10n.navigation_book_fuel,
                                onTap: () => wm.onFuelTap(vehicle),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
