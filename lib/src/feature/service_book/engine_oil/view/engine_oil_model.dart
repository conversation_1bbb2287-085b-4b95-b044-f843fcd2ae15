import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class EngineOilModel extends ElementaryModel {
  EngineOilModel({
    required ServiceBookRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<List<EngineOilData>>> getEngineOil(int vehicleId) =>
      _repository.getEngineOilForVehicle(vehicleId: vehicleId);
}
