import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/engine_oil/view/engine_oil_model.dart';
import 'package:sba/src/feature/service_book/engine_oil/view/engine_oil_screen.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';

abstract interface class IEngineOilWidgetModel implements IWidgetModel {
  EntityStateNotifier<List<EngineOilData>> get engineOil;

  void onAdd();

  void onEdit(EngineOilData data);
}

EngineOilWidgetModel defaultEngineOilWidgetModelFactory(BuildContext context) {
  return EngineOilWidgetModel(
    EngineOilModel(errorHandler: get(), repository: get()),
  );
}

class EngineOilWidgetModel extends WidgetModel<EngineOilScreen, EngineOilModel>
    implements IEngineOilWidgetModel {
  EngineOilWidgetModel(super.model);

  final _engineOil = EntityStateNotifier<List<EngineOilData>>();

  @override
  void initWidgetModel() {
    _syncData();
    super.initWidgetModel();
  }

  void _syncData() async {
    _engineOil.loading();
    final result = await model.getEngineOil(widget.args.vehicleId);

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      await context.router.popRoute();
      return;
    }

    _engineOil.content(
      result.maybeValue!.sorted(
        (a, b) => b.changeDate.compareTo(a.changeDate),
      ),
    );
  }

  @override
  EntityStateNotifier<List<EngineOilData>> get engineOil => _engineOil;

  @override
  void onAdd() async {
    await EditEngineOilChangeRoute(
      vehicleId: widget.args.vehicleId,
    ).push<void>(context);

    _syncData();
  }

  @override
  void onEdit(EngineOilData data) async {
    await EditEngineOilChangeRoute(
      vehicleId: widget.args.vehicleId,
      $extra: data,
    ).push<void>(context);
    _syncData();
  }
}
