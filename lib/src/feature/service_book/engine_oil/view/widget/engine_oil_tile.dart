import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';
import 'package:sba/src/repository/service_book/model/types/vehicle_filter_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/circle_button.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class EngineOilTile extends StatelessWidget {
  const EngineOilTile({
    required this.data,
    required this.onEdit,
    super.key,
  });

  final EngineOilData data;
  final VoidCallback onEdit;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: UISpacing.m,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          data.changeDate.formatDate(context),
          style: context.textTheme.headlineMedium,
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            vertical: UISpacing.m,
            horizontal: UISpacing.l,
          ),
          decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
          alignment: Alignment.center,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  Skeleton.replace(
                    width: 40,
                    height: 40,
                    child: Assets.icons.engine.svg(),
                  ),
                  const Spacer(),
                  CircleButton(
                    icon: const Icon(Icons.edit),
                    onTap: onEdit,
                  ),
                ],
              ),
              const Gap(UISpacing.m),
              LabelWithText(
                label: context.l10n.service_book_oil_label,
                text: data.oilType,
              ),
              const Gap(UISpacing.s),
              LabelWithText(
                label: context.l10n.service_book_autoservice_label,
                text: data.autoService,
              ),
              const Gap(UISpacing.s),
              LabelWithText(
                label: context.l10n.service_book_odometer_label,
                text: data.mileage.formatToMileage(context),
              ),
              const Gap(UISpacing.s),
              LabelWithText(
                label: context.l10n.service_book_quantity_label,
                text: data.oilQuantity.formatLiters(context),
              ),
              const Gap(UISpacing.s),
              LabelWithText(
                label: context.l10n.service_book_price_label,
                text: data.price.formatCurrency(context),
              ),
              if (data.changedFilters.isNotEmpty) const Gap(UISpacing.s),
              if (data.changedFilters.isNotEmpty)
                Text(
                  context.l10n.service_book_changed_label,
                  style: context.textTheme.bodySmall,
                ),
              if (data.changedFilters.isNotEmpty) const Gap(UISpacing.xs),
              Wrap(
                runSpacing: UISpacing.s,
                spacing: UISpacing.s,
                alignment: WrapAlignment.spaceBetween,
                runAlignment: WrapAlignment.spaceBetween,
                children: data.changedFilters
                    .map(
                      (e) => Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.check,
                            color: UIColors.green,
                            size: 18,
                          ),
                          const Gap(UISpacing.xs),
                          Text(
                            e.localizedText(context),
                            style: context.textTheme.bodySmall
                                ?.copyWith(fontWeight: UIFontWeight.semiBold),
                          ),
                        ],
                      ),
                    )
                    .toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
