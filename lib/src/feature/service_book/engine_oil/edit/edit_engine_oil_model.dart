import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class EditEngineOilModel extends ElementaryModel {
  EditEngineOilModel({
    required ServiceBookRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<void>> createOrUpdate(EngineOilData data) =>
      _repository.crudEngineOil(data);

  Future<Result<void>> deleteEngineOil(EngineOilData data) =>
      _repository.crudEngineOil(data, delete: true);
}
