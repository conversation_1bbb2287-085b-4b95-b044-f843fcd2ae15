import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/engine_oil/edit/edit_engine_oil_wm.dart';
import 'package:sba/src/feature/service_book/engine_oil/edit/widget/engine_oil_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class EditEngineOilScreenArgs {
  EditEngineOilScreenArgs({required this.vehicleId, required this.data});

  final int? vehicleId;
  final EngineOilData? data;
}

class EditEngineOilScreen extends ElementaryWidget<IEditEngineOilWidgetModel> {
  const EditEngineOilScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultEditEngineOilWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final EditEngineOilScreenArgs args;

  @override
  Widget build(IEditEngineOilWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_engine_oil,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: StateNotifierBuilder(
            listenableState: wm.initialValue,
            builder: (context, data) => EngineOilForm(
              initialValue: data,
              onSubmitClick: wm.onSubmit,
              onDeleteClick: wm.onDelete,
            ),
          ),
        ),
      ),
    );
  }
}
