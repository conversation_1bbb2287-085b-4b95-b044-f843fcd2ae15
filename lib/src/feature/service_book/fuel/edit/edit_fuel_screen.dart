import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/fuel/edit/edit_fuel_wm.dart';
import 'package:sba/src/feature/service_book/fuel/edit/widget/refuel_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class EditFuelScreenArgs {
  EditFuelScreenArgs({required this.vehicleId, required this.data});

  final int? vehicleId;
  final RefuelData? data;
}

class EditFuelScreen
    extends ElementaryWidget<IEditFuelWidgetModel> {
  const EditFuelScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultEditFuelWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final EditFuelScreenArgs args;

  @override
  Widget build(IEditFuelWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_fuel,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: DoubleSourceBuilder(
            firstSource: wm.fuels,
            secondSource: wm.initialValue,
            builder: (context, fuels, data) => RefuelForm(
              initialValue: data,
              traders: fuels,
              onSubmitClick: wm.onSubmit,
              onDeleteClick: wm.onDelete,
            ),
          ),
        ),
      ),
    );
  }
}
