import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/fuel_trader.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class RefuelForm extends StatelessWidget {
  RefuelForm({
    required this.onSubmitClick,
    required this.onDeleteClick,
    super.key,
    this.initialValue,
    this.traders,
  });

  final RefuelData? initialValue;
  final List<FuelTrader>? traders;
  final ValueChanged<RefuelData> onSubmitClick;
  final ValueChanged<RefuelData> onDeleteClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final ValueNotifier<FuelTrader?> _selectedTrader = ValueNotifier(null);

  static const ({
    String date,
    String dealer,
    String fuel,
    String mileage,
    String name,
    String notes,
    String price,
    String quantity,
  })
  _keys = (
    date: 'date',
    dealer: 'dealer',
    fuel: 'fuel',
    name: 'name',
    quantity: 'quantity',
    mileage: 'mileage',
    price: 'price',
    notes: 'notes',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderDateTimePicker(
            name: _keys.date,
            initialValue: initialValue?.date,
            locale: context.locale,
            textInputAction: TextInputAction.next,
            inputType: InputType.date,
            decoration: InputDecoration(
              labelText: context.l10n.form_date,
              hintText: context.l10n.form_date_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<FuelTrader>(
            name: _keys.dealer,
            initialValue: initialValue?.trader,
            decoration: InputDecoration(
              labelText: context.l10n.form_dealer,
              hintText: context.l10n.form_dealer_choose_hint,
            ),
            items:
                traders
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.name),
                      ),
                    )
                    .toList() ??
                List.empty(),
            onChanged: (d) => _selectedTrader.value = d,
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          ValueListenableBuilder(
            valueListenable: _selectedTrader,
            builder: (context, trader, _) => FormBuilderDropdown<Fuel>(
              name: _keys.fuel,
              initialValue: trader != null ? null : initialValue?.fuel,
              decoration: InputDecoration(
                labelText: context.l10n.form_fuel,
                hintText: context.l10n.form_fuel_hint,
              ),
              items:
                  (trader ?? initialValue?.trader)?.fuels
                      .map(
                        (it) => DropdownMenuItem(
                          value: it,
                          child: Text(it.name),
                        ),
                      )
                      .toList() ??
                  List.empty(),
              validator: FormBuilderValidators.required(
                errorText: context.l10n.form_validation_required,
              ),
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.quantity,
            initialValue: initialValue?.quantity.toString(),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.toDouble(),
            decoration: InputDecoration(
              labelText: context.l10n.form_quantity,
              hintText: context.l10n.form_quantity_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.price,
            initialValue: initialValue?.price.toString(),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.toDouble(),
            decoration: InputDecoration(
              labelText: context.l10n.form_price_per_liter,
              hintText: context.l10n.form_price_per_liter_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.mileage,
            initialValue: initialValue?.mileage.toString(),
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.round(),
            decoration: InputDecoration(
              labelText: context.l10n.form_odometer,
              hintText: context.l10n.form_odometer_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.notes,
            initialValue: initialValue?.notes,
            maxLines: null,
            minLines: 4,
            keyboardType: TextInputType.multiline,
            decoration: InputDecoration(
              labelText: context.l10n.form_note,
              hintText: context.l10n.form_note_hint,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(
              initialValue != null
                  ? context.l10n.action_edit
                  : context.l10n.action_add,
            ),
          ),
          if (initialValue != null) const Gap(UISpacing.m),
          if (initialValue != null)
            FilledButton(
              onPressed: () => onDeleteClick(initialValue!),
              style: FilledButton.styleFrom(
                backgroundColor: context.theme.colorScheme.error,
              ),
              child: Text(context.l10n.action_delete),
            ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      final data = initialValue ?? RefuelData.empty();

      onSubmitClick(
        data.copyWith(
          date: _formKey.currentState!.value[_keys.date] as DateTime,
          trader: _formKey.currentState!.value[_keys.dealer] as FuelTrader,
          fuel: _formKey.currentState!.value[_keys.fuel] as Fuel,
          quantity: _formKey.currentState!.value[_keys.quantity] as double,
          mileage: _formKey.currentState!.value[_keys.mileage] as int,
          price: _formKey.currentState!.value[_keys.price] as double,
          notes: _formKey.currentState!.value[_keys.notes] as String?,
        ),
      );
    }
  }
}
