import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/fuel_trader.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class EditFuelModel extends ElementaryModel {
  EditFuelModel(
      {required ServiceBookRepository serviceBookRepository,
      required GeneralRepository generalRepository,
      super.errorHandler,})
      : _serviceBookRepository = serviceBookRepository,
        _generalRepository = generalRepository;

  final ServiceBookRepository _serviceBookRepository;
  final GeneralRepository _generalRepository;

  Future<Result<void>> createOrUpdate(RefuelData data) =>
      _serviceBookRepository.crudRefuel(data);

  Future<Result<void>> deleteRefuel(RefuelData data) =>
      _serviceBookRepository.crudRefuel(data, delete: true);

  Future<List<FuelTrader>?> get fuels =>
      _generalRepository.getFuels().then((e) => e.maybeValue);
}
