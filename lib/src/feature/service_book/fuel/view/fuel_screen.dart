import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/service_book/fuel/view/fuel_wm.dart';
import 'package:sba/src/feature/service_book/fuel/view/widget/refuel_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

final class FuelScreenArgs {
  FuelScreenArgs({required this.vehicleId});

  final int vehicleId;
}

class FuelScreen
    extends ElementaryWidget<IFuelWidgetModel> {
  const FuelScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultFuelWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final FuelScreenArgs args;

  @override
  Widget build(IFuelWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          EntityStateNotifierBuilder(
            listenableEntityState: wm.refuels,
            builder: (context, data) => SliverTitle(
              text: context.l10n.navigation_book_fuel,
              padding: UISpacing.defaultElementPaddingWithoutBottom,
              action: data?.isEmpty ?? true
                  ? null
                  : FloatingActionButton.small(
                      onPressed: wm.onAdd,
                      child: const Icon(Icons.add),
                    ),
            ),
          ),
        ],
        body: EntityStateNotifierBuilder(
          listenableEntityState: wm.refuels,
          builder: (context, data) => SmartListView(
            loading: data == null,
            data: data,
            skeletonData: List.filled(1, RefuelData.fake()),
            emptyText: '',
            padding: UISpacing.defaultScreenPadding,
            separator: const Gap(UISpacing.ll),
            showActionWhenData: false,
            action: ActionBox(
              icon: Icons.add,
              text: context.l10n.action_add,
              onTap: wm.onAdd,
            ),
            builder: (context, data) => RefuelTile(
              data: data,
              onEdit: () => wm.onEdit(data),
            ),
          ),
        ),
      ),
    );
  }
}
