import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/circle_button.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class RefuelTile extends StatelessWidget {
  const RefuelTile({
    required this.data,
    required this.onEdit,
    super.key,
  });

  final RefuelData data;
  final VoidCallback onEdit;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: UISpacing.m,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          data.date.formatDate(context),
          style: context.textTheme.headlineMedium,
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            vertical: UISpacing.m,
            horizontal: UISpacing.l,
          ),
          decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
          alignment: Alignment.center,
          child: Row(
            spacing: UISpacing.m,
            children: [
              Skeleton.replace(
                width: 40,
                height: 40,
                child: Assets.icons.localGasStation.svg(),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    LabelWithText(
                      label: context.l10n.service_book_dealer_label,
                      text: data.trader?.name ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_fuel_label,
                      text: data.fuel?.name ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_quantity_label,
                      text: data.quantity.formatLiters(context),
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_odometer_label,
                      text: data.mileage.formatToMileage(context),
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_price_label,
                      text: data.price.formatCurrency(context),
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_value_label,
                      text: data.value.formatCurrency(context),
                    ),
                  ],
                ),
              ),
              CircleButton(
                icon: const Icon(Icons.edit),
                onTap: onEdit,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
