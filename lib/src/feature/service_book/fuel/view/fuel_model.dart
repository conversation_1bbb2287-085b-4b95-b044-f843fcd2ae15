import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class FuelModel extends ElementaryModel {
  FuelModel(
      {required ServiceBookRepository repository, super.errorHandler,})
      : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<List<RefuelData>>> getRefuelData(int vehicleId) =>
      _repository.getRefuels(vehicleId: vehicleId);
}
