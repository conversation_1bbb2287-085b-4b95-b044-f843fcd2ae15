import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

// TODO: cover with documentation
/// Default Elementary model for EditOtherService module
class EditOtherServiceModel extends ElementaryModel {
  EditOtherServiceModel({
    required ServiceBookRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<void>> createOrUpdate(VehicleServiceData data) =>
      _repository.crudVehicleService(data);

  Future<Result<void>> deleteVehicleService(VehicleServiceData data) =>
      _repository.crudVehicleService(data, delete: true);
}
