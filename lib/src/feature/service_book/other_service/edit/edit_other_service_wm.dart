import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/service_book/other_service/edit/edit_other_service_model.dart';
import 'package:sba/src/feature/service_book/other_service/edit/edit_other_service_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:toastification/toastification.dart';

abstract interface class IEditOtherServiceWidgetModel implements IWidgetModel {
  StateNotifier<VehicleServiceData?> get initialValue;

  void onDelete(VehicleServiceData data);

  void onSubmit(VehicleServiceData data);
}

EditOtherServiceWidgetModel defaultEditOtherServiceWidgetModelFactory(
  BuildContext context,
) {
  return EditOtherServiceWidgetModel(
    EditOtherServiceModel(
      repository: get(),
      errorHandler: get(),
    ),
  );
}

// TODO: cover with documentation
/// Default widget model for EditOtherServiceWidget
class EditOtherServiceWidgetModel
    extends WidgetModel<EditOtherServiceScreen, EditOtherServiceModel>
    implements IEditOtherServiceWidgetModel {
  EditOtherServiceWidgetModel(super.model);

  final _initialValue = StateNotifier<VehicleServiceData?>();

  @override
  void initWidgetModel() {
    _initialValue.accept(widget.args.data);
    super.initWidgetModel();
  }

  @override
  void onSubmit(VehicleServiceData data) async {
    final value = data.copyWith(vehicleId: widget.args.vehicleId);

    await context.showLoadingDialog();
    final result = await model.createOrUpdate(value);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context.showToast(
      type: ToastificationType.success,
      title: data.id == null
          ? context.l10n.message_success_create
          : context.l10n.message_success_edit,
    );

    await context.router.popRoute();
  }

  @override
  void onDelete(VehicleServiceData data) async {
    Future<void> delete(VehicleServiceData data) async {
      await context.showLoadingDialog();
      final result = await model.deleteVehicleService(data);
      context.hideLoadingDialog();

      if (result is Failure) {
        await context.showGeneralErrorDialog(failure: result);
        return;
      }

      context.showToast(
        type: ToastificationType.success,
        title: context.l10n.message_success_delete,
      );

      await context.router.popRoute();
    }

    await context.showMessageDialog(
      builder: (BuildContext context) => MessageDialog.action(
        type: MessageDialogType.error,
        title: context.l10n.message_delete_title,
        text: context.l10n.message_delete_text,
        primaryActionText: context.l10n.action_delete,
        secondaryActionText: context.l10n.action_cancel,
        primaryAction: () => delete(initialValue.value!),
      ),
    );
  }

  @override
  StateNotifier<VehicleServiceData?> get initialValue => _initialValue;
}
