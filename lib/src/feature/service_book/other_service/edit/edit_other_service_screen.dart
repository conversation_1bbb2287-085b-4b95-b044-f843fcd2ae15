import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/other_service/edit/edit_other_service_wm.dart';
import 'package:sba/src/feature/service_book/other_service/edit/widget/other_service_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class EditOtherServiceScreenArgs {
  EditOtherServiceScreenArgs({required this.vehicleId, required this.data});

  final int? vehicleId;
  final VehicleServiceData? data;
}

class EditOtherServiceScreen
    extends ElementaryWidget<IEditOtherServiceWidgetModel> {
  const EditOtherServiceScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultEditOtherServiceWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final EditOtherServiceScreenArgs args;

  @override
  Widget build(IEditOtherServiceWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_other_service,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: StateNotifierBuilder(
            listenableState: wm.initialValue,
            builder: (context, data) => OtherServiceForm(
              initialValue: data,
              onSubmitClick: wm.onSubmit,
              onDeleteClick: wm.onDelete,
            ),
          ),
        ),
      ),
    );
  }
}
