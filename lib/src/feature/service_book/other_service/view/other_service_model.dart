import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class OtherServiceModel extends ElementaryModel {
  OtherServiceModel({
    required ServiceBookRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<List<VehicleServiceData>>> getVehicleService(int vehicleId) =>
      _repository.getServicesForVehicle(vehicleId: vehicleId);
}
