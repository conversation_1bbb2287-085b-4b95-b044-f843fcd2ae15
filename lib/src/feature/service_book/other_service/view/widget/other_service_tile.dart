import 'package:flutter/material.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/circle_button.dart';
import 'package:skeletonizer/skeletonizer.dart';

class OtherServiceTile extends StatelessWidget {
  const OtherServiceTile({
    required this.data,
    required this.onEdit,
    super.key,
  });

  final VehicleServiceData data;
  final VoidCallback onEdit;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: UISpacing.m,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          data.serviceDate.formatDate(context),
          style: context.textTheme.headlineMedium,
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            vertical: UISpacing.m,
            horizontal: UISpacing.l,
          ),
          decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
          alignment: Alignment.center,
          child: Row(
            spacing: UISpacing.m,
            children: [
              Skeleton.replace(
                width: 40,
                height: 40,
                child: Assets.icons.build.svg(),
              ),
              Expanded(
                child: Text(
                  data.name,
                  style: context.textTheme.headlineSmall,
                ),
              ),
              CircleButton(
                icon: const Icon(Icons.edit),
                onTap: onEdit,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
