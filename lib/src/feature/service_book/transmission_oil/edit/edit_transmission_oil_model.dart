import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class EditTransmissionOilModel extends ElementaryModel {
  EditTransmissionOilModel({
    required ServiceBookRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<void>> createOrUpdate(TransmissionOilData data) =>
      _repository.crudTransmissionOil(data);

  Future<Result<void>> deleteEngineOil(TransmissionOilData data) =>
      _repository.crudTransmissionOil(data, delete: true);
}
