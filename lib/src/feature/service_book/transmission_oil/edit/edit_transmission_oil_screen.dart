import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/transmission_oil/edit/edit_transmission_oil_wm.dart';
import 'package:sba/src/feature/service_book/transmission_oil/edit/widget/transmission_oil_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class EditTransmissionOilScreenArgs {
  EditTransmissionOilScreenArgs({required this.vehicleId, required this.data});

  final int? vehicleId;
  final TransmissionOilData? data;
}

class EditTransmissionOilScreen
    extends ElementaryWidget<IEditTransmissionOilWidgetModel> {
  const EditTransmissionOilScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultEditTransmissionOilWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final EditTransmissionOilScreenArgs args;

  @override
  Widget build(IEditTransmissionOilWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_transmission_oil,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: StateNotifierBuilder(
            listenableState: wm.initialValue,
            builder: (context, data) => TransmissionOilForm(
              initialValue: data,
              onSubmitClick: wm.onSubmit,
              onDeleteClick: wm.onDelete,
            ),
          ),
        ),
      ),
    );
  }
}
