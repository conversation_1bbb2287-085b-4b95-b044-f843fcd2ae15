import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/circle_button.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class TransmissionOilTile extends StatelessWidget {
  const TransmissionOilTile({
    required this.data,
    required this.onEdit,
    super.key,
  });

  final TransmissionOilData data;
  final VoidCallback onEdit;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: UISpacing.m,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          data.changeDate.formatDate(context),
          style: context.textTheme.headlineMedium,
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            vertical: UISpacing.m,
            horizontal: UISpacing.l,
          ),
          decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
          alignment: Alignment.center,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                children: [
                  Skeleton.replace(
                    width: 40,
                    height: 40,
                    child: Assets.icons.gearbox.svg(),
                  ),
                  const Spacer(),
                  CircleButton(
                    icon: const Icon(Icons.edit),
                    onTap: onEdit,
                  ),
                ],
              ),
              const Gap(UISpacing.m),
              LabelWithText(
                label: context.l10n.service_book_oil_label,
                text: data.oilType,
              ),
              const Gap(UISpacing.s),
              LabelWithText(
                label: context.l10n.service_book_autoservice_label,
                text: data.autoService,
              ),
              const Gap(UISpacing.s),
              LabelWithText(
                label: context.l10n.service_book_odometer_label,
                text: data.mileage.formatToMileage(context),
              ),
              const Gap(UISpacing.s),
              LabelWithText(
                label: context.l10n.service_book_quantity_label,
                text: data.oilQuantity.formatLiters(context),
              ),
              const Gap(UISpacing.s),
              LabelWithText(
                label: context.l10n.service_book_price_label,
                text: data.price.formatCurrency(context),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
