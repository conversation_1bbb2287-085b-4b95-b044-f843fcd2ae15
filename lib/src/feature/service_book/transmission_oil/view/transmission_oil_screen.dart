import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/service_book/transmission_oil/view/transmission_oil_wm.dart';
import 'package:sba/src/feature/service_book/transmission_oil/view/widget/transmission_oil_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

final class TransmissionOilScreenArgs {
  TransmissionOilScreenArgs({required this.vehicleId});

  final int vehicleId;
}

class TransmissionOilScreen
    extends ElementaryWidget<ITransmissionOilWidgetModel> {
  const TransmissionOilScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultTransmissionOilWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final TransmissionOilScreenArgs args;

  @override
  Widget build(ITransmissionOilWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          EntityStateNotifierBuilder(
            listenableEntityState: wm.transmissionOil,
            builder: (context, data) => SliverTitle(
              text: context.l10n.navigation_book_transmission_oil,
              padding: UISpacing.defaultElementPaddingWithoutBottom,
              action: data?.isEmpty ?? true
                  ? null
                  : FloatingActionButton.small(
                      onPressed: wm.onAdd,
                      child: const Icon(Icons.add),
                    ),
            ),
          ),
        ],
        body: EntityStateNotifierBuilder(
          listenableEntityState: wm.transmissionOil,
          builder: (context, data) => SmartListView(
            loading: data == null,
            data: data,
            skeletonData: List.filled(1, TransmissionOilData.fake()),
            emptyText: '',
            padding: UISpacing.defaultScreenPadding,
            separator: const Gap(UISpacing.ll),
            showActionWhenData: false,
            action: ActionBox(
              icon: Icons.add,
              text: context.l10n.action_add,
              onTap: wm.onAdd,
            ),
            builder: (context, data) => TransmissionOilTile(
              data: data,
              onEdit: () => wm.onEdit(data),
            ),
          ),
        ),
      ),
    );
  }
}
