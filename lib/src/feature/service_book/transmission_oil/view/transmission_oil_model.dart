import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class TransmissionOilModel extends ElementaryModel {
  TransmissionOilModel(
      {required ServiceBookRepository repository, super.errorHandler,})
      : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<List<TransmissionOilData>>> getTransmissionOil(int vehicleId) =>
      _repository.getTransmissionOilForVehicle(vehicleId: vehicleId);
}
