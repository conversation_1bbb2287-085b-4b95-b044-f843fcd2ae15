import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/vehicle_service/vehicle_service_model.dart';
import 'package:sba/src/feature/service_book/vehicle_service/vehicle_service_screen.dart';

abstract interface class IVehicleServiceWidgetModel implements IWidgetModel {
  void onEngineOilTap();

  void onTransmissionOilTap();

  void onTyresTap();

  void onOtherTap();
}

VehicleServiceWidgetModel defaultVehicleServiceWidgetModelFactory(
  BuildContext context,
) {
  return VehicleServiceWidgetModel(VehicleServiceModel());
}

class VehicleServiceWidgetModel
    extends WidgetModel<VehicleServiceScreen, VehicleServiceModel>
    implements IVehicleServiceWidgetModel {
  VehicleServiceWidgetModel(super.model);

  @override
  void onEngineOilTap() {
    EngineOilChangesRoute(vehicleId: widget.args.vehicleId).go(context);
  }

  @override
  void onOtherTap() {
    OtherServicesRoute(vehicleId: widget.args.vehicleId).go(context);
  }

  @override
  void onTransmissionOilTap() {
    TransmissionOilChangesRoute(vehicleId: widget.args.vehicleId).go(context);
  }

  @override
  void onTyresTap() {
    TyresRoute(vehicleId: widget.args.vehicleId).go(context);
  }
}
