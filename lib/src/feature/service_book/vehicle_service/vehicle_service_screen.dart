import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/vehicle_service/vehicle_service_wm.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/navigation_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class VehicleServiceScreenArgs {
  VehicleServiceScreenArgs({required this.vehicleId});

  final int vehicleId;
}

class VehicleServiceScreen
    extends ElementaryWidget<IVehicleServiceWidgetModel> {
  const VehicleServiceScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultVehicleServiceWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final VehicleServiceScreenArgs args;

  @override
  Widget build(IVehicleServiceWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_service,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: Builder(
          builder: (context) => GridView.count(
            padding: UISpacing.defaultScreenPadding,
            crossAxisCount: 2,
            mainAxisSpacing: UISpacing.s,
            crossAxisSpacing: UISpacing.s,
            childAspectRatio: 17 / 10,
            children: [
              NavigationBox(
                icon: Assets.icons.engine.svg(),
                text: context.l10n.navigation_book_engine_oil,
                onTap: wm.onEngineOilTap,
              ),
              NavigationBox(
                icon: Assets.icons.gearbox.svg(),
                text: context.l10n.navigation_book_transmission_oil,
                onTap: wm.onTransmissionOilTap,
              ),
              NavigationBox(
                icon: Assets.icons.tire.svg(),
                text: context.l10n.navigation_book_tyres,
                onTap: wm.onTyresTap,
              ),
              NavigationBox(
                icon: Assets.icons.build.svg(),
                text: context.l10n.navigation_book_other_service,
                onTap: wm.onOtherTap,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
