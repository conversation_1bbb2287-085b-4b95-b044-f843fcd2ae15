import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/tyre_brand.dart';
import 'package:sba/src/repository/general/model/tyre_size.dart';
import 'package:sba/src/repository/service_book/model/types/vehicle_tyre_type.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_option_picker.dart';
import 'package:sba/src/ui/widget/option_box.dart';

class TireForm extends StatelessWidget {
  TireForm({
    required this.onSubmitClick,
    required this.onDeleteClick,
    super.key,
    this.initialValue,
    this.brands,
    this.widths,
    this.heights,
    this.diameters,
  });

  final TyreData? initialValue;
  final List<TyreBrand>? brands;
  final List<TyreSize>? widths;
  final List<TyreSize>? heights;
  final List<TyreSize>? diameters;
  final ValueChanged<TyreData> onSubmitClick;
  final ValueChanged<TyreData> onDeleteClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  static const ({
    String brand,
    String date,
    String dealer,
    String diameter,
    String dot,
    String height,
    String mileage,
    String model,
    String type,
    String width,
  })
  _keys = (
    type: 'type',
    brand: 'brand',
    model: 'model',
    dot: 'dot',
    width: 'width',
    height: 'height',
    diameter: 'diameter',
    date: 'date',
    mileage: 'mileage',
    dealer: 'dealer',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          FormBuilderOptionPicker<VehicleTyreType>(
            name: _keys.type,
            label: context.l10n.form_tyre_type_hint,
            initialValue: initialValue?.type,
            data: VehicleTyreType.values,
            elementBuilder: (type, selected) => SizedBox.square(
              dimension: 104,
              child: OptionBox(
                icon: type.icon,
                text: type.localizedText(context),
                selected: selected,
              ),
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<TyreBrand>(
            name: _keys.brand,
            initialValue: initialValue?.brand,
            decoration: InputDecoration(
              labelText: context.l10n.form_brand,
              hintText: context.l10n.form_brand_hint,
            ),
            items:
                brands
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.name),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.model,
            initialValue: initialValue?.model,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_model,
              hintText: context.l10n.form_model_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.dot,
            initialValue: initialValue?.dot,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_dot,
              hintText: context.l10n.form_dot_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<double>(
            name: _keys.width,
            initialValue: initialValue?.width,
            decoration: InputDecoration(
              labelText: context.l10n.form_tyre_width,
              hintText: context.l10n.form_tyre_width_hint,
            ),
            items:
                widths
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it.size,
                        child: Text(it.size.toString()),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<double>(
            name: _keys.height,
            initialValue: initialValue?.height,
            decoration: InputDecoration(
              labelText: context.l10n.form_tyre_height,
              hintText: context.l10n.form_tyre_height_hint,
            ),
            items:
                heights
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it.size,
                        child: Text(it.size.toString()),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<double>(
            name: _keys.diameter,
            initialValue: initialValue?.diameter,
            decoration: InputDecoration(
              labelText: context.l10n.form_tyre_diameter,
              hintText: context.l10n.form_tyre_diameter_hint,
            ),
            items:
                diameters
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it.size,
                        child: Text(it.size.toString()),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderDateTimePicker(
            name: _keys.date,
            initialValue: initialValue?.buyDate,
            locale: context.locale,
            textInputAction: TextInputAction.next,
            inputType: InputType.date,
            decoration: InputDecoration(
              labelText: context.l10n.form_date,
              hintText: context.l10n.form_date_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.mileage,
            initialValue: initialValue?.mileage.toString(),
            keyboardType: TextInputType.number,
            textInputAction: TextInputAction.next,
            valueTransformer: (e) => num.tryParse(e ?? '')?.round(),
            decoration: InputDecoration(
              labelText: context.l10n.form_odometer,
              hintText: context.l10n.form_odometer_hint,
            ),
            validator: FormBuilderValidators.numeric(
              errorText: context.l10n.form_validation_numeric,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderTextField(
            name: _keys.dealer,
            initialValue: initialValue?.dot,
            keyboardType: TextInputType.name,
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              labelText: context.l10n.form_dealer,
              hintText: context.l10n.form_dealer_hint,
            ),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(
              initialValue != null
                  ? context.l10n.action_edit
                  : context.l10n.action_add,
            ),
          ),
          if (initialValue != null) const Gap(UISpacing.m),
          if (initialValue != null)
            FilledButton(
              onPressed: () => onDeleteClick(initialValue!),
              style: FilledButton.styleFrom(
                backgroundColor: context.theme.colorScheme.error,
              ),
              child: Text(context.l10n.action_delete),
            ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      final data = initialValue ?? TyreData.empty();
      onSubmitClick(
        data.copyWith(
          tyreType: _formKey.currentState!.value[_keys.type] as VehicleTyreType,
          tyreBrand: _formKey.currentState!.value[_keys.brand] as TyreBrand,
          tyreModel: _formKey.currentState!.value[_keys.model] as String,
          tyreDot: _formKey.currentState!.value[_keys.dot] as String,
          width: _formKey.currentState!.value[_keys.width] as double,
          height: _formKey.currentState!.value[_keys.height] as double,
          diameter: _formKey.currentState!.value[_keys.diameter] as double,
          buyDate: _formKey.currentState!.value[_keys.date] as DateTime,
          mileage: _formKey.currentState!.value[_keys.mileage] as int,
          dealer: _formKey.currentState!.value[_keys.dealer] as String,
        ),
      );
    }
  }
}
