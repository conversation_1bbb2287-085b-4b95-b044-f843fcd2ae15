import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/tyre_brand.dart';
import 'package:sba/src/repository/general/model/tyre_size.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class EditTireModel extends ElementaryModel {
  EditTireModel({
    required ServiceBookRepository serviceBookRepository,
    required GeneralRepository generalRepository,
    super.errorHandler,
  })  : _serviceBookRepository = serviceBookRepository,
        _generalRepository = generalRepository;

  final ServiceBookRepository _serviceBookRepository;
  final GeneralRepository _generalRepository;

  Future<Result<void>> createOrUpdate(TyreData data) =>
      _serviceBookRepository.crudTyres(data);

  Future<Result<void>> deleteTyre(TyreData data) =>
      _serviceBookRepository.crudTyres(data, delete: true);

  Future<List<TyreBrand>?> get tyreBrands =>
      _generalRepository.getTyres().then((e) => e.maybeValue);

  Future<List<TyreSize>?> get tyreWidths =>
      _generalRepository.getTyreWidths().then((e) => e.maybeValue);

  Future<List<TyreSize>?> get tyreHeights =>
      _generalRepository.getTyreHeights().then((e) => e.maybeValue);

  Future<List<TyreSize>?> get tyreDiameters =>
      _generalRepository.getTyreDiameters().then((e) => e.maybeValue);
}
