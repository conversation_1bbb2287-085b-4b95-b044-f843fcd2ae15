import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/tires/buy/edit/edit_tire_wm.dart';
import 'package:sba/src/feature/service_book/tires/buy/edit/widget/tire_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class EditTireScreenArgs {
  EditTireScreenArgs({required this.vehicleId, required this.data});

  final int? vehicleId;
  final TyreData? data;
}

class EditTireScreen extends ElementaryWidget<IEditTireWidgetModel> {
  const EditTireScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultEditTireWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final EditTireScreenArgs args;

  @override
  Widget build(IEditTireWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_new_tyres,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: MultiListenerRebuilder(
            listenableList: [
              wm.tires,
              wm.initialValue,
              wm.tireWidths,
              wm.tireHeights,
              wm.tireDiameters,
            ],
            builder: (context) => TireForm(
              initialValue: wm.initialValue.value,
              brands: wm.tires.value,
              widths: wm.tireWidths.value,
              heights: wm.tireHeights.value,
              diameters: wm.tireDiameters.value,
              onSubmitClick: wm.onSubmit,
              onDeleteClick: wm.onDelete,
            ),
          ),
        ),
      ),
    );
  }
}
