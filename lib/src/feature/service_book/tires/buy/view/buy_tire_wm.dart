import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/tires/buy/view/buy_tire_model.dart';
import 'package:sba/src/feature/service_book/tires/buy/view/buy_tire_screen.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';

abstract interface class IBuyTireWidgetModel implements IWidgetModel {
  EntityStateNotifier<List<TyreData>> get tires;

  void onAdd();

  void onEdit(TyreData data);
}

BuyTireWidgetModel defaultBuyTireWidgetModelFactory(
  BuildContext context,
) {
  return BuyTireWidgetModel(
    BuyTireModel(repository: get(), errorHandler: get()),
  );
}

class BuyTireWidgetModel extends WidgetModel<BuyTireScreen, BuyTireModel>
    implements IBuyTireWidgetModel {
  BuyTireWidgetModel(super.model);

  final _tires = EntityStateNotifier<List<TyreData>>();

  @override
  void initWidgetModel() {
    _syncData();
    super.initWidgetModel();
  }

  void _syncData() async {
    _tires.loading();
    final result = await model.getVehicleTires(widget.args.vehicleId);

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      await context.router.popRoute();
      return;
    }

    _tires.content(
      result.maybeValue!.sorted(
        (a, b) => b.buyDate.compareTo(a.buyDate),
      ),
    );
  }

  @override
  EntityStateNotifier<List<TyreData>> get tires => _tires;

  @override
  void onAdd() async {
    await EditNewTyreRoute(
      vehicleId: widget.args.vehicleId,
    ).push<void>(context);

    _syncData();
  }

  @override
  void onEdit(TyreData data) async {
    await EditNewTyreRoute(
      vehicleId: widget.args.vehicleId,
      $extra: data,
    ).push<void>(context);

    _syncData();
  }
}
