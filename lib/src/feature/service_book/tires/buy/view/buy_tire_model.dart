import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class BuyTireModel extends ElementaryModel {
  BuyTireModel({
    required ServiceBookRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<List<TyreData>>> getVehicleTires(int vehicleId) =>
      _repository.getTyres(vehicleId: vehicleId);
}
