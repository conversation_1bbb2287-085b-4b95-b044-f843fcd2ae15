import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/types/vehicle_tyre_type.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/circle_button.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class TireTile extends StatelessWidget {
  const TireTile({
    required this.data,
    required this.onEdit,
    super.key,
  });

  final TyreData data;
  final VoidCallback onEdit;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: UISpacing.m,
        horizontal: UISpacing.l,
      ),
      decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
      alignment: Alignment.center,
      child: Row(
        spacing: UISpacing.m,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Skeleton.replace(
            width: 40,
            height: 40,
            child: data.type?.icon ?? const SizedBox.shrink(),
          ),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  data.type?.localizedText(context) ?? '',
                  style: context.textTheme.headlineSmall,
                ),
                const Gap(UISpacing.ll),
                LabelWithText(
                  label: context.l10n.service_book_brand_label,
                  text: data.brand?.name ?? '',
                ),
                const Gap(UISpacing.s),
                LabelWithText(
                  label: context.l10n.service_book_model_label,
                  text: data.model,
                ),
                const Gap(UISpacing.s),
                LabelWithText(
                  label: context.l10n.service_book_dot_label,
                  text: data.dot,
                ),
                const Gap(UISpacing.s),
                LabelWithText(
                  label: context.l10n.service_book_size_label,
                  text: data.size,
                ),
                const Gap(UISpacing.s),
                LabelWithText(
                  label: context.l10n.service_book_buy_date_label,
                  text: data.buyDate.formatDate(context),
                ),
                const Gap(UISpacing.s),
                LabelWithText(
                  label: context.l10n.service_book_odometer_label,
                  text: data.mileage.formatToMileage(context),
                ),
                const Gap(UISpacing.s),
                LabelWithText(
                  label: context.l10n.service_book_dealer_label,
                  text: data.dealer,
                ),
              ],
            ),
          ),
          CircleButton(
            icon: const Icon(Icons.edit),
            onTap: onEdit,
          ),
        ],
      ),
    );
  }
}
