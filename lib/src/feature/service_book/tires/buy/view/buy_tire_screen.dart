import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/service_book/tires/buy/view/buy_tire_wm.dart';
import 'package:sba/src/feature/service_book/tires/buy/view/widget/tire_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

final class BuyTireScreenArgs {
  BuyTireScreenArgs({required this.vehicleId});

  final int vehicleId;
}

class BuyTireScreen extends ElementaryWidget<IBuyTireWidgetModel> {
  const BuyTireScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultBuyTireWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final BuyTireScreenArgs args;

  @override
  Widget build(IBuyTireWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          EntityStateNotifierBuilder(
            listenableEntityState: wm.tires,
            builder: (context, data) => SliverTitle(
              text: context.l10n.navigation_book_new_tyres,
              padding: UISpacing.defaultElementPaddingWithoutBottom,
              action: data?.isEmpty ?? true
                  ? null
                  : FloatingActionButton.small(
                      onPressed: wm.onAdd,
                      child: const Icon(Icons.add),
                    ),
            ),
          ),
        ],
        body: EntityStateNotifierBuilder(
          listenableEntityState: wm.tires,
          builder: (context, data) => SmartListView(
            loading: data == null,
            data: data,
            skeletonData: List.filled(1, TyreData.fake()),
            emptyText: '',
            padding: UISpacing.defaultScreenPadding,
            separator: const Gap(UISpacing.ll),
            showActionWhenData: false,
            action: ActionBox(
              icon: Icons.add,
              text: context.l10n.action_add,
              onTap: wm.onAdd,
            ),
            builder: (context, data) => TireTile(
              data: data,
              onEdit: () => wm.onEdit(data),
            ),
          ),
        ),
      ),
    );
  }
}
