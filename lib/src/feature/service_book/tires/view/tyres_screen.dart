import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/tires/view/tyres_wm.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/navigation_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class TyresScreenArgs {
  TyresScreenArgs({required this.vehicleId});

  final int vehicleId;
}

class TyresScreen extends ElementaryWidget<ITyresWidgetModel> {
  const TyresScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultTyresWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final TyresScreenArgs args;

  @override
  Widget build(ITyresWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_tyres,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: Builder(
          builder: (context) => GridView.count(
            padding: UISpacing.defaultScreenPadding,
            crossAxisCount: 2,
            mainAxisSpacing: UISpacing.s,
            crossAxisSpacing: UISpacing.s,
            childAspectRatio: 17 / 10,
            children: [
              NavigationBox(
                icon: Assets.icons.addShoppingCart.svg(),
                text: context.l10n.navigation_book_new_tyres,
                onTap: wm.onNewTap,
              ),
              NavigationBox(
                icon: Assets.icons.changeCircle.svg(),
                text: context.l10n.navigation_book_swap_tyres,
                onTap: wm.onSwapTap,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
