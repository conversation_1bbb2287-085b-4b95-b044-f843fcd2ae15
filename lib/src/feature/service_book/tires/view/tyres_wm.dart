import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/tires/view/tyres_model.dart';
import 'package:sba/src/feature/service_book/tires/view/tyres_screen.dart';

abstract interface class ITyresWidgetModel implements IWidgetModel {
  void onNewTap();

  void onSwapTap();
}

TyresWidgetModel defaultTyresWidgetModelFactory(
  BuildContext context,
) {
  return TyresWidgetModel(TyresModel());
}

class TyresWidgetModel extends WidgetModel<TyresScreen, TyresModel>
    implements ITyresWidgetModel {
  TyresWidgetModel(super.model);

  @override
  void onNewTap() {
    NewTyresRoute(vehicleId: widget.args.vehicleId).go(context);
  }

  @override
  void onSwapTap() {
    TyreSwapsRoute(vehicleId: widget.args.vehicleId).go(context);
  }
}
