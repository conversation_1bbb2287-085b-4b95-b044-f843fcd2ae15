import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/service_book/tires/swap/view/swap_tire_model.dart';
import 'package:sba/src/feature/service_book/tires/swap/view/swap_tire_screen.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';

abstract interface class ISwapTireWidgetModel implements IWidgetModel {
  EntityStateNotifier<List<TyreSwapData>> get tires;

  void onAdd();

  void onEdit(TyreSwapData data);
}

SwapTireWidgetModel defaultSwapTireWidgetModelFactory(
  BuildContext context,
) {
  return SwapTireWidgetModel(
    SwapTireModel(repository: get(), errorHandler: get()),
  );
}

class SwapTireWidgetModel extends WidgetModel<SwapTireScreen, SwapTireModel>
    implements ISwapTireWidgetModel {
  SwapTireWidgetModel(super.model);

  final _tires = EntityStateNotifier<List<TyreSwapData>>();

  @override
  void initWidgetModel() {
    _syncData();
    super.initWidgetModel();
  }

  void _syncData() async {
    _tires.loading();
    final result = await model.getTireSwaps(widget.args.vehicleId);

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      await context.router.popRoute();
      return;
    }

    _tires.content(
      result.maybeValue!.sorted(
        (a, b) => b.swapDate.compareTo(a.swapDate),
      ),
    );
  }

  @override
  EntityStateNotifier<List<TyreSwapData>> get tires => _tires;

  @override
  void onAdd() async {
    await EditTyreSwapRoute(
      vehicleId: widget.args.vehicleId,
    ).push<void>(context);

    _syncData();
  }

  @override
  void onEdit(TyreSwapData data) async {
    await EditTyreSwapRoute(
      vehicleId: widget.args.vehicleId,
      $extra: data,
    ).push<void>(context);

    _syncData();
  }
}
