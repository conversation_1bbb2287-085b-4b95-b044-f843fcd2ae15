import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/types/vehicle_tyre_type.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/circle_button.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class TireSwapTile extends StatelessWidget {
  const TireSwapTile({
    required this.data,
    required this.onEdit,
    super.key,
  });

  final TyreSwapData data;
  final VoidCallback onEdit;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: UISpacing.m,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          data.swapDate.formatDate(context),
          style: context.textTheme.headlineMedium,
        ),
        Container(
          padding: const EdgeInsets.symmetric(
            vertical: UISpacing.m,
            horizontal: UISpacing.l,
          ),
          decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
          alignment: Alignment.center,
          child: Row(
            spacing: UISpacing.m,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Skeleton.replace(
                width: 40,
                height: 40,
                child: data.tyres?.type?.icon ?? const SizedBox.shrink(),
              ),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      data.tyres?.type?.localizedText(context) ?? '',
                      style: context.textTheme.headlineSmall,
                    ),
                    const Gap(UISpacing.ll),
                    LabelWithText(
                      label: context.l10n.service_book_brand_label,
                      text: data.tyres?.brand?.name ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_model_label,
                      text: data.tyres?.model ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_dot_label,
                      text: data.tyres?.dot ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_buy_date_label,
                      text: data.tyres?.buyDate.formatDate(context) ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_odometer_label,
                      text: data.tyres?.mileage.formatToMileage(context) ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_dealer_label,
                      text: data.tyres?.dealer ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.service_book_changed_by_label,
                      text: data.autoService,
                    ),
                  ],
                ),
              ),
              CircleButton(
                icon: const Icon(Icons.edit),
                onTap: onEdit,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
