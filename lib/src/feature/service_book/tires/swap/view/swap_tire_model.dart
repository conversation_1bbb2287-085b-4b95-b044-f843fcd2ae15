import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class SwapTireModel extends ElementaryModel {
  SwapTireModel({
    required ServiceBookRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ServiceBookRepository _repository;

  Future<Result<List<TyreSwapData>>> getTireSwaps(int vehicleId) =>
      _repository.getTyreSwaps(vehicleId: vehicleId);
}
