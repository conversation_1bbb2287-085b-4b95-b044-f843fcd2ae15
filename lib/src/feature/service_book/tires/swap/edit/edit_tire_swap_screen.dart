import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/service_book/tires/swap/edit/edit_tire_swap_wm.dart';
import 'package:sba/src/feature/service_book/tires/swap/edit/widget/tire_swap_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class EditTireSwapScreenArgs {
  EditTireSwapScreenArgs({required this.vehicleId, required this.data});

  final int? vehicleId;
  final TyreSwapData? data;
}

class EditTireSwapScreen extends ElementaryWidget<IEditTireSwapWidgetModel> {
  const EditTireSwapScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultEditTireSwapWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final EditTireSwapScreenArgs args;

  @override
  Widget build(IEditTireSwapWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_book_swap_tyres,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: DoubleSourceBuilder(
            firstSource: wm.tires,
            secondSource: wm.initialValue,
            builder: (context, tires, data) => TireSwapForm(
              initialValue: data,
              tires: tires,
              onSubmitClick: wm.onSubmit,
              onDeleteClick: wm.onDelete,
            ),
          ),
        ),
      ),
    );
  }
}
