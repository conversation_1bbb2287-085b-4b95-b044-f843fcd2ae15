import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/service_book/tires/swap/edit/edit_tire_swap_model.dart';
import 'package:sba/src/feature/service_book/tires/swap/edit/edit_tire_swap_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';
import 'package:toastification/toastification.dart';

abstract interface class IEditTireSwapWidgetModel implements IWidgetModel {
  StateNotifier<TyreSwapData?> get initialValue;

  StateNotifier<List<TyreData>?> get tires;

  void onDelete(TyreSwapData data);

  void onSubmit(TyreSwapData data);
}

EditTireSwapWidgetModel defaultEditTireSwapWidgetModelFactory(
  BuildContext context,
) {
  return EditTireSwapWidgetModel(
    EditTireSwapModel(
      serviceBookRepository: get(),
      errorHandler: get(),
    ),
  );
}

class EditTireSwapWidgetModel
    extends WidgetModel<EditTireSwapScreen, EditTireSwapModel>
    implements IEditTireSwapWidgetModel {
  EditTireSwapWidgetModel(super.model);

  final _initialValue = StateNotifier<TyreSwapData?>();
  final _tires = StateNotifier<List<TyreData>?>();

  @override
  void initWidgetModel() async {
    _initialValue.accept(widget.args.data);
    _tires.accept(await model.getTires(widget.args.vehicleId ?? 0));

    super.initWidgetModel();
  }

  @override
  void onSubmit(TyreSwapData data) async {
    final value = data.copyWith(vehicleId: widget.args.vehicleId);

    await context.showLoadingDialog();
    final result = await model.createOrUpdate(value);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context.showToast(
      type: ToastificationType.success,
      title: data.id == null
          ? context.l10n.message_success_create
          : context.l10n.message_success_edit,
    );

    await context.router.popRoute();
  }

  @override
  void onDelete(TyreSwapData data) async {
    Future<void> delete(TyreSwapData data) async {
      await context.showLoadingDialog();
      final result = await model.deleteTyre(data);
      context.hideLoadingDialog();

      if (result is Failure) {
        await context.showGeneralErrorDialog(failure: result);
        return;
      }

      context.showToast(
        type: ToastificationType.success,
        title: context.l10n.message_success_delete,
      );

      await context.router.popRoute();
    }

    await context.showMessageDialog(
      builder: (BuildContext context) => MessageDialog.action(
        type: MessageDialogType.error,
        title: context.l10n.message_delete_title,
        text: context.l10n.message_delete_text,
        primaryActionText: context.l10n.action_delete,
        secondaryActionText: context.l10n.action_cancel,
        primaryAction: () => delete(initialValue.value!),
      ),
    );
  }

  @override
  StateNotifier<TyreSwapData?> get initialValue => _initialValue;

  @override
  StateNotifier<List<TyreData>?> get tires => _tires;
}
