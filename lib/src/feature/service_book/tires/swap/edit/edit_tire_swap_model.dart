import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/repository/service_book/service_book_repository.dart';

class EditTireSwapModel extends ElementaryModel {
  EditTireSwapModel({
    required ServiceBookRepository serviceBookRepository,
    super.errorHandler,
  }) : _serviceBookRepository = serviceBookRepository;

  final ServiceBookRepository _serviceBookRepository;

  Future<Result<void>> createOrUpdate(TyreSwapData data) =>
      _serviceBookRepository.crudTyreSwap(data);

  Future<Result<void>> deleteTyre(TyreSwapData data) =>
      _serviceBookRepository.crudTyreSwap(data, delete: true);

  Future<List<TyreData>?> getTires(int vehicleId) => _serviceBookRepository
      .getTyres(vehicleId: vehicleId)
      .then((e) => e.maybeValue);
}
