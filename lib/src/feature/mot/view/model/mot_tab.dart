import 'package:flutter/material.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/avi/model/types/avi_status.dart';

enum MotTabType {
  active,
  completed,
  cancelled,
}

final class MotTab {
  const MotTab({required this.type, required this.status});

  final MotTabType type;
  final Set<AviStatus> status;

  String formattedTitle(BuildContext context) => switch (type) {
        MotTabType.active => context.l10n.mot_status_active_many,
        MotTabType.cancelled => context.l10n.mot_status_cancelled_many,
        MotTabType.completed => context.l10n.mot_status_completed_many,
      };
}
