import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/mot/request/mot_request_wm.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/avi/model/avi_point_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_vehicle_picker.dart';
import 'package:sba/src/ui/widget/form_builder_visibility.dart';

class ReservationForm extends StatelessWidget {
  ReservationForm({
    required this.onSubmit,
    required this.onDocumentTap,
    super.key,
    this.vehicles,
    this.point,
  });

  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  final ValueChanged<BookingRecord> onSubmit;
  final VoidCallback onDocumentTap;
  final List<VehicleData>? vehicles;
  final AviPointData? point;
  final ValueNotifier<bool> _showVehiclePicker = ValueNotifier(false);

  static const ({String plateNumber, String time, String vehicle}) _keys = (
    time: 'time',
    vehicle: 'vehicle',
    plateNumber: 'plate_number',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            point?.name ?? '',
            style: context.textTheme.headlineLarge,
            textAlign: TextAlign.center,
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<AviPointSlotData>(
            name: _keys.time,
            decoration: InputDecoration(
              labelText: context.l10n.form_time,
              hintText: context.l10n.form_time_hint,
            ),
            items: point?.freeCalendarSlots
                    ?.map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it.slot),
                      ),
                    )
                    .toList() ??
                List.empty(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.l),
          FormBuilderVehiclePicker(
            name: _keys.vehicle,
            vehicles: vehicles,
            onOtherSelected: (e) => _showVehiclePicker.value = e,
          ),
          FormBuilderVisibility(
            visible: _showVehiclePicker,
            padding: const EdgeInsets.only(top: UISpacing.l),
            child: FormBuilderTextField(
              name: _keys.plateNumber,
              decoration: InputDecoration(
                labelText: context.l10n.form_plate_number,
                hintText: context.l10n.form_plate_number_hint,
              ),
              textInputAction: TextInputAction.next,
              validator: FormBuilderValidators.licensePlate(
                errorText: context.l10n.form_validation_license_plate,
              ),
            ),
          ),
          const Gap(UISpacing.l),
          Text(
            context.l10n.mot_request_valid_caption,
            style: context.textTheme.bodySmall,
          ),
          const Gap(UISpacing.ll),
          TextButton(
            onPressed: onDocumentTap,
            child: Text(
              context.l10n.mot_documents_button,
              textAlign: TextAlign.start,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_send_request),
          ),
        ],
      ),
    );
  }

  void _onSubmit() async {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmit(
        (
          slot: _formKey.currentState!.value[_keys.time] as AviPointSlotData,
          plateNumber: (_formKey.currentState!.value[_keys.vehicle] as VehicleData?)?.plateNumber ??
              _formKey.currentState!.value[_keys.plateNumber] as String,
        ),
      );
    }
  }
}
