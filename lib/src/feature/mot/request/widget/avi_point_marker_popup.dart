import 'package:flutter/material.dart';
import 'package:sba/src/repository/avi/model/avi_point_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class AviPointMarkerPopup extends StatelessWidget {
  const AviPointMarkerPopup({required this.data, super.key});

  final AviPointData data;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(
        maxWidth: 250,
      ),
      child: Card(
        clipBehavior: Clip.hardEdge,
        color: context.theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: UISpacing.m, horizontal: UISpacing.l),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                data.name ?? '',
                style: context.textTheme.headlineSmall,
                textAlign: TextAlign.center,
              ),
              if (data.address != null)
                Item(
                  icon: Icons.home,
                  label: data.address!,
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class Item extends StatelessWidget {
  const Item({required this.icon, required this.label, super.key});

  final IconData icon;
  final String label;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      spacing: UISpacing.s,
      children: [
        Icon(
          icon,
          color: context.theme.colorScheme.primary,
        ),
        Expanded(
          child: Text(
            label,
            style: context.textTheme.bodySmall,
          ),
        ),
      ],
    );
  }
}
