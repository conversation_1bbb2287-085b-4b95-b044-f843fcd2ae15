import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/mot/request/mot_request_wm.dart';
import 'package:sba/src/feature/mot/request/widget/avi_point_marker_popup.dart';
import 'package:sba/src/feature/mot/request/widget/reservation_form.dart';
import 'package:sba/src/feature/mot/request/widget/search_center_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/focus_on_first_build.dart';
import 'package:sba/src/ui/widget/open_street_map.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class MotRequestScreen extends ElementaryWidget<IMotRequestWidgetModel> {
  const MotRequestScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultMotRequestWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IMotRequestWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_mot_request,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            spacing: UISpacing.xxl,
            children: [
              StateNotifierBuilder(
                listenableState: wm.places,
                builder: (context, places) => SearchCenterForm(
                  places: places,
                  onSubmit: wm.checkMotCenter,
                ),
              ),
              StateNotifierBuilder(
                listenableState: wm.foundCenters,
                builder: (context, data) => switch (data) {
                  null => const SizedBox.shrink(),
                  [] => FocusOnFirstBuild(
                      child: Center(
                        child: Text(context.l10n.empty_search),
                      ),
                    ),
                  _ => FocusOnFirstBuild(
                      child: OpenStreetMap(
                        markers: data,
                        enableInteractions: false,
                        animateMapBoundsToFitMarkers: true,
                        onMarkerTap: (marker) => wm.selectCenter(marker.data),
                        popupBuilder: (context, marker) => AviPointMarkerPopup(data: marker.data),
                      ),
                    )
                },
              ),
              DoubleSourceBuilder(
                firstSource: wm.selectedCenter,
                secondSource: wm.vehicles,
                builder: (context, data, vehicles) => AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  child: data == null
                      ? const SizedBox.shrink()
                      : FocusOnFirstBuild(
                          child: ReservationForm(
                            vehicles: vehicles,
                            point: data,
                            onSubmit: wm.book,
                            onDocumentTap: wm.onDocumentTap,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
