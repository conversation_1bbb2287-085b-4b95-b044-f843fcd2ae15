import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/avi/avi_repository.dart';
import 'package:sba/src/repository/avi/model/avi_point_data.dart';
import 'package:sba/src/repository/avi/model/book_avi_data.dart';
import 'package:sba/src/repository/avi/model/search_avi_data.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class MotRequestModel extends ElementaryModel {
  MotRequestModel({
    required AVIRepository aviRepository,
    required GeneralRepository generalRepository,
    required VehicleRepository vehicleRepository,
    super.errorHandler,
  })  : _aviRepository = aviRepository,
        _generalRepository = generalRepository,
        _vehicleRepository = vehicleRepository;

  final AVIRepository _aviRepository;
  final GeneralRepository _generalRepository;
  final VehicleRepository _vehicleRepository;

  Future<List<Place>?> get places => _generalRepository.getPlaces().then((e) => e.maybeValue);

  Future<List<VehicleData>?> get vehicles => _vehicleRepository.getVehicles();

  Future<Result<List<AviPointData>>> findAVIPoint(SearchAviData data) => _aviRepository.findAVIPoint(data);

  Future<Result<void>> book(BookAviData data) => _aviRepository.bookAVI(data);
}
