import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/repository/subscription/subscription_repository.dart';

class BuySubscriptionModel extends ElementaryModel {
  BuySubscriptionModel({
    required SubscriptionRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final SubscriptionRepository _repository;

  Future<Result<List<SubscriptionProductData>>> get subscriptions =>
      _repository.getSubscriptionProducts();

  Future<SubscriptionData?> subscriptionById(int id) =>
      _repository.getSubscription(id).then((e) => e.maybeValue);
}
