import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/subscription/buy/widget/subscription_plan_card.dart';

import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/smart_pageview.dart';

class SubscriptionItemsTab extends StatelessWidget {
  const SubscriptionItemsTab({
    required this.hint,
    required this.onSelect,
    super.key,
    this.data,
  });

  final String hint;
  final List<SubscriptionProductData>? data;
  final ValueChanged<SubscriptionProductData> onSelect;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: UISpacing.l, bottom: UISpacing.xxl),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: UISpacing.defaultElementHorizontalPadding,
            child: Assets.images.subscriptionPartners.image(),
          ),
          const Gap(UISpacing.l),
          Padding(
            padding: UISpacing.defaultElementHorizontalPadding,
            child: Text(hint, style: context.textTheme.bodySmall),
          ),
          const Gap(UISpacing.xl),
          SmartPageView(
            key: key,
            loading: data == null,
            data: data,
            skeletonData: List.filled(2, SubscriptionProductData.fake()),
            builder: (context, data) => SubscriptionPlanCard(
              data: data,
              onRequest: () => onSelect(data),
            ),
          ),
        ],
      ),
    );
  }
}
