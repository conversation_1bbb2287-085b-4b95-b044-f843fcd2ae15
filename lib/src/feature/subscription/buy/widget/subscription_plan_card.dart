import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/iterable_extension.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SubscriptionPlanCard extends StatelessWidget {
  const SubscriptionPlanCard({
    required this.data,
    required this.onRequest,
    super.key,
  });

  final SubscriptionProductData data;
  final VoidCallback onRequest;

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.hardEdge,
      color: context.theme.colorScheme.surface,
      shape: RoundedRectangleBorder(
        side: const BorderSide(
          color: UIColors.menuBackground,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.zero,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Skeleton.leaf(
              child: ColoredBox(
                color: context.theme.colorScheme.primary,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: UISpacing.xl,
                    horizontal: UISpacing.l,
                  ),
                  child: Text(
                    data.price.formatCurrency(context),
                    textAlign: TextAlign.center,
                    style: context.textTheme.headlineLarge?.copyWith(
                      color: context.theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
              ),
            ),
            Skeleton.leaf(
              child: ColoredBox(
                color: data.type?.color ?? UIColors.primary,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: UISpacing.m,
                    horizontal: UISpacing.l,
                  ),
                  child: Text(
                    data.type?.localizedName(context) ?? '',
                    textAlign: TextAlign.center,
                    style: context.textTheme.headlineMedium?.copyWith(
                      color: context.theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
              ),
            ),
            ...data.coverage
                .map(
                  (e) => _DescriptionItem(
                    item: e,
                  ) as Widget,
                )
                .intersperse(const Divider()),
            Padding(
              padding: const EdgeInsets.all(UISpacing.ll),
              child: FilledButton(
                onPressed: onRequest,
                child: Text(context.l10n.action_request),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _DescriptionItem extends StatelessWidget {
  const _DescriptionItem({required this.item});

  final SubscriptionCoverage item;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: UISpacing.m,
        horizontal: UISpacing.l,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            item.coverage,
            style: context.textTheme.headlineSmall,
          ),
          const Gap(UISpacing.xs),
          Text(
            item.coverageText,
            style: context.textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}
