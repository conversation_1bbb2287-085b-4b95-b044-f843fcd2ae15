import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/subscription/buy/form/buy_subscription_form.dart';
import 'package:sba/src/feature/subscription/buy/form/buy_subscription_form_model.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/payment/model/payment_status.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_request_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_delivery_type.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_payment_type.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';

abstract interface class IBuySubscriptionFormWidgetModel
    implements IWidgetModel {
  StateNotifier<UserData> get user;

  StateNotifier<List<Place>> get places;

  StateNotifier<List<VehicleData>> get vehicles;

  StateNotifier<List<SubscriptionDeliveryType>> get deliveryTypes;

  StateNotifier<SubscriptionDeliveryType> get selectedDeliveryType;

  StateNotifier<List<SubscriptionPackage>> get additionalPackage;

  StateNotifier<List<SubscriptionPackage>> get selectedAdditionalPackage;

  StateNotifier<List<SubscriptionPaymentType>> get paymentTypes;

  StateNotifier<SubscriptionPaymentType> get selectedPaymentType;

  void onDeliveryTypeChanged(SubscriptionDeliveryType? value);

  void onAdditionalPackageChanged(List<SubscriptionPackage>? value);

  void onPaymentTypeChanged(SubscriptionPaymentType? value);

  void onSubmit(SubscriptionRequestData data);
}

BuySubscriptionFormWidgetModel defaultBuySubscriptionFormWidgetModelFactory(
  BuildContext context,
) {
  return BuySubscriptionFormWidgetModel(
    BuySubscriptionFormModel(
      subscriptionRepository: get(),
      userRepository: get(),
      generalRepository: get(),
      vehicleRepository: get(),
      errorHandler: get(),
    ),
  );
}

class BuySubscriptionFormWidgetModel
    extends WidgetModel<BuySubscriptionFormWidget, BuySubscriptionFormModel>
    implements IBuySubscriptionFormWidgetModel {
  BuySubscriptionFormWidgetModel(super.model);

  final _user = StateNotifier<UserData>();
  final _places = StateNotifier<List<Place>>();
  final _vehicles = StateNotifier<List<VehicleData>>();

  final _deliveryTypes =
      StateNotifier<List<SubscriptionDeliveryType>>(initValue: _types);

  final _selectedDeliveryType = StateNotifier<SubscriptionDeliveryType>(
    initValue: SubscriptionDeliveryType.office,
  );
  final _additionalPackage = StateNotifier<List<SubscriptionPackage>>();
  final _selectedAdditionalPackage = StateNotifier<List<SubscriptionPackage>>();

  final _paymentTypes =
      StateNotifier<List<SubscriptionPaymentType>>(initValue: _typesPayment);
  final _selectedPaymentType = StateNotifier<SubscriptionPaymentType>(
    initValue: SubscriptionPaymentType.delivery,
  );

  @override
  void initWidgetModel() {
    _loadData();
    super.initWidgetModel();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  void _loadData() async {
    _user.accept(await model.user);
    _places.accept(await model.places);
    _vehicles.accept(await model.vehicles);

    final package = widget.data.packageWith(widget.renewData != null);
    _additionalPackage.accept(package == null ? [] : [package]);
  }

  @override
  void onDeliveryTypeChanged(SubscriptionDeliveryType? value) {
    _selectedDeliveryType.accept(value);
  }

  @override
  void onAdditionalPackageChanged(List<SubscriptionPackage>? value) {
    _selectedAdditionalPackage.accept(value);
  }

  @override
  void onPaymentTypeChanged(SubscriptionPaymentType? value) {
    _selectedPaymentType.accept(value);
  }

  @override
  void onSubmit(SubscriptionRequestData data) async {
    await context.showLoadingDialog();
    final result = await model.createRequest(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      return;
    }

    await context.showLoadingDialog();
    final payResult = await model.payRequest(result.maybeValue!);
    context.hideLoadingDialog();

    if (payResult is Failure) {
      await context.showGeneralErrorDialog(failure: payResult as Failure);
    }

    final paid = payResult.maybeValue?.isPaid ?? false;

    await context.showMessageDialog(
      builder: (context) => MessageDialog.info(
        type: MessageDialogType.success,
        title: context.l10n.navigation_buy_subscription,
        text: paid
            ? context.l10n.message_success_paid_subscription
            : context.l10n.message_success_subscription,
        actionText: context.l10n.action_ok,
        action: () => this.context.navigator.maybePop(),
      ),
    );
  }

  @override
  StateNotifier<List<Place>> get places => _places;

  @override
  StateNotifier<UserData> get user => _user;

  @override
  StateNotifier<List<VehicleData>> get vehicles => _vehicles;

  @override
  StateNotifier<List<SubscriptionDeliveryType>> get deliveryTypes =>
      _deliveryTypes;

  @override
  StateNotifier<SubscriptionDeliveryType> get selectedDeliveryType =>
      _selectedDeliveryType;

  @override
  StateNotifier<List<SubscriptionPackage>> get additionalPackage =>
      _additionalPackage;

  @override
  StateNotifier<List<SubscriptionPackage>> get selectedAdditionalPackage =>
      _selectedAdditionalPackage;

  @override
  StateNotifier<List<SubscriptionPaymentType>> get paymentTypes =>
      _paymentTypes;

  @override
  StateNotifier<SubscriptionPaymentType> get selectedPaymentType =>
      _selectedPaymentType;
}

const List<SubscriptionDeliveryType> _types = [
  SubscriptionDeliveryType.office,
  SubscriptionDeliveryType.address,
];

const List<SubscriptionPaymentType> _typesPayment = [
  SubscriptionPaymentType.delivery,
  SubscriptionPaymentType.card,
  SubscriptionPaymentType.bank,
];
