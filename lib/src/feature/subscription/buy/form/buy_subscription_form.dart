import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:gap/gap.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/delivery/widget/address/address_delivery_form.dart';
import 'package:sba/src/delivery/widget/office/office_delivery_form.dart';
import 'package:sba/src/feature/subscription/buy/form/buy_subscription_form_wm.dart';
import 'package:sba/src/feature/subscription/buy/form/widget/additional_package.dart';
import 'package:sba/src/feature/subscription/buy/form/widget/data_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_request_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_category.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_delivery_type.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_payment_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_custom_checkbox_group.dart';
import 'package:sba/src/ui/widget/form_builder_custom_radio_group.dart';

class BuySubscriptionFormWidget
    extends ElementaryWidget<IBuySubscriptionFormWidgetModel> {
  BuySubscriptionFormWidget({
    required this.data,
    this.renewData,
    Key? key,
    WidgetModelFactory wmFactory = defaultBuySubscriptionFormWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final SubscriptionData? renewData;
  final SubscriptionProductData data;
  final GlobalKey<DataFormState> _dataFormKey = GlobalKey();
  final GlobalKey<OfficeDeliveryFormState> _officeDeliveryFormKey = GlobalKey();
  final GlobalKey<AddressDeliveryFormState> _addressDeliveryFormKey =
      GlobalKey();

  @override
  Widget build(IBuySubscriptionFormWidgetModel wm) {
    return Builder(
      builder: (
        context,
      ) =>
          Padding(
        padding: context.viewInsets,
        child: SingleChildScrollView(
          controller: ModalScrollController.of(context),
          padding: UISpacing.defaultScreenPadding,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                renewData == null
                    ? context.l10n.subscription_order_title(
                        data.category?.localizedName(context) ?? '',
                      )
                    : context.l10n.subscription_renew_title(
                        data.category?.localizedName(context) ?? '',
                      ),
                style: context.textTheme.headlineLarge,
                textAlign: TextAlign.center,
              ),
              const Gap(UISpacing.xl),
              TripleSourceBuilder(
                firstSource: wm.user,
                secondSource: wm.places,
                thirdSource: wm.vehicles,
                builder: (context, user, places, vehicles) => DataForm(
                  key: _dataFormKey,
                  data: user,
                  cities: places,
                  vehicles: vehicles,
                  vehicle: renewData?.vehicle,
                  showCompanyData:
                      data.category == SubscriptionCategory.subscription,
                ),
              ),
              const Gap(UISpacing.l),
              StateNotifierBuilder(
                listenableState: wm.additionalPackage,
                builder: (context, package) =>
                    FormBuilderCustomCheckboxGroup<SubscriptionPackage>(
                  name: '',
                  label: context.l10n.form_additional_package,
                  options: package
                          ?.map(
                            (e) => FormBuilderFieldOption(
                              value: e,
                              child: AdditionalPackage(data: e),
                            ),
                          )
                          .toList() ??
                      [],
                  onChanged: wm.onAdditionalPackageChanged,
                ),
              ),
              const Gap(UISpacing.l),
              DoubleSourceBuilder(
                firstSource: wm.deliveryTypes,
                secondSource: wm.selectedDeliveryType,
                builder: (
                  context,
                  types,
                  selection,
                ) =>
                    FormBuilderCustomRadioGroup<SubscriptionDeliveryType>(
                  name: '',
                  label: context.l10n.form_delivery_type,
                  initialValue: selection,
                  orientation: OptionsOrientation.vertical,
                  options: types
                          ?.map(
                            (e) => FormBuilderFieldOption(
                              value: e,
                              child: Text(
                                e.localizedName(context),
                              ),
                            ),
                          )
                          .toList() ??
                      [],
                  onChanged: wm.onDeliveryTypeChanged,
                ),
              ),
              const Gap(UISpacing.l),
              StateNotifierBuilder(
                listenableState: wm.selectedDeliveryType,
                builder: (context, type) => switch (type) {
                  SubscriptionDeliveryType.address => AddressDeliveryForm(
                      key: _addressDeliveryFormKey,
                    ),
                  SubscriptionDeliveryType.office => OfficeDeliveryForm(
                      key: _officeDeliveryFormKey,
                    ),
                  _ => const SizedBox.shrink(),
                },
              ),
              const Gap(UISpacing.l),
              DoubleSourceBuilder(
                firstSource: wm.paymentTypes,
                secondSource: wm.selectedPaymentType,
                builder: (context, types, selection) =>
                    FormBuilderCustomRadioGroup<SubscriptionPaymentType>(
                  name: '',
                  label: context.l10n.form_payment_type,
                  initialValue: selection,
                  orientation: OptionsOrientation.vertical,
                  options: types
                          ?.map(
                            (e) => FormBuilderFieldOption(
                              value: e,
                              child: Text(
                                e.localizedName(context),
                              ),
                            ),
                          )
                          .toList() ??
                      List.empty(),
                  onChanged: wm.onPaymentTypeChanged,
                ),
              ),
              const Gap(UISpacing.xl),
              FilledButton(
                onPressed: () => saveAndValidate(wm),
                child: Text(context.l10n.action_make_order),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void saveAndValidate(IBuySubscriptionFormWidgetModel wm) {
    final userData = _dataFormKey.currentState?.saveAndValidate();
    final address = _addressDeliveryFormKey.currentState?.saveAndValidate();
    final office = _officeDeliveryFormKey.currentState?.saveAndValidate();
    final deliveryType = wm.selectedDeliveryType.value;
    final additionalPackage = wm.selectedAdditionalPackage.value;
    final paymentType = wm.selectedPaymentType.value;

    if (userData == null ||
        (address == null && office == null) ||
        deliveryType == null) {
      return;
    }

    wm.onSubmit(
      SubscriptionRequestData(
        id: null,
        oldCardId: renewData?.id,
        subscriptionData: data,
        company: userData.company,
        eik: userData.eik,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        phone: userData.phone,
        place: userData.city,
        address: userData.address,
        vehicle: userData.vehicle,
        status: null,
        additionalPackage: additionalPackage,
        deliveryType: deliveryType,
        deliveryOffice: office,
        addressDeliveryData: address,
        paymentType: paymentType,
        paymentStatus: null,
      ),
    );
  }
}
