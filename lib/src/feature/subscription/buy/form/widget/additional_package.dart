import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/num_extension.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class AdditionalPackage extends StatelessWidget {
  const AdditionalPackage({required this.data, super.key});

  final SubscriptionPackage data;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            data.name,
            style: context.textTheme.bodySmall,
          ),
        ),
        const Gap(UISpacing.s),
        Text(
          data.total.formatCurrency(context),
          style: context.textTheme.headlineSmall,
        ),
      ],
    );
  }
}
