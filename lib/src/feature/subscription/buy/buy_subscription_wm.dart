import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/subscription/buy/buy_subscription_model.dart';
import 'package:sba/src/feature/subscription/buy/buy_subscription_screen.dart';
import 'package:sba/src/feature/subscription/buy/form/buy_subscription_form.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_category.dart';

abstract interface class IBuySubscriptionWidgetModel implements IWidgetModel {
  StateNotifier<List<SubscriptionProductData>?> get subscriptions;

  StateNotifier<List<SubscriptionProductData>?> get memberships;

  void onSubscriptionTap(SubscriptionProductData data);
}

BuySubscriptionWidgetModel defaultBuySubscriptionWidgetModelFactory(
  BuildContext context,
) {
  return BuySubscriptionWidgetModel(
    BuySubscriptionModel(
      repository: get(),
      errorHandler: get(),
    ),
  );
}

class BuySubscriptionWidgetModel
    extends WidgetModel<BuySubscriptionScreen, BuySubscriptionModel>
    implements IBuySubscriptionWidgetModel {
  BuySubscriptionWidgetModel(super.model);

  final _subscriptions = StateNotifier<List<SubscriptionProductData>?>();
  final _memberships = StateNotifier<List<SubscriptionProductData>?>();

  @override
  void initWidgetModel() {
    super.initWidgetModel();
    _loadData();
  }

  void _loadData() async {
    final result = await model.subscriptions;
    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
      return;
    }

    _subscriptions.accept(
      result.maybeValue
          ?.where((e) => e.category == SubscriptionCategory.subscription)
          .sorted((p, n) => p.price.compareTo(n.price))
          .toList(),
    );

    _memberships.accept(
      result.maybeValue
          ?.where((e) => e.category == SubscriptionCategory.membership)
          .sorted((p, n) => p.price.compareTo(n.price))
          .toList(),
    );
  }

  @override
  void onSubscriptionTap(SubscriptionProductData data) async {
    final renewData = widget.args.renewData != null
        ? await model.subscriptionById(widget.args.renewData!)
        : null;

    await context.showModalBottomSheet<void>(
      builder: (context) => BuySubscriptionFormWidget(
        renewData: renewData,
        data: data,
      ),
    );
  }

  @override
  StateNotifier<List<SubscriptionProductData>?> get subscriptions =>
      _subscriptions;

  @override
  StateNotifier<List<SubscriptionProductData>?> get memberships => _memberships;
}
