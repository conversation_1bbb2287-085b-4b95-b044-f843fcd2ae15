part of '../main/main_router.dart';

class SubscriptionRoute extends GoRouteData with _$SubscriptionRoute {
  const SubscriptionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SubscriptionScreen();
  }
}

class ActivateSubscriptionRoute extends GoRouteData
    with _$ActivateSubscriptionRoute {
  const ActivateSubscriptionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ActivateSubscriptionScreen();
  }
}

class BuySubscriptionRoute extends GoRouteData with _$BuySubscriptionRoute {
  const BuySubscriptionRoute({this.renewData});

  final int? renewData;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return BuySubscriptionScreen(
      args: BuySubscriptionScreenArgs(renewData: renewData),
    );
  }
}

class SubscriptionDetailsRoute extends GoRouteData
    with _$SubscriptionDetailsRoute {
  const SubscriptionDetailsRoute({required this.subscriptionId});

  final int subscriptionId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return SubscriptionDetailsScreen(
      args: SubscriptionDetailsScreenArgs(
        subscriptionId: subscriptionId,
      ),
    );
  }
}
