import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/feature/subscription/details/subscription_details_wm.dart';
import 'package:sba/src/feature/subscription/details/widget/subscription_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_category.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:skeletonizer/skeletonizer.dart';

final class SubscriptionDetailsScreenArgs {
  const SubscriptionDetailsScreenArgs({
    required this.subscriptionId,
  });

  final int subscriptionId;
}

class SubscriptionDetailsScreen
    extends ElementaryWidget<ISubscriptionDetailsWidgetModel> {
  const SubscriptionDetailsScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultSubscriptionDetailsWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final SubscriptionDetailsScreenArgs args;

  @override
  Widget build(ISubscriptionDetailsWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_subscription_details,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: EntityStateNotifierBuilder(
          listenableEntityState: wm.subscription,
          builder: (context, data) {
            final evaluatedData = data ?? SubscriptionData.fake();
            return SingleChildScrollView(
              padding: UISpacing.defaultScreenPadding,
              child: Skeletonizer(
                enableSwitchAnimation: true,
                enabled: data == null,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SubscriptionTile(
                      data: evaluatedData,
                    ),
                    const Gap(UISpacing.xxl),
                    LabelWithText(
                      defaultStyle: context.textTheme.bodyMedium,
                      label: context.l10n.subscription_details_category_label,
                      text:
                          evaluatedData.category?.localizedName(context) ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      defaultStyle: context.textTheme.bodyMedium,
                      label: context.l10n.subscription_details_type_label,
                      text: evaluatedData.type?.localizedName(context) ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      defaultStyle: context.textTheme.bodyMedium,
                      label: context.l10n.subscription_details_vehicle_label,
                      text: evaluatedData.vehicle?.plateNumber ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      defaultStyle: context.textTheme.bodyMedium,
                      label:
                          context.l10n.subscription_details_card_number_label,
                      text: '№ ${evaluatedData.cardNumber}',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      defaultStyle: context.textTheme.bodyMedium,
                      label: context.l10n.subscription_details_valid_from_label,
                      text: evaluatedData.dateFrom.formatDate(context),
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      defaultStyle: context.textTheme.bodyMedium,
                      label:
                          context.l10n.subscription_details_valid_until_label,
                      text: evaluatedData.dateTo.formatDate(context),
                    ),
                    const Gap(UISpacing.xxl),
                    SizedBox(
                      width: double.maxFinite,
                      child: FilledButton(
                        onPressed: () => wm.onRenewTap(evaluatedData),
                        child: Text(context.l10n.action_renew_card),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
