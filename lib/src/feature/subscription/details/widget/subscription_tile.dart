import 'package:flutter/material.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_type.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SubscriptionTile extends StatelessWidget {
  const SubscriptionTile({
    required this.data,
    super.key,
    this.constraints = const BoxConstraints(maxHeight: 220),
    this.onTap,
  });

  final SubscriptionData data;
  final BoxConstraints constraints;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: AspectRatio(
        aspectRatio: 100 / 67,
        child: Card(
          clipBehavior: Clip.hardEdge,
          child: Stack(
            alignment: Alignment.bottomLeft,
            children: [
              if (data.type != null && data.category != null)
                Skeleton.replace(
                  width: double.maxFinite,
                  child: data.type!
                      .image(data.category!)
                      .image(fit: BoxFit.fitWidth),
                ),
              FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: 1,
                heightFactor: 1 / 5,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: UISpacing.xxl,
                  ),
                  child: Text(
                    '№ ${data.cardNumber}',
                    style: UITypography.subscriptionText
                        .copyWith(color: Colors.white),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
