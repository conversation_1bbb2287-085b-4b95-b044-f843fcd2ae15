import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/subscription_repository.dart';

class SubscriptionDetailsModel extends ElementaryModel {
  SubscriptionDetailsModel({
    required SubscriptionRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final SubscriptionRepository _repository;

  Future<Result<SubscriptionData?>> getSubscription(int id) =>
      _repository.getSubscription(id);
}
