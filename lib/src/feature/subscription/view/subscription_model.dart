import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/payment/model/payment_status.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_request_data.dart';
import 'package:sba/src/repository/subscription/subscription_repository.dart';

class SubscriptionModel extends ElementaryModel {
  SubscriptionModel({
    required SubscriptionRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final SubscriptionRepository _repository;

  Stream<Result<List<SubscriptionData>>> get subscriptions =>
      _repository.subscriptions;

  Stream<Result<List<SubscriptionRequestData>>> get requests =>
      _repository.requests;

  Future<void> refresh() => _repository.refresh();

  Future<Result<PaymentStatus>> payRequest(SubscriptionRequestData data) =>
      _repository.paySubscriptionRequest(data);
}
