import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/subscription/activate/activate_subscription_model.dart';
import 'package:sba/src/feature/subscription/activate/activate_subscription_screen.dart';

abstract interface class IActivateSubscriptionWidgetModel
    implements IWidgetModel {}

ActivateSubscriptionWidgetModel defaultActivateSubscriptionWidgetModelFactory(
  BuildContext context,
) {
  return ActivateSubscriptionWidgetModel(ActivateSubscriptionModel());
}

class ActivateSubscriptionWidgetModel
    extends WidgetModel<ActivateSubscriptionScreen, ActivateSubscriptionModel>
    implements IActivateSubscriptionWidgetModel {
  ActivateSubscriptionWidgetModel(super.model);
}
