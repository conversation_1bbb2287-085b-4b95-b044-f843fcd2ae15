import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_multi_visibility.dart';
import 'package:sba/src/ui/widget/form_builder_small_checkbox.dart';

class ActivateCardForm extends StatelessWidget {
  ActivateCardForm({required this.onSubmitClick, super.key});

  final VoidCallback onSubmitClick;
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();
  final ValueNotifier<bool> _showOtherVehicle = ValueNotifier(false);

  static const ({
    String address,
    String brand,
    String city,
    String conditions,
    String gdpr,
    String model,
    String omv,
    String plate,
    String terms,
    String vehicle,
  })
  _keys = (
    vehicle: 'vehicle',
    brand: 'brand',
    model: 'model',
    plate: 'plate',
    city: 'city',
    address: 'address',
    gdpr: 'gdpr',
    omv: 'omv',
    terms: 'terms',
    conditions: 'conditions',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.l10n.subscription_activate_information_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderDropdown<String>(
            name: _keys.vehicle,
            decoration: InputDecoration(
              labelText: context.l10n.form_vehicle,
              hintText: context.l10n.form_vehicle_hint,
            ),
            items: ['PB123', context.l10n.form_type_option_vehicle_other]
                .map((data) => DropdownMenuItem(value: data, child: Text(data)))
                .toList(),
            validator: FormBuilderValidators.required(
              errorText: context.l10n.form_validation_required,
            ),
            onChanged: (data) => _showOtherVehicle.value =
                data == context.l10n.form_type_option_vehicle_other,
          ),
          FormBuilderMultiVisibility(
            visible: _showOtherVehicle,
            children: [
              const Gap(UISpacing.l),
              FormBuilderDropdown<String>(
                name: _keys.brand,
                decoration: InputDecoration(
                  labelText: context.l10n.form_brand,
                  hintText: context.l10n.form_brand_hint,
                ),
                items: ['Test', 'Test2']
                    .map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it),
                      ),
                    )
                    .toList(),
                validator: FormBuilderValidators.required(
                  errorText: context.l10n.form_validation_required,
                ),
              ),
              const Gap(UISpacing.l),
              FormBuilderDropdown<String>(
                name: _keys.model,
                decoration: InputDecoration(
                  labelText: context.l10n.form_model,
                  hintText: context.l10n.form_model_hint,
                ),
                items: ['Test', 'Test2']
                    .map(
                      (it) => DropdownMenuItem(
                        value: it,
                        child: Text(it),
                      ),
                    )
                    .toList(),
                validator: FormBuilderValidators.required(
                  errorText: context.l10n.form_validation_required,
                ),
              ),
              const Gap(UISpacing.l),
              FormBuilderTextField(
                name: _keys.plate,
                decoration: InputDecoration(
                  labelText: context.l10n.form_plate_number,
                  hintText: context.l10n.form_plate_number_hint,
                ),
                textInputAction: TextInputAction.next,
                validator: FormBuilderValidators.licensePlate(
                  errorText: context.l10n.form_validation_license_plate,
                ),
              ),
              const Gap(UISpacing.xxl),
              Text(
                context.l10n.subscription_activate_place_section,
                style: context.textTheme.headlineMedium,
              ),
              const Gap(UISpacing.l),
              FormBuilderTextField(
                name: _keys.city,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.next,
                decoration: InputDecoration(
                  labelText: context.l10n.form_city,
                  hintText: context.l10n.form_city_hint,
                ),
                validator: FormBuilderValidators.required(
                  errorText: context.l10n.form_validation_required,
                ),
              ),
              const Gap(UISpacing.l),
              FormBuilderTextField(
                name: _keys.address,
                keyboardType: TextInputType.streetAddress,
                textInputAction: TextInputAction.done,
                decoration: InputDecoration(
                  labelText: context.l10n.form_address,
                  hintText: context.l10n.form_address_hint,
                ),
                validator: FormBuilderValidators.required(
                  errorText: context.l10n.form_validation_required,
                ),
              ),
            ],
          ),
          const Gap(UISpacing.l),
          FormBuilderSmallCheckbox(
            name: _keys.gdpr,
            title: context.l10n.subscription_activate_gdpr_hint,
            expanded: true,
            validator: FormBuilderValidators.equal(
              true,
              errorText: context.l10n.form_validation_required,
            ),
          ),
          FormBuilderSmallCheckbox(
            name: _keys.omv,
            title: context.l10n.subscription_activate_omv_hint,
            expanded: true,
            validator: FormBuilderValidators.equal(
              true,
              errorText: context.l10n.form_validation_required,
            ),
          ),
          FormBuilderSmallCheckbox(
            name: _keys.terms,
            title: context.l10n.subscription_activate_terms_hint,
            expanded: true,
            validator: FormBuilderValidators.equal(
              true,
              errorText: context.l10n.form_validation_required,
            ),
          ),
          FormBuilderSmallCheckbox(
            name: _keys.conditions,
            title: context.l10n.subscription_activate_conditions_hint,
            expanded: true,
            validator: FormBuilderValidators.equal(
              true,
              errorText: context.l10n.form_validation_required,
            ),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: () => _onSubmit(context),
            child: Text(context.l10n.action_activate),
          ),
        ],
      ),
    );
  }

  void _onSubmit(BuildContext context) async {
    if (_formKey.currentState!.saveAndValidate()) {}
  }
}
