import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/subscription/activate/activate_subscription_wm.dart';
import 'package:sba/src/feature/subscription/activate/widget/activate_card_form.dart';
import 'package:sba/src/feature/subscription/activate/widget/check_card_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class ActivateSubscriptionScreen
    extends ElementaryWidget<IActivateSubscriptionWidgetModel> {
  const ActivateSubscriptionScreen({
    Key? key,
    WidgetModelFactory wmFactory =
        defaultActivateSubscriptionWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IActivateSubscriptionWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_activate_subscription,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: Builder(
          builder: (context) {
            return SingleChildScrollView(
              padding: UISpacing.defaultScreenPadding,
              child: Column(
                children: [
                  Text(
                    context.l10n.subscription_activate_activate_section,
                    style: context.textTheme.bodySmall,
                  ),
                  const Gap(UISpacing.xxl),
                  CheckCardForm(
                    onSubmitClick: () {},
                  ),
                  const Gap(UISpacing.xxl),
                  ActivateCardForm(
                    onSubmitClick: () {},
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
