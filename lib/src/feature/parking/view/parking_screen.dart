import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/parking/view/parking_wm.dart';
import 'package:sba/src/feature/parking/view/widget/parking_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/action_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_list_view.dart';

class ParkingScreen extends ElementaryWidget<IParkingWidgetModel> {
  const ParkingScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultParkingWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IParkingWidgetModel wm) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: wm.refresh,
        notificationPredicate: (n) => n.depth == 1,
        child: NestedScrollView(
          headerSliverBuilder: (context, _) => [
            StateNotifierBuilder(
              listenableState: wm.parkingData,
              builder: (context, data) => SliverTitle(
                text: context.l10n.navigation_parking,
                padding: UISpacing.defaultElementPaddingWithoutBottom,
                action: data?.isEmpty ?? true
                    ? null
                    : FloatingActionButton.small(
                        onPressed: wm.requestParking,
                        child: const Icon(Icons.add),
                      ),
              ),
            ),
          ],
          body: StateNotifierBuilder(
            listenableState: wm.parkingData,
            builder: (context, data) => SmartListView(
              loading: data == null,
              data: data,
              skeletonData: List.filled(1, ParkingRequest.fake()),
              padding: UISpacing.defaultScreenPadding,
              separator: const Gap(UISpacing.m),
              showActionWhenData: false,
              action: ActionBox(
                icon: Icons.add,
                text: context.l10n.action_request_parking,
                onTap: wm.requestParking,
              ),
              builder: (context, data) => ParkingTile(
                data: data,
                onNewRequestTap: () => wm.requestParking(data: data),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
