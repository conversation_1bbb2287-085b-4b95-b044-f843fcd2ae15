import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ParkingTile extends StatelessWidget {
  const ParkingTile({
    required this.data,
    required this.onNewRequestTap,
    super.key,
  });

  final ParkingRequest data;
  final VoidCallback onNewRequestTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(UISpacing.m),
      decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Skeleton.replace(
                width: 48,
                height: 48,
                child: Assets.icons.localParking.svg(
                  colorFilter: ColorFilter.mode(
                    context.theme.colorScheme.primary,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              const Gap(UISpacing.m),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    LabelWithText(
                      label: context.l10n.parking_tile_city,
                      text: data.place?.city ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.parking_tile_vehicle,
                      text: data.plateNumber,
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.parking_tile_zone,
                      text: data.zone?.name ?? '',
                    ),
                    const Gap(UISpacing.s),
                    LabelWithText(
                      label: context.l10n.parking_tile_until,
                      text: data.paidUntil?.formatTime(context) ?? '',
                      textColor: data.paidUntil
                              ?.isLessThan(const Duration(minutes: 10)) ?? false
                          ? context.theme.colorScheme.error
                          : null,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const Gap(UISpacing.ll),
          FilledButton(
            onPressed: onNewRequestTap,
            child: Text(context.l10n.action_prolong_parking),
          ),
        ],
      ),
    );
  }
}
