import 'package:collection/collection.dart';
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/parking/view/parking_model.dart';
import 'package:sba/src/feature/parking/view/parking_screen.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';

abstract interface class IParkingWidgetModel implements IWidgetModel {
  StateNotifier<List<ParkingRequest>> get parkingData;

  Future<void> refresh();

  void requestParking({ParkingRequest? data});
}

ParkingWidgetModel defaultParkingWidgetModelFactory(BuildContext context) {
  return ParkingWidgetModel(
    ParkingModel(
      parkingRepository: get(),
      errorHandler: get(),
    ),
  );
}

class ParkingWidgetModel extends WidgetModel<ParkingScreen, ParkingModel>
    implements IParkingWidgetModel {
  ParkingWidgetModel(super.model);

  final _parkingData = StateNotifier<List<ParkingRequest>>();
  final _sub = CompositeSubscription();

  @override
  Future<void> initWidgetModel() async {
    _sub.add(
      model.parkingRequests
          .asyncMap((result) async {
            if (result is Failure) {
              await context.showGeneralErrorDialog(
                failure: result as Failure,
              );
            }
            return result;
          })
          .map((e) => e.maybeValue)
          .map((e) => e?.sorted((a, b) => b.paidUntil!.compareTo(a.paidUntil!)))
          .listen(_parkingData.accept),
    );
    super.initWidgetModel();
  }

  @override
  void dispose() {
    _sub.dispose();
    super.dispose();
  }

  @override
  Future<void> refresh() => model.refresh();

  @override
  void requestParking({ParkingRequest? data}) {
    RequestParkingRoute($extra: data).go(context);
  }

  @override
  StateNotifier<List<ParkingRequest>> get parkingData => _parkingData;
}
