import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/sms_handler/sms_handler.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/repository/parking/model/parking_zone.dart';
import 'package:sba/src/repository/parking/parking_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class RequestParkingModel extends ElementaryModel {
  RequestParkingModel({
    required ParkingRepository repository,
    required VehicleRepository vehicleRepository,
    required SmsHandler smsHandler,
    super.errorHandler,
  })  : _repository = repository,
        _vehicleRepository = vehicleRepository,
        _smsHandler = smsHandler;

  final ParkingRepository _repository;
  final VehicleRepository _vehicleRepository;
  final SmsHandler _smsHandler;

  Future<List<ParkingZone>?> get parkingZones =>
      _repository.getParkingZones().then((e) => e.maybeValue);

  Future<List<VehicleData>?> get vehicles =>
      _vehicleRepository.getVehicles();

  Future<Result<void>> createParkingRequest(ParkingRequest data) =>
      _repository.createParkingRequest(data);

  Future<Result<void>> sendSms(BuildContext context, ParkingRequest data) =>
      _smsHandler.sendSms(
        context,
        data.zone!.phone,
        data.plateNumber,
      );
}
