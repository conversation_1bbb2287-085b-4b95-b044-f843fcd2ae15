import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/parking/request/request_parking_wm.dart';
import 'package:sba/src/feature/parking/request/widget/request_parking_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

final class RequestParkingScreenArgs {
  RequestParkingScreenArgs({this.request});

  final ParkingRequest? request;
}

class RequestParkingScreen
    extends ElementaryWidget<IRequestParkingWidgetModel> {
  const RequestParkingScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultRequestParkingWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final RequestParkingScreenArgs args;

  @override
  Widget build(IRequestParkingWidgetModel wm) {
    return NestedScrollView(
      headerSliverBuilder: (context, _) => [
        SliverTitle(
          text: context.l10n.navigation_parking_request,
          padding: UISpacing.defaultElementPaddingWithoutBottom,
        ),
      ],
      body: SingleChildScrollView(
        padding: UISpacing.defaultScreenPadding,
        child: TripleSourceBuilder(
          firstSource: wm.initialData,
          secondSource: wm.parkingZones,
          thirdSource: wm.vehicles,
          builder: (context, data, zones, vehicles) => RequestParkingForm(
            initialData: data,
            zones: zones,
            vehicles: vehicles,
            onSubmitClick: wm.onSubmit,
          ),
        ),
      ),
    );
  }
}
