part of '../main/main_router.dart';

class ParkingRoute extends GoRouteData with _$ParkingRoute {
  const ParkingRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ParkingScreen();
  }
}

class RequestParkingRoute extends GoRouteData with _$RequestParkingRoute {
  const RequestParkingRoute({this.$extra});

  final ParkingRequest? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return RequestParkingScreen(
      args: RequestParkingScreenArgs(request: $extra),
    );
  }
}
