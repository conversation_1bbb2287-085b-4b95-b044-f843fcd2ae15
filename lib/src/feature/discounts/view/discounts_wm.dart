import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/discounts/view/discounts_model.dart';
import 'package:sba/src/feature/discounts/view/discounts_screen.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/repository/contact/model/discounts.dart';

abstract interface class IDiscountsWidgetModel implements IWidgetModel {
  StateNotifier<DiscountCategory?> get selectedCategory;

  StateNotifier<List<DiscountCategory?>> get categories;

  EntityStateNotifier<List<DiscountItem>> get items;

  void onCategoryTap(DiscountCategory? category);

  void onDiscountTap(DiscountItem item);
}

DiscountsWidgetModel defaultDiscountsWidgetModelFactory(BuildContext context) {
  return DiscountsWidgetModel(
    DiscountsModel(
      contactRepository: get(),
      errorHandler: get(),
    ),
  );
}

class DiscountsWidgetModel extends WidgetModel<DiscountsScreen, DiscountsModel>
    implements IDiscountsWidgetModel {
  DiscountsWidgetModel(super.model);

  final _selectedCategory = StateNotifier<DiscountCategory?>();
  final _categories = StateNotifier<List<DiscountCategory?>>();
  final _items = EntityStateNotifier<List<DiscountItem>>();

  @override
  void initWidgetModel() async {
    super.initWidgetModel();
    final result = await model.discounts;

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
    }

    _categories.accept(result.maybeValue?.addFirst(null));
    onCategoryTap(null);
  }

  void _loadItems() async {
    _items.loading();
    final selection = _selectedCategory.value;
    final items = _categories.value?.nonNulls
        .where((c) => selection == null || c == selection)
        .expand((e) => e.items)
        .toList()
        .unique((item) => item.name);

    await Future<void>.delayed(const Duration(milliseconds: 200));
    if (items == null) {
      _items.error();
      return;
    }

    _items.content(items);
  }

  @override
  void onCategoryTap(DiscountCategory? category) {
    _selectedCategory.accept(category);
    _loadItems();
  }

  @override
  void onDiscountTap(DiscountItem item) =>
      DiscountDetailsRoute($extra: item).go(context);

  @override
  StateNotifier<List<DiscountCategory?>> get categories => _categories;

  @override
  EntityStateNotifier<List<DiscountItem>> get items => _items;

  @override
  StateNotifier<DiscountCategory?> get selectedCategory => _selectedCategory;
}
