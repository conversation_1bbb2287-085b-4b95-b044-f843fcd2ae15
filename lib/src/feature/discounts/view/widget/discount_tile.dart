import 'package:flutter/material.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/repository/contact/model/discounts.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/uri_image.dart';
import 'package:skeletonizer/skeletonizer.dart';

class DiscountTile extends StatelessWidget {
  const DiscountTile({
    required this.data,
    required this.onTap,
    super.key,
  });

  final DiscountItem data;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            flex: 2,
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: UIColors.border,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Skeleton.replace(
                width: double.maxFinite,
                height: double.maxFinite,
                child: Hero(
                  tag: data.image ?? '',
                  child: UriImage(
                    placeholder: Assets.images.placeholderImage.path,
                    fit: BoxFit.cover,
                    imageUri: data.image,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: UISpacing.s),
              child: Text(
                data.title ?? data.name,
                style: context.textTheme.headlineSmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
