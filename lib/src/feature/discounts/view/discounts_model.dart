import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/contact/contact_repository.dart';
import 'package:sba/src/repository/contact/model/discounts.dart';

class DiscountsModel extends ElementaryModel {
  DiscountsModel({
    required ContactRepository contactRepository,
    super.errorHandler,
  }) : _contactRepository = contactRepository;

  final ContactRepository _contactRepository;

  Future<Result<List<DiscountCategory>>> get discounts =>
      _contactRepository.getDiscounts();
}
