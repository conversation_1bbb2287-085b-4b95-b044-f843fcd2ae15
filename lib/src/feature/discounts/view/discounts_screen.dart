import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/discounts/view/discounts_wm.dart';
import 'package:sba/src/feature/discounts/view/widget/discount_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/contact/model/discounts.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_grid_view.dart';
import 'package:sba/src/ui/widget/smart_wrap_view.dart';
import 'package:sliver_tools/sliver_tools.dart';

class DiscountsScreen extends ElementaryWidget<IDiscountsWidgetModel> {
  const DiscountsScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultDiscountsWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IDiscountsWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_partners,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
          SliverPinnedHeader(
            child: Container(
              padding: const EdgeInsets.all(UISpacing.ll),
              color: context.theme.colorScheme.surface,
              child: DoubleSourceBuilder(
                firstSource: wm.categories,
                secondSource: wm.selectedCategory,
                builder: (context, categories, selection) => SmartWrapView(
                  loading: categories == null,
                  data: categories,
                  skeletonData: List.filled(5, DiscountCategory.fake()),
                  builder: (context, category) => ChoiceChip(
                    label: Text(
                        category?.name ?? context.l10n.partners_filter_all,),
                    selected: selection == category,
                    onSelected: (_) => wm.onCategoryTap(category),
                  ),
                ),
              ),
            ),
          ),
        ],
        body: EntityStateNotifierBuilder(
          listenableEntityState: wm.items,
          builder: (context, data) => SmartGridView(
            loading: data == null,
            data: data,
            skeletonData: List.filled(5, DiscountItem.fake()),
            emptyText: context.l10n.empty_common,
            builder: (context, data) => DiscountTile(
              data: data,
              onTap: () => wm.onDiscountTap(data),
            ),
            delegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: UISpacing.m,
              crossAxisSpacing: UISpacing.s,
              childAspectRatio: 17 / 14,
            ),
            padding: UISpacing.defaultElementPaddingWithoutTop,
          ),
        ),
      ),
    );
  }
}
