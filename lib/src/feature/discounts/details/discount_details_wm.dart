import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/discounts/details/discount_details_model.dart';
import 'package:sba/src/feature/discounts/details/discount_details_screen.dart';

abstract interface class IDiscountDetailsWidgetModel implements IWidgetModel {}

DiscountDetailsWidgetModel defaultDiscountDetailsWidgetModelFactory(
  BuildContext context,
) {
  return DiscountDetailsWidgetModel(DiscountDetailsModel());
}

class DiscountDetailsWidgetModel
    extends WidgetModel<DiscountDetailsScreen, DiscountDetailsModel>
    implements IDiscountDetailsWidgetModel {
  DiscountDetailsWidgetModel(super.model);
}
