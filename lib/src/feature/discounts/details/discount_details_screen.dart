import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/discounts/details/discount_details_wm.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/contact/model/discounts.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';
import 'package:sba/src/ui/widget/html_view.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/uri_image.dart';

final class DiscountDetailsScreenArgs {
  DiscountDetailsScreenArgs({required this.item});

  final DiscountItem item;
}

class DiscountDetailsScreen
    extends ElementaryWidget<IDiscountDetailsWidgetModel> {
  const DiscountDetailsScreen({
    required this.args,
    Key? key,
    WidgetModelFactory wmFactory = defaultDiscountDetailsWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final DiscountDetailsScreenArgs args;

  @override
  Widget build(IDiscountDetailsWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: args.item.name,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: Builder(
          builder: (context) {
            return SingleChildScrollView(
              child: Padding(
                padding: UISpacing.defaultScreenPadding,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Hero(
                        tag: args.item.image ?? '',
                        child: UriImage(
                          placeholder: Assets.images.placeholderImage.path,
                          imageUri: args.item.image,
                        ),
                      ),
                    ),
                    const Gap(UISpacing.xl),
                    Text(
                      args.item.announcement ?? '',
                      style: context.textTheme.headlineLarge,
                    ),
                    const Gap(UISpacing.xl),
                    Text(
                      context.l10n.partners_details_info_section,
                      style: context.textTheme.headlineMedium,
                    ),
                    const Gap(UISpacing.xl),
                    if (args.item.content != null)
                      HtmlView(
                        html: args.item.content!,
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
