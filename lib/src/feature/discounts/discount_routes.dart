part of '../main/main_router.dart';

class DiscountRoute extends GoRouteData with _$DiscountRoute {
  const DiscountRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const DiscountsScreen();
  }
}

class DiscountDetailsRoute extends GoRouteData with _$DiscountDetailsRoute {
  const DiscountDetailsRoute({required this.$extra});

  final DiscountItem $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DiscountDetailsScreen(args: DiscountDetailsScreenArgs(item: $extra));
  }
}
