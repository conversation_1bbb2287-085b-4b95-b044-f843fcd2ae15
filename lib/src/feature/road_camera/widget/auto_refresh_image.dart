import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';

typedef ImageBuilder = Widget Function(String uri);

class AutoRefreshImage extends StatefulWidget {
  const AutoRefreshImage({
    required this.refreshInterval,
    required this.builder,
    super.key,
    this.imageUri,
  });

  final String? imageUri;
  final Duration refreshInterval;
  final ImageBuilder builder;

  @override
  State<AutoRefreshImage> createState() => _AutoRefreshImageState();
}

class _AutoRefreshImageState extends State<AutoRefreshImage> {
  late Stream<String> _timerStream;

  final BehaviorSubject<String?> _imageUriSubject = BehaviorSubject();

  @override
  void initState() {
    _timerStream = _imageUriSubject
        .where((e) => e?.isNotEmpty ?? false)
        .map((e) => e!)
        .switchMap(
          (image) => Rx.repeat(
            (time) => Rx.timer(
              '$image?t=$time',
              widget.refreshInterval,
            ),
          ).startWith(image),
        );

    _imageUriSubject.add(widget.imageUri);

    super.initState();
  }

  @override
  void didUpdateWidget(covariant AutoRefreshImage oldWidget) {
    if (oldWidget.imageUri != widget.imageUri) {
      _imageUriSubject.add(widget.imageUri);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder(
      stream: _timerStream,
      builder: (context, data) => widget.builder(data.data ?? ''),
    );
  }
}
