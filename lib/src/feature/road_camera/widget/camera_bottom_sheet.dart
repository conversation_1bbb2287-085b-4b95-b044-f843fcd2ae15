import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/road_camera/widget/auto_refresh_image.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/uri_image.dart';

class CameraBottomSheet extends StatelessWidget {
  const CameraBottomSheet({
    this.title,
    this.imageUri,
    super.key,
  });

  final String? imageUri;
  final String? title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: UISpacing.defaultScreenPadding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            title ?? 'l',
            style: context.textTheme.headlineLarge,
            textAlign: TextAlign.center,
          ),
          const Gap(UISpacing.xl),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: AutoRefreshImage(
              refreshInterval: const Duration(seconds: 30),
              imageUri: imageUri,
              builder: (uri) => UriImage(
                key: ValueKey(uri),
                placeholder: Assets.images.placeholderImage.path,
                imageUri: uri,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
