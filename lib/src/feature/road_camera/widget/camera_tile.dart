import 'package:flutter/material.dart';
import 'package:sba/src/feature/road_camera/widget/auto_refresh_image.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/uri_image.dart';
import 'package:skeletonizer/skeletonizer.dart';

class CameraTile extends StatelessWidget {
  const CameraTile({
    this.imageUrl,
    this.text,
    super.key,
    this.onTap,
  });

  final String? imageUrl;
  final String? text;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            flex: 2,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Skeleton.replace(
                width: double.maxFinite,
                height: double.maxFinite,
                child: AutoRefreshImage(
                  refreshInterval: const Duration(seconds: 30),
                  imageUri: imageUrl,
                  builder: (uri) => UriImage(
                    placeholder: Assets.images.placeholderImage.path,
                    fit: BoxFit.cover,
                    imageUri: uri,
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: UISpacing.s),
              child: Text(
                text ?? '',
                style: context.textTheme.headlineSmall,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
