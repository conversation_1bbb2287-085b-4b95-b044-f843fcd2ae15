import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/road_camera/view/road_camera_wm.dart';
import 'package:sba/src/feature/road_camera/widget/camera_tile.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/road_camera/model/road_camera.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/open_street_map.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';
import 'package:sba/src/ui/widget/smart_grid_view.dart';

class RoadCameraScreen extends ElementaryWidget<IRoadCameraWidgetModel> {
  const RoadCameraScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultRoadCameraWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IRoadCameraWidgetModel wm) {
    return Scaffold(
      body: Builder(
        builder: (context) {
          return NestedScrollView(
            headerSliverBuilder: (context, _) => [
              SliverTitle(
                text: context.l10n.navigation_road_cameras,
                padding: UISpacing.defaultScreenPadding,
              ),
            ],
            body: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: EntityStateNotifierBuilder(
                    listenableEntityState: wm.markerData,
                    builder: (context, data) => OpenStreetMap(
                      padding: UISpacing.defaultElementHorizontalPadding,
                      markers: data,
                      animateMapBoundsToFitMarkers: true,
                      onMarkerTap: (data) => wm.onCameraTap(data.data),
                    ),
                  ),
                ),
                const SliverGap(UISpacing.xl),
                EntityStateNotifierBuilder(
                  listenableEntityState: wm.cameraData,
                  builder: (context, data) => SmartGridView.sliver(
                    loading: data == null,
                    data: data,
                    skeletonData: List.filled(5, RoadCamera.fake()),
                    emptyText: context.l10n.empty_common,
                    builder: (context, data) => CameraTile(
                      imageUrl: data.imageUri,
                      text: data.name,
                      onTap: () => wm.onCameraTap(data),
                    ),
                    delegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: UISpacing.s,
                      crossAxisSpacing: UISpacing.s,
                      childAspectRatio: 17 / 14,
                    ),
                    padding: UISpacing.defaultElementPaddingWithoutTop,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
