import 'dart:async';

import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/road_camera/view/road_camera_model.dart';
import 'package:sba/src/feature/road_camera/view/road_camera_screen.dart';
import 'package:sba/src/feature/road_camera/widget/camera_bottom_sheet.dart';
import 'package:sba/src/repository/road_camera/model/road_camera.dart';
import 'package:sba/src/ui/widget/open_street_map.dart';

abstract interface class IRoadCameraWidgetModel implements IWidgetModel {
  EntityStateNotifier<List<RoadCamera>> get cameraData;

  EntityStateNotifier<List<MarkerData<RoadCamera>>> get markerData;

  void onCameraTap(RoadCamera data);
}

RoadCameraWidgetModel defaultRoadCameraWidgetModelFactory(
  BuildContext context,
) {
  return RoadCameraWidgetModel(
    RoadCameraModel(
      repository: get(),
      errorHandler: get(),
    ),
  );
}

class RoadCameraWidgetModel
    extends WidgetModel<RoadCameraScreen, RoadCameraModel>
    implements IRoadCameraWidgetModel {
  RoadCameraWidgetModel(super.model);

  final EntityStateNotifier<List<RoadCamera>> _cameraState =
      EntityStateNotifier();
  final EntityStateNotifier<List<MarkerData<RoadCamera>>> _markerState =
      EntityStateNotifier();

  @override
  void initWidgetModel() {
    loadData();
    super.initWidgetModel();
  }

  Future<void> loadData() async {
    _cameraState.loading();
    _markerState.loading();

    final result = await model.getRoadCameras();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
    }

    _cameraState.content(
      result.maybeValue ?? List.empty(),
    );
    _markerState.content(
      result.maybeValue
              ?.map((e) => MarkerData(coordinates: e.coordinates, data: e))
              .toList(growable: false) ??
          List.empty(),
    );
  }

  @override
  EntityStateNotifier<List<RoadCamera>> get cameraData => _cameraState;

  @override
  EntityStateNotifier<List<MarkerData<RoadCamera>>> get markerData =>
      _markerState;

  @override
  void onCameraTap(RoadCamera data) {
    context.showModalBottomSheet<void>(
      builder: (context) =>
          CameraBottomSheet(imageUri: data.imageUri, title: data.name),
    );
  }
}
