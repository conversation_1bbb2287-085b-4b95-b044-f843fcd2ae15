import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/road_camera/model/road_camera.dart';
import 'package:sba/src/repository/road_camera/road_camera_repository.dart';

class RoadCameraModel extends ElementaryModel {
  RoadCameraModel({
    required RoadCameraRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final RoadCameraRepository _repository;

  Future<Result<List<RoadCamera>>> getRoadCameras() =>
      _repository.getRoadCameras();
}
