import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/profile/notification_settings/notification_settings_model.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/user/model/type/notification_type.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/form_builder_custom_checkbox_group.dart';

class NotificationSettingsForm extends StatelessWidget {
  NotificationSettingsForm({
    required this.onSubmit,
    super.key,
    this.initialData,
  });

  final ValueChanged<ConsentRecord> onSubmit;
  final UserData? initialData;
  final _formKey = GlobalKey<FormBuilderState>();

  static const ({String notification, String sms}) _keys = (
    notification: 'notification',
    sms: 'sms',
  );

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            context.l10n.profile_notification_email_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderCustomCheckboxGroup<NotificationType>(
            name: _keys.notification,
            initialValue: initialData?.emailNotifications.toList(),
            label: context.l10n.form_notification_hint,
            options: NotificationType.values
                .map(
                  (type) => FormBuilderFieldOption(
                    value: type,
                    child: Text(
                      type.localizedName(context),
                    ),
                  ),
                )
                .toList(),
          ),
          const Gap(UISpacing.xxl),
          Text(
            context.l10n.profile_notification_sms_section,
            style: context.textTheme.headlineMedium,
          ),
          const Gap(UISpacing.l),
          FormBuilderCustomCheckboxGroup<NotificationType>(
            name: _keys.sms,
            initialValue: initialData?.phoneNotifications.toList(),
            label: context.l10n.form_sms_hint,
            options: NotificationType.values
                .map(
                  (type) => FormBuilderFieldOption(
                    value: type,
                    child: Text(
                      type.localizedName(context),
                    ),
                  ),
                )
                .toList(),
          ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: _onSubmit,
            child: Text(context.l10n.action_edit),
          ),
        ],
      ),
    );
  }

  void _onSubmit() {
    if (_formKey.currentState!.saveAndValidate()) {
      onSubmit(
        (
          email:
              (_formKey.currentState!.value[_keys.notification]
                      as List<NotificationType>)
                  .toSet(),
          phone:
              (_formKey.currentState!.value[_keys.sms]
                      as List<NotificationType>)
                  .toSet(),
        ),
      );
    }
  }
}
