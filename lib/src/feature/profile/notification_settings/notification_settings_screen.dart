import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/profile/notification_settings/notification_settings_wm.dart';
import 'package:sba/src/feature/profile/notification_settings/widget/notification_settings_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class NotificationSettingsScreen
    extends ElementaryWidget<INotificationSettingsWidgetModel> {
  const NotificationSettingsScreen({
    Key? key,
    WidgetModelFactory wmFactory =
        defaultNotificationSettingsWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(INotificationSettingsWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_notification,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: StateNotifierBuilder(
            listenableState: wm.initialValue,
            builder: (context, data) => NotificationSettingsForm(
              initialData: data,
              onSubmit: wm.onSubmit,
            ),
          ),
        ),
      ),
    );
  }
}
