import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/user/model/type/notification_type.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/user/user_repository.dart';

typedef ConsentRecord = ({
  Set<NotificationType> email,
  Set<NotificationType> phone
});

class NotificationSettingsModel extends ElementaryModel {
  NotificationSettingsModel({
    required UserRepository userRepository,
    super.errorHandler,
  }) : _userRepository = userRepository;

  final UserRepository _userRepository;

  Future<UserData?> get user => _userRepository.getUser();

  Future<Result<void>> update(ConsentRecord record) =>
      _userRepository.updateConsent(
        emailConsent: record.email,
        mobileConsent: record.phone,
      );
}
