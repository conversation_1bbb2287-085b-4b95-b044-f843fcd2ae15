part of '../main/main_router.dart';

class <PERSON>Route extends GoRouteData with _$ProfileRoute {
  const ProfileRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ProfileScreen();
  }
}

class EditProfileRoute extends GoRouteData with _$EditProfileRoute {
  const EditProfileRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const EditProfileScreen();
  }
}

class EditPasswordRoute extends GoRouteData with _$EditPasswordRoute {
  const EditPasswordRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ChangePasswordScreen();
  }
}

class NotificationSettingsRoute extends GoRouteData
    with _$NotificationSettingsRoute {
  const NotificationSettingsRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const NotificationSettingsScreen();
  }
}
