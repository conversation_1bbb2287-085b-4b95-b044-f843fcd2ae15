import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/profile/password/change_password_model.dart';
import 'package:sba/src/feature/profile/password/change_password_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:toastification/toastification.dart';

abstract interface class IChangePasswordWidgetModel implements IWidgetModel {
  Future<void> onSubmit(PasswordData data);
}

ChangePasswordWidgetModel defaultChangePasswordWidgetModelFactory(
  BuildContext context,
) {
  return ChangePasswordWidgetModel(
    ChangePasswordModel(authRepository: get(), errorHandler: get()),
  );
}

class ChangePasswordWidgetModel
    extends WidgetModel<ChangePasswordScreen, ChangePasswordModel>
    implements IChangePasswordWidgetModel {
  ChangePasswordWidgetModel(super.model);

  @override
  Future<void> onSubmit(PasswordData data) async {
    await context.showLoadingDialog();
    final result = await model.update(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context.showToast(
      type: ToastificationType.success,
      title: context.l10n.message_success_edit,
    );

    await context.router.popRoute();
  }
}
