import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';

typedef PasswordData = ({String email, String oldPassword, String newPassword});

class ChangePasswordModel extends ElementaryModel {
  ChangePasswordModel({
    required AuthRepository authRepository,
    super.errorHandler,
  }) : _authRepository = authRepository;

  final AuthRepository _authRepository;

  Future<Result<void>> update(PasswordData data) =>
      _authRepository.updatePassword(
        email: data.email,
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
      );
}
