import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/profile/password/change_password_wm.dart';
import 'package:sba/src/feature/profile/password/widget/password_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class ChangePasswordScreen
    extends ElementaryWidget<IChangePasswordWidgetModel> {
  const ChangePasswordScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultChangePasswordWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IChangePasswordWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_profile_password,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: PasswordForm(
            onSubmit: wm.onSubmit,
          ),
        ),
      ),
    );
  }
}
