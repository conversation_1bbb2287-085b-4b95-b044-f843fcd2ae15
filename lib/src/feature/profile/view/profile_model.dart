import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/user/user_repository.dart';

class ProfileModel extends ElementaryModel {
  ProfileModel({
    required AuthRepository authRepository,
    required UserRepository userRepository,
    super.errorHandler,
  })  : _authRepository = authRepository,
        _userRepository = userRepository;

  final AuthRepository _authRepository;
  final UserRepository _userRepository;

  Future<Result<void>> logout() => _authRepository.logout();

  Stream<UserData?> get userData => _userRepository.user;
}
