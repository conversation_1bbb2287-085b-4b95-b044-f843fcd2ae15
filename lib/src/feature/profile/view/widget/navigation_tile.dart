import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class NavigationTile extends StatelessWidget {
  const NavigationTile({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    super.key,
  });

  final Widget icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: SizedBox(
        width: 36,
        height: 36,
        child: ColorFiltered(
          colorFilter: ColorFilter.mode(
            context.theme.colorScheme.primary,
            BlendMode.srcIn,
          ),
          child: icon,
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(
        Icons.arrow_forward_ios_sharp,
        size: 18,
      ),
    );
  }
}
