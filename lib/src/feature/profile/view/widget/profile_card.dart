import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/feature/profile/view/widget/profile_avatar.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class ProfileCard extends StatelessWidget {
  const ProfileCard({required this.fullName, required this.email, super.key});

  final String fullName;
  final String email;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Card(
          margin: const EdgeInsets.only(top: 30),
          child: Padding(
            padding: const EdgeInsets.only(
              top: UISpacing.ll + 30,
              left: UISpacing.ll,
              right: UISpacing.ll,
              bottom: UISpacing.ll,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  fullName,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: context.textTheme.headlineMedium
                      ?.copyWith(color: UIColors.heading),
                ),
                const Gap(UISpacing.xs),
                Text(
                  email,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: context.textTheme.bodyMedium
                      ?.copyWith(color: UIColors.menuForeground),
                ),
              ],
            ),
          ),
        ),
        ProfileAvatar(
          initials: fullName.getInitials(),
        ),
      ],
    );
  }
}
