import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class ProfileAvatar extends StatelessWidget {
  const ProfileAvatar({
    super.key,
    this.initials,
  });

  final String? initials;

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: 30,
      backgroundColor: context.theme.colorScheme.primary,
      child: Text(
        (initials?.isEmpty ?? true) ? '?' : initials!,
        style: context.textTheme.headlineLarge?.copyWith(
          color: context.theme.colorScheme.onPrimary,
        ),
      ),
    );
  }
}
