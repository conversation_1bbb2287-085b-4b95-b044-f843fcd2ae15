import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/profile/view/profile_wm.dart';
import 'package:sba/src/feature/profile/view/widget/navigation_tile.dart';
import 'package:sba/src/feature/profile/view/widget/profile_card.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/ui/theme/theme.dart';

class ProfileScreen extends ElementaryWidget<IProfileWidgetModel> {
  const ProfileScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultProfileWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IProfileWidgetModel wm) {
    return Scaffold(
      body: Padding(
        padding: UISpacing.defaultElementPaddingWithoutTop,
        child: Builder(
          builder: (context) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                StateNotifierBuilder(
                  listenableState: wm.user,
                  builder: (context, state) => ProfileCard(
                    fullName: state?.fullName ?? '',
                    email: state?.email ?? '',
                  ),
                ),
                const Gap(UISpacing.ll),
                NavigationTile(
                  icon: Assets.icons.accountCircle.svg(),
                  title: context.l10n.profile_navigation_edit_title,
                  subtitle: context.l10n.profile_navigation_edit_subtitle,
                  onTap: wm.onProfileEditTap,
                ),
                NavigationTile(
                  icon: Assets.icons.protect.svg(),
                  title: context.l10n.profile_navigation_password_title,
                  subtitle: context.l10n.profile_navigation_password_subtitle,
                  onTap: wm.onPasswordTap,
                ),
                NavigationTile(
                  icon: Assets.icons.directionsCar.svg(),
                  title: context.l10n.profile_navigation_cars_title,
                  subtitle: context.l10n.profile_navigation_cars_subtitle,
                  onTap: wm.onVehicleEditTap,
                ),
                NavigationTile(
                  icon: Assets.icons.notification.svg(),
                  title: context.l10n.profile_navigation_notifications_title,
                  subtitle:
                      context.l10n.profile_navigation_notifications_subtitle,
                  onTap: wm.onNotificationSettingsTap,
                ),
                Expanded(
                  child: Center(
                    child: OutlinedButton.icon(
                      onPressed: wm.onExit,
                      label: Text(context.l10n.action_exit),
                      icon: Assets.icons.exitApp.svg(),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
