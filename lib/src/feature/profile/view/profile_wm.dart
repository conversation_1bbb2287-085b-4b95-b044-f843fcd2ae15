import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/feature/main/main_router.dart';
import 'package:sba/src/feature/profile/view/profile_model.dart';
import 'package:sba/src/feature/profile/view/profile_screen.dart';
import 'package:sba/src/repository/user/model/user_data.dart';

abstract interface class IProfileWidgetModel implements IWidgetModel {
  void onProfileEditTap();

  void onPasswordTap();

  void onVehicleEditTap();

  void onNotificationSettingsTap();

  void onExit();

  StateNotifier<UserData> get user;
}

ProfileWidgetModel defaultProfileWidgetModelFactory(BuildContext context) {
  return ProfileWidgetModel(
    ProfileModel(
      authRepository: get(),
      userRepository: get(),
      errorHandler: get(),
    ),
  );
}

class ProfileWidgetModel extends WidgetModel<ProfileScreen, ProfileModel>
    implements IProfileWidgetModel {
  ProfileWidgetModel(super.model);

  final StateNotifier<UserData> _user = StateNotifier();
  final CompositeSubscription _subscription = CompositeSubscription();

  @override
  void initWidgetModel() {
    _subscription.add(model.userData.listen(_user.accept));
    super.initWidgetModel();
  }

  @override
  void dispose() {
    _subscription.dispose();
    super.dispose();
  }

  @override
  void onNotificationSettingsTap() {
    const NotificationSettingsRoute().go(context);
  }

  @override
  void onProfileEditTap() {
    const EditProfileRoute().go(context);
  }

  @override
  void onPasswordTap() {
    const EditPasswordRoute().go(context);
  }

  @override
  void onVehicleEditTap() {
    const MyVehiclesRoute().go(context);
  }

  @override
  void onExit() => model.logout();

  @override
  StateNotifier<UserData> get user => _user;
}
