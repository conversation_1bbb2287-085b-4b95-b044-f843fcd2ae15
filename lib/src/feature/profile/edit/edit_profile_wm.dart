import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/profile/edit/edit_profile_model.dart';
import 'package:sba/src/feature/profile/edit/edit_profile_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:toastification/toastification.dart';

abstract interface class IEditProfileWidgetModel implements IWidgetModel {
  StateNotifier<List<Place>?> get places;

  StateNotifier<UserData?> get user;

  Future<void> onSubmit(AddressData data);
}

EditProfileWidgetModel defaultEditProfileWidgetModelFactory(
  BuildContext context,
) {
  return EditProfileWidgetModel(
    EditProfileModel(
      generalRepository: get(),
      userRepository: get(),
      errorHandler: get(),
    ),
  );
}

class EditProfileWidgetModel
    extends WidgetModel<EditProfileScreen, EditProfileModel>
    implements IEditProfileWidgetModel {
  EditProfileWidgetModel(super.model);

  final _places = StateNotifier<List<Place>?>();
  final _user = StateNotifier<UserData?>();

  @override
  void initWidgetModel() async {
    _places.accept(await model.places);
    _user.accept(await model.user);
    super.initWidgetModel();
  }

  @override
  Future<void> onSubmit(AddressData data) async {
    await context.showLoadingDialog();
    final result = await model.update(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    context.showToast(
      type: ToastificationType.success,
      title: context.l10n.message_success_edit,
    );

    await context.router.popRoute();
  }

  @override
  StateNotifier<List<Place>?> get places => _places;

  @override
  StateNotifier<UserData?> get user => _user;
}
