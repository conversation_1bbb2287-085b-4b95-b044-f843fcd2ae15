import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/profile/edit/edit_profile_wm.dart';
import 'package:sba/src/feature/profile/edit/widget/edit_form.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class EditProfileScreen extends ElementaryWidget<IEditProfileWidgetModel> {
  const EditProfileScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultEditProfileWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IEditProfileWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(
            text: context.l10n.navigation_profile_edit,
            padding: UISpacing.defaultElementPaddingWithoutBottom,
          ),
        ],
        body: SingleChildScrollView(
          padding: UISpacing.defaultScreenPadding,
          child: DoubleSourceBuilder(
            firstSource: wm.places,
            secondSource: wm.user,
            builder: (context, places, user) => EditForm(
              places: places,
              data: user,
              onSubmit: wm.onSubmit,
            ),
          ),
        ),
      ),
    );
  }
}
