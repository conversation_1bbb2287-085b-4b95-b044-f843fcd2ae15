import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:sba/src/repository/user/user_repository.dart';

typedef AddressData = ({Place place, String address});

class EditProfileModel extends ElementaryModel {
  EditProfileModel({
    required GeneralRepository generalRepository,
    required UserRepository userRepository,
    super.errorHandler,
  })  : _generalRepository = generalRepository,
        _userRepository = userRepository;

  final GeneralRepository _generalRepository;
  final UserRepository _userRepository;

  Future<List<Place>?> get places =>
      _generalRepository.getPlaces().then((e) => e.maybeValue);

  Future<UserData?> get user => _userRepository.getUser();

  Future<Result<void>> update(AddressData data) =>
      _userRepository.updateAddress(place: data.place, address: data.address);
}
