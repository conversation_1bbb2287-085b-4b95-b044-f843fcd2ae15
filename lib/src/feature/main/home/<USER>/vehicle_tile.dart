import 'package:flutter/material.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/toll/model/toll_data.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/widget/label_with_text.dart';
import 'package:sba/src/ui/widget/uri_image.dart';
import 'package:skeletonizer/skeletonizer.dart';

class VehicleTile extends StatelessWidget {
  const VehicleTile({
    required this.data,
    this.toll,
    this.subscription,
    super.key,
  });

  final VehicleData data;
  final SubscriptionData? subscription;
  final TollData? toll;

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.hardEdge,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Skeleton.replace(
            width: double.maxFinite,
            height: double.maxFinite,
            child: UriImage(
              imageUri: data.imageUri,
              placeholder: Assets.images.carPlaceholderWide.path,
              color: data.imageUri == null
                  ? Colors.transparent
                  : UIColors.imageMask,
              fit: BoxFit.cover,
              blendMode: BlendMode.darken,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(UISpacing.m),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${data.brandName} ${data.modelName}',
                        style: context.textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    Assets.icons.directionsCar.svg(
                      width: 24,
                      height: 24,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                    Text(
                      data.plateNumber,
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
                const Spacer(),
                if (subscription != null)
                  LabelWithText(
                    label: context.l10n.home_vehicle_tile_subscription,
                    text: subscription?.dateTo.formatDate(context) ?? '',
                    defaultColor: Colors.white,
                  ),
                if (toll != null)
                  LabelWithText(
                    label: context.l10n.home_vehicle_tile_subscription,
                    text: toll?.to.formatDate(context) ?? '',
                    defaultColor: Colors.white,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
