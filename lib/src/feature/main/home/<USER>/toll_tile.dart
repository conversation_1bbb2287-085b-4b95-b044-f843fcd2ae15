import 'package:flutter/material.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/toll/model/toll_data.dart';
import 'package:sba/src/ui/widget/list_tile_box.dart';

class TollTile extends StatelessWidget {
  const TollTile({required this.data, super.key});

  final TollData data;

  @override
  Widget build(BuildContext context) {
    return ListTileBox(
      icon: Assets.icons.vignette.svg(),
      title: context.l10n.home_toll_tile_title(data.from.formatDate(context)),
      subtitle: context.l10n.home_toll_tile_valid(data.to.formatDate(context)),
    );
  }
}
