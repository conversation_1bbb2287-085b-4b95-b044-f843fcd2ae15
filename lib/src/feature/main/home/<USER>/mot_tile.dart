import 'package:flutter/material.dart';
import 'package:sba/src/common/extension/datetime_extension.dart';
import 'package:sba/src/feature/main/home/<USER>/mot_data.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/widget/list_tile_box.dart';

class MotTile extends StatelessWidget {
  const MotTile({
    required this.data,
    super.key,
  });

  final MotData data;

  @override
  Widget build(BuildContext context) {
    return ListTileBox(
      icon: Assets.icons.warehouse.svg(),
      title: context.l10n.home_mot_tile_title(data.from.formatDate(context)),
      subtitle:
          context.l10n.home_mot_tile_valid(data.valid.formatDate(context)),
    );
  }
}
