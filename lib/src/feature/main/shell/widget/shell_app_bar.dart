import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';

class ShellAppBar extends StatelessWidget implements PreferredSizeWidget {
  const ShellAppBar.user({
    required this.notificationCount,
    super.key,
    this.errorText,
    this.onNotificationTap,
    this.onProfileTap,
    this.onBackTap,
  }) : onRegisterTap = null;

  const ShellAppBar.guest({
    super.key,
    this.onRegisterTap,
    this.onBackTap,
  })  : errorText = null,
        notificationCount = 0,
        onProfileTap = null,
        onNotificationTap = null;

  final String? errorText;
  final int notificationCount;
  final VoidCallback? onNotificationTap;
  final VoidCallback? onProfileTap;
  final VoidCallback? onRegisterTap;
  final VoidCallback? onBackTap;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        padding: UISpacing.defaultElementHorizontalPadding,
        color: context.theme.colorScheme.surface,
        alignment: Alignment.bottomCenter,
        child: NavigationToolbar(
          leading: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: onBackTap == null
                ? Assets.images.appLogo.image(width: 48, height: 48)
                : IconButton(
                    onPressed: onBackTap,
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      size: 28,
                    ),
                  ),
          ),
          middle: errorText != null
              ? _errorWidget(context: context, text: errorText!)
              : const SizedBox.shrink(),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (onNotificationTap != null)
                Badge(
                  label:
                      Text(notificationCount > 9 ? '9+' : '$notificationCount'),
                  isLabelVisible: notificationCount > 0,
                  offset: const Offset(-4, 0),
                  child: IconButton(
                    onPressed: onNotificationTap,
                    icon: Assets.icons.notification.svg(),
                  ),
                ),
              if (onProfileTap != null)
                IconButton(
                  onPressed: onProfileTap,
                  icon: Assets.icons.accountCircle.svg(),
                ),
              if (onRegisterTap != null)
                SizedBox(
                  width: 150,
                  child: FilledButton(
                    onPressed: onRegisterTap,
                    child: Text(
                      context.l10n.action_register,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _errorWidget({required BuildContext context, required String text}) =>
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error,
            size: 16,
            color: context.theme.colorScheme.error,
          ),
          const Gap(UISpacing.xs),
          Text(
            text,
            style: context.textTheme.bodySmall?.copyWith(
              color: context.theme.colorScheme.error,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      );

  @override
  Size get preferredSize => const Size.fromHeight(62);
}
