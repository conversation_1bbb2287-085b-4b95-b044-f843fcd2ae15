import 'package:elementary/elementary.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/notifications/notification_repository.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

class ShellModel extends ElementaryModel {
  ShellModel({
    required UserRepository userRepository,
    required AuthRepository authRepository,
    required VehicleRepository vehicleRepository,
    required NotificationRepository notificationRepository,
    super.errorHandler,
  })  : _userRepository = userRepository,
        _authRepository = authRepository,
        _vehicleRepository = vehicleRepository,
        _notificationRepository = notificationRepository;

  final UserRepository _userRepository;
  final AuthRepository _authRepository;
  final VehicleRepository _vehicleRepository;
  final NotificationRepository _notificationRepository;

  Stream<UserType> get type => _userRepository.type;

  Stream<int> get vehicleCount => _vehicleRepository.vehicleCount.startWith(1);

  Stream<int> get unreadNotificationCount =>
      _notificationRepository.unreadCount.startWith(0);

  Future<Result<void>> logout() => _authRepository.logout();

  Future<void> requestNotificationPermission() =>
      _notificationRepository.requestPermissions();
}
