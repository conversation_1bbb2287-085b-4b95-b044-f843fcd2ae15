
import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/feature/main/model/destination_type.dart';
import 'package:sba/src/feature/main/shell/shell_wm.dart';
import 'package:sba/src/ui/widget/after_layout_listener.dart';

class ShellScreen extends ElementaryWidget<IShellWidgetModel> {
  const ShellScreen({
    required this.navigator,
    required this.state,
    Key? key,
    WidgetModelFactory wmFactory = defaultShellWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final Widget navigator;
  final GoRouterState state;

  @override
  Widget build(IShellWidgetModel wm) {
    return StateNotifierBuilder(
      listenableState: wm.topBar,
      builder: (context, topBar) => Scaffold(
        appBar: topBar,
        body: StateNotifierBuilder(
          listenableState: wm.navigator,
          builder: (context, navigator) => AfterLayoutListener(
            key: <PERSON><PERSON><PERSON>(navigator),
            afterLayout: wm.onLayout,
            child: navigator ?? const SizedBox.shrink(),
          ),
        ),
        bottomNavigationBar: DoubleSourceBuilder(
          firstSource: wm.destinations,
          secondSource: wm.selectedDestination,
          builder: (context, destinations, selected) =>
              destinations?.isEmpty ?? true
                  ? const SizedBox.shrink()
                  : BottomNavigationBar(
                      items: destinations
                              ?.map(
                                (destination) => BottomNavigationBarItem(
                                  icon: destination.icon,
                                  label: destination.localizedName(context),
                                ),
                              )
                              .toList() ??
                          List.empty(),
                      onTap: (i) => wm.onNavigationTap(destinations![i]),
                      currentIndex: destinations?.indexOfNullable(
                            selected,
                            def: destinations.length - 1,
                          ) ??
                          0,
                    ),
        ),
      ),
    );
  }
}
