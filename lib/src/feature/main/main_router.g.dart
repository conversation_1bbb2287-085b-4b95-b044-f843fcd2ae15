// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'main_router.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$mainRoute];

RouteBase get $mainRoute => ShellRouteData.$route(
  navigatorKey: MainRoute.$navigatorKey,
  factory: $MainRouteExtension._fromState,
  routes: [
    GoRouteData.$route(
      path: '/home',
      factory: _$HomeRoute._fromState,
      routes: [
        GoRouteData.$route(
          path: 'profile',
          factory: _$ProfileRoute._fromState,
          routes: [
            GoRouteData.$route(
              path: 'edit',
              factory: _$EditProfileRoute._fromState,
            ),
            GoRouteData.$route(
              path: 'password',
              factory: _$EditPasswordRoute._fromState,
            ),
            GoRouteData.$route(
              path: 'notification_settings',
              factory: _$NotificationSettingsRoute._fromState,
            ),
            GoRouteData.$route(
              path: 'vehicles',
              factory: _$MyVehiclesRoute._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'new',
                  factory: _$NewVehicleRoute._fromState,
                ),
                GoRouteData.$route(
                  path: 'edit',
                  factory: _$EditVehicleRoute._fromState,
                ),
              ],
            ),
          ],
        ),
        GoRouteData.$route(
          path: 'notification',
          factory: _$NotificationRoute._fromState,
        ),
        GoRouteData.$route(
          path: 'subscription_details',
          factory: _$SubscriptionDetailsRoute._fromState,
        ),
      ],
    ),
    GoRouteData.$route(
      path: '/parking',
      factory: _$ParkingRoute._fromState,
      routes: [
        GoRouteData.$route(
          path: 'request',
          factory: _$RequestParkingRoute._fromState,
        ),
      ],
    ),
    GoRouteData.$route(
      path: '/road_assistance',
      factory: _$RoadAssistanceRoute._fromState,
      routes: [
        GoRouteData.$route(
          path: 'request',
          factory: _$RequestRoadAssistanceRoute._fromState,
        ),
        GoRouteData.$route(
          path: 'request_other',
          factory: _$RequestRoadAssistanceOtherRoute._fromState,
        ),
      ],
    ),
    GoRouteData.$route(
      path: '/toll',
      factory: _$TollRoute._fromState,
      routes: [
        GoRouteData.$route(path: 'check', factory: _$CheckTollRoute._fromState),
        GoRouteData.$route(path: 'buy', factory: _$BuyTollRoute._fromState),
      ],
    ),
    GoRouteData.$route(
      path: '/mot',
      factory: _$MotRoute._fromState,
      routes: [
        GoRouteData.$route(
          path: 'request',
          factory: _$MotRequestRoute._fromState,
        ),
      ],
    ),
    GoRouteData.$route(path: '/other', factory: _$OtherRoute._fromState),
    GoRouteData.$route(
      path: '/subscription',
      factory: _$SubscriptionRoute._fromState,
      routes: [
        GoRouteData.$route(
          path: 'activate',
          factory: _$ActivateSubscriptionRoute._fromState,
        ),
        GoRouteData.$route(
          path: 'buy',
          factory: _$BuySubscriptionRoute._fromState,
        ),
      ],
    ),
    GoRouteData.$route(
      path: '/training',
      factory: _$TrainingCenterRoute._fromState,
    ),
    GoRouteData.$route(
      path: '/road_cameras',
      factory: _$RoadCameraRoute._fromState,
    ),
    GoRouteData.$route(
      path: '/legalHelp',
      factory: _$LegalHelpRoute._fromState,
    ),
    GoRouteData.$route(
      path: '/discounts',
      factory: _$DiscountRoute._fromState,
      routes: [
        GoRouteData.$route(
          path: 'details',
          factory: _$DiscountDetailsRoute._fromState,
        ),
      ],
    ),
    GoRouteData.$route(
      path: '/information',
      factory: _$InformationRoute._fromState,
    ),
    GoRouteData.$route(
      path: '/service_book',
      factory: _$ServiceBookRoute._fromState,
      routes: [
        GoRouteData.$route(
          path: 'service',
          factory: _$VehicleServicesRoute._fromState,
          routes: [
            GoRouteData.$route(
              path: 'engine_oil',
              factory: _$EngineOilChangesRoute._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'edit',
                  factory: _$EditEngineOilChangeRoute._fromState,
                ),
              ],
            ),
            GoRouteData.$route(
              path: 'transmission_oil',
              factory: _$TransmissionOilChangesRoute._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'edit',
                  factory: _$EditTransmissionOilChangeRoute._fromState,
                ),
              ],
            ),
            GoRouteData.$route(
              path: 'tyres',
              factory: _$TyresRoute._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'new_tyre',
                  factory: _$NewTyresRoute._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'edit',
                      factory: _$EditNewTyreRoute._fromState,
                    ),
                  ],
                ),
                GoRouteData.$route(
                  path: 'tyre_swap',
                  factory: _$TyreSwapsRoute._fromState,
                  routes: [
                    GoRouteData.$route(
                      path: 'edit',
                      factory: _$EditTyreSwapRoute._fromState,
                    ),
                  ],
                ),
              ],
            ),
            GoRouteData.$route(
              path: 'other',
              factory: _$OtherServicesRoute._fromState,
              routes: [
                GoRouteData.$route(
                  path: 'edit',
                  factory: _$EditOtherServiceRoute._fromState,
                ),
              ],
            ),
          ],
        ),
        GoRouteData.$route(
          path: 'fuel',
          factory: _$RefuelsRoute._fromState,
          routes: [
            GoRouteData.$route(
              path: 'edit',
              factory: _$EditRefuelRoute._fromState,
            ),
          ],
        ),
        GoRouteData.$route(
          path: 'annual_inspection',
          factory: _$AnnualInspectionRoute._fromState,
          routes: [
            GoRouteData.$route(
              path: 'edit',
              factory: _$EditAnnualInspectionRoute._fromState,
            ),
          ],
        ),
      ],
    ),
  ],
);

extension $MainRouteExtension on MainRoute {
  static MainRoute _fromState(GoRouterState state) => const MainRoute();
}

mixin _$HomeRoute on GoRouteData {
  static HomeRoute _fromState(GoRouterState state) => const HomeRoute();

  @override
  String get location => GoRouteData.$location('/home');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$ProfileRoute on GoRouteData {
  static ProfileRoute _fromState(GoRouterState state) => const ProfileRoute();

  @override
  String get location => GoRouteData.$location('/home/<USER>');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditProfileRoute on GoRouteData {
  static EditProfileRoute _fromState(GoRouterState state) =>
      const EditProfileRoute();

  @override
  String get location => GoRouteData.$location('/home/<USER>/edit');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditPasswordRoute on GoRouteData {
  static EditPasswordRoute _fromState(GoRouterState state) =>
      const EditPasswordRoute();

  @override
  String get location => GoRouteData.$location('/home/<USER>/password');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$NotificationSettingsRoute on GoRouteData {
  static NotificationSettingsRoute _fromState(GoRouterState state) =>
      const NotificationSettingsRoute();

  @override
  String get location =>
      GoRouteData.$location('/home/<USER>/notification_settings');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$MyVehiclesRoute on GoRouteData {
  static MyVehiclesRoute _fromState(GoRouterState state) =>
      const MyVehiclesRoute();

  @override
  String get location => GoRouteData.$location('/home/<USER>/vehicles');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$NewVehicleRoute on GoRouteData {
  static NewVehicleRoute _fromState(GoRouterState state) =>
      const NewVehicleRoute();

  @override
  String get location => GoRouteData.$location('/home/<USER>/vehicles/new');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditVehicleRoute on GoRouteData {
  static EditVehicleRoute _fromState(GoRouterState state) =>
      EditVehicleRoute(id: int.parse(state.uri.queryParameters['id']!));

  EditVehicleRoute get _self => this as EditVehicleRoute;

  @override
  String get location => GoRouteData.$location(
    '/home/<USER>/vehicles/edit',
    queryParams: {'id': _self.id.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$NotificationRoute on GoRouteData {
  static NotificationRoute _fromState(GoRouterState state) =>
      const NotificationRoute();

  @override
  String get location => GoRouteData.$location('/home/<USER>');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$SubscriptionDetailsRoute on GoRouteData {
  static SubscriptionDetailsRoute _fromState(GoRouterState state) =>
      SubscriptionDetailsRoute(
        subscriptionId: int.parse(
          state.uri.queryParameters['subscription-id']!,
        ),
      );

  SubscriptionDetailsRoute get _self => this as SubscriptionDetailsRoute;

  @override
  String get location => GoRouteData.$location(
    '/home/<USER>',
    queryParams: {'subscription-id': _self.subscriptionId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$ParkingRoute on GoRouteData {
  static ParkingRoute _fromState(GoRouterState state) => const ParkingRoute();

  @override
  String get location => GoRouteData.$location('/parking');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$RequestParkingRoute on GoRouteData {
  static RequestParkingRoute _fromState(GoRouterState state) =>
      RequestParkingRoute($extra: state.extra as ParkingRequest?);

  RequestParkingRoute get _self => this as RequestParkingRoute;

  @override
  String get location => GoRouteData.$location('/parking/request');

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

mixin _$RoadAssistanceRoute on GoRouteData {
  static RoadAssistanceRoute _fromState(GoRouterState state) =>
      const RoadAssistanceRoute();

  @override
  String get location => GoRouteData.$location('/road_assistance');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$RequestRoadAssistanceRoute on GoRouteData {
  static RequestRoadAssistanceRoute _fromState(GoRouterState state) =>
      const RequestRoadAssistanceRoute();

  @override
  String get location => GoRouteData.$location('/road_assistance/request');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$RequestRoadAssistanceOtherRoute on GoRouteData {
  static RequestRoadAssistanceOtherRoute _fromState(GoRouterState state) =>
      const RequestRoadAssistanceOtherRoute();

  @override
  String get location =>
      GoRouteData.$location('/road_assistance/request_other');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$TollRoute on GoRouteData {
  static TollRoute _fromState(GoRouterState state) => const TollRoute();

  @override
  String get location => GoRouteData.$location('/toll');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$CheckTollRoute on GoRouteData {
  static CheckTollRoute _fromState(GoRouterState state) =>
      const CheckTollRoute();

  @override
  String get location => GoRouteData.$location('/toll/check');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$BuyTollRoute on GoRouteData {
  static BuyTollRoute _fromState(GoRouterState state) => const BuyTollRoute();

  @override
  String get location => GoRouteData.$location('/toll/buy');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$MotRoute on GoRouteData {
  static MotRoute _fromState(GoRouterState state) => const MotRoute();

  @override
  String get location => GoRouteData.$location('/mot');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$MotRequestRoute on GoRouteData {
  static MotRequestRoute _fromState(GoRouterState state) =>
      const MotRequestRoute();

  @override
  String get location => GoRouteData.$location('/mot/request');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$OtherRoute on GoRouteData {
  static OtherRoute _fromState(GoRouterState state) => const OtherRoute();

  @override
  String get location => GoRouteData.$location('/other');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$SubscriptionRoute on GoRouteData {
  static SubscriptionRoute _fromState(GoRouterState state) =>
      const SubscriptionRoute();

  @override
  String get location => GoRouteData.$location('/subscription');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$ActivateSubscriptionRoute on GoRouteData {
  static ActivateSubscriptionRoute _fromState(GoRouterState state) =>
      const ActivateSubscriptionRoute();

  @override
  String get location => GoRouteData.$location('/subscription/activate');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$BuySubscriptionRoute on GoRouteData {
  static BuySubscriptionRoute _fromState(GoRouterState state) =>
      BuySubscriptionRoute(
        renewData: _$convertMapValue(
          'renew-data',
          state.uri.queryParameters,
          int.tryParse,
        ),
      );

  BuySubscriptionRoute get _self => this as BuySubscriptionRoute;

  @override
  String get location => GoRouteData.$location(
    '/subscription/buy',
    queryParams: {
      if (_self.renewData != null) 'renew-data': _self.renewData!.toString(),
    },
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$TrainingCenterRoute on GoRouteData {
  static TrainingCenterRoute _fromState(GoRouterState state) =>
      const TrainingCenterRoute();

  @override
  String get location => GoRouteData.$location('/training');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$RoadCameraRoute on GoRouteData {
  static RoadCameraRoute _fromState(GoRouterState state) =>
      const RoadCameraRoute();

  @override
  String get location => GoRouteData.$location('/road_cameras');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$LegalHelpRoute on GoRouteData {
  static LegalHelpRoute _fromState(GoRouterState state) =>
      const LegalHelpRoute();

  @override
  String get location => GoRouteData.$location('/legalHelp');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$DiscountRoute on GoRouteData {
  static DiscountRoute _fromState(GoRouterState state) => const DiscountRoute();

  @override
  String get location => GoRouteData.$location('/discounts');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$DiscountDetailsRoute on GoRouteData {
  static DiscountDetailsRoute _fromState(GoRouterState state) =>
      DiscountDetailsRoute($extra: state.extra as DiscountItem);

  DiscountDetailsRoute get _self => this as DiscountDetailsRoute;

  @override
  String get location => GoRouteData.$location('/discounts/details');

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

mixin _$InformationRoute on GoRouteData {
  static InformationRoute _fromState(GoRouterState state) =>
      const InformationRoute();

  @override
  String get location => GoRouteData.$location('/information');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$ServiceBookRoute on GoRouteData {
  static ServiceBookRoute _fromState(GoRouterState state) =>
      const ServiceBookRoute();

  @override
  String get location => GoRouteData.$location('/service_book');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$VehicleServicesRoute on GoRouteData {
  static VehicleServicesRoute _fromState(GoRouterState state) =>
      VehicleServicesRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  VehicleServicesRoute get _self => this as VehicleServicesRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EngineOilChangesRoute on GoRouteData {
  static EngineOilChangesRoute _fromState(GoRouterState state) =>
      EngineOilChangesRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  EngineOilChangesRoute get _self => this as EngineOilChangesRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/engine_oil',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditEngineOilChangeRoute on GoRouteData {
  static EditEngineOilChangeRoute _fromState(GoRouterState state) =>
      EditEngineOilChangeRoute(
        vehicleId: _$convertMapValue(
          'vehicle-id',
          state.uri.queryParameters,
          int.tryParse,
        ),
        $extra: state.extra as EngineOilData?,
      );

  EditEngineOilChangeRoute get _self => this as EditEngineOilChangeRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/engine_oil/edit',
    queryParams: {
      if (_self.vehicleId != null) 'vehicle-id': _self.vehicleId!.toString(),
    },
  );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

mixin _$TransmissionOilChangesRoute on GoRouteData {
  static TransmissionOilChangesRoute _fromState(GoRouterState state) =>
      TransmissionOilChangesRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  TransmissionOilChangesRoute get _self => this as TransmissionOilChangesRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/transmission_oil',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditTransmissionOilChangeRoute on GoRouteData {
  static EditTransmissionOilChangeRoute _fromState(GoRouterState state) =>
      EditTransmissionOilChangeRoute(
        vehicleId: _$convertMapValue(
          'vehicle-id',
          state.uri.queryParameters,
          int.tryParse,
        ),
        $extra: state.extra as TransmissionOilData?,
      );

  EditTransmissionOilChangeRoute get _self =>
      this as EditTransmissionOilChangeRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/transmission_oil/edit',
    queryParams: {
      if (_self.vehicleId != null) 'vehicle-id': _self.vehicleId!.toString(),
    },
  );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

mixin _$TyresRoute on GoRouteData {
  static TyresRoute _fromState(GoRouterState state) => TyresRoute(
    vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
  );

  TyresRoute get _self => this as TyresRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/tyres',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$NewTyresRoute on GoRouteData {
  static NewTyresRoute _fromState(GoRouterState state) => NewTyresRoute(
    vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
  );

  NewTyresRoute get _self => this as NewTyresRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/tyres/new_tyre',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditNewTyreRoute on GoRouteData {
  static EditNewTyreRoute _fromState(GoRouterState state) => EditNewTyreRoute(
    vehicleId: _$convertMapValue(
      'vehicle-id',
      state.uri.queryParameters,
      int.tryParse,
    ),
    $extra: state.extra as TyreData?,
  );

  EditNewTyreRoute get _self => this as EditNewTyreRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/tyres/new_tyre/edit',
    queryParams: {
      if (_self.vehicleId != null) 'vehicle-id': _self.vehicleId!.toString(),
    },
  );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

mixin _$TyreSwapsRoute on GoRouteData {
  static TyreSwapsRoute _fromState(GoRouterState state) => TyreSwapsRoute(
    vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
  );

  TyreSwapsRoute get _self => this as TyreSwapsRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/tyres/tyre_swap',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditTyreSwapRoute on GoRouteData {
  static EditTyreSwapRoute _fromState(GoRouterState state) => EditTyreSwapRoute(
    vehicleId: _$convertMapValue(
      'vehicle-id',
      state.uri.queryParameters,
      int.tryParse,
    ),
    $extra: state.extra as TyreSwapData?,
  );

  EditTyreSwapRoute get _self => this as EditTyreSwapRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/tyres/tyre_swap/edit',
    queryParams: {
      if (_self.vehicleId != null) 'vehicle-id': _self.vehicleId!.toString(),
    },
  );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

mixin _$OtherServicesRoute on GoRouteData {
  static OtherServicesRoute _fromState(GoRouterState state) =>
      OtherServicesRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  OtherServicesRoute get _self => this as OtherServicesRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/other',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditOtherServiceRoute on GoRouteData {
  static EditOtherServiceRoute _fromState(GoRouterState state) =>
      EditOtherServiceRoute(
        vehicleId: _$convertMapValue(
          'vehicle-id',
          state.uri.queryParameters,
          int.tryParse,
        ),
        $extra: state.extra as VehicleServiceData?,
      );

  EditOtherServiceRoute get _self => this as EditOtherServiceRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/service/other/edit',
    queryParams: {
      if (_self.vehicleId != null) 'vehicle-id': _self.vehicleId!.toString(),
    },
  );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

mixin _$RefuelsRoute on GoRouteData {
  static RefuelsRoute _fromState(GoRouterState state) => RefuelsRoute(
    vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
  );

  RefuelsRoute get _self => this as RefuelsRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/fuel',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditRefuelRoute on GoRouteData {
  static EditRefuelRoute _fromState(GoRouterState state) => EditRefuelRoute(
    vehicleId: _$convertMapValue(
      'vehicle-id',
      state.uri.queryParameters,
      int.tryParse,
    ),
    $extra: state.extra as RefuelData?,
  );

  EditRefuelRoute get _self => this as EditRefuelRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/fuel/edit',
    queryParams: {
      if (_self.vehicleId != null) 'vehicle-id': _self.vehicleId!.toString(),
    },
  );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

mixin _$AnnualInspectionRoute on GoRouteData {
  static AnnualInspectionRoute _fromState(GoRouterState state) =>
      AnnualInspectionRoute(
        vehicleId: int.parse(state.uri.queryParameters['vehicle-id']!),
      );

  AnnualInspectionRoute get _self => this as AnnualInspectionRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/annual_inspection',
    queryParams: {'vehicle-id': _self.vehicleId.toString()},
  );

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$EditAnnualInspectionRoute on GoRouteData {
  static EditAnnualInspectionRoute _fromState(GoRouterState state) =>
      EditAnnualInspectionRoute(
        vehicleId: _$convertMapValue(
          'vehicle-id',
          state.uri.queryParameters,
          int.tryParse,
        ),
        $extra: state.extra as AnnualInspectionData?,
      );

  EditAnnualInspectionRoute get _self => this as EditAnnualInspectionRoute;

  @override
  String get location => GoRouteData.$location(
    '/service_book/annual_inspection/edit',
    queryParams: {
      if (_self.vehicleId != null) 'vehicle-id': _self.vehicleId!.toString(),
    },
  );

  @override
  void go(BuildContext context) => context.go(location, extra: _self.$extra);

  @override
  Future<T?> push<T>(BuildContext context) =>
      context.push<T>(location, extra: _self.$extra);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location, extra: _self.$extra);

  @override
  void replace(BuildContext context) =>
      context.replace(location, extra: _self.$extra);
}

T? _$convertMapValue<T>(
  String key,
  Map<String, String> map,
  T? Function(String) converter,
) {
  final value = map[key];
  return value == null ? null : converter(value);
}
