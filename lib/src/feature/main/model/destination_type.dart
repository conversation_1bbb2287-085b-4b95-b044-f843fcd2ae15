import 'package:flutter/material.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';

// ignore_for_file: deprecated_member_use_from_same_package
enum DestinationType {
  home('/home'),
  roadAssistance('/road_assistance'),
  mot('/mot'),
  parking('/parking'),
  other('/other'),
  subscription('/subscription'),
  legalHelp('/legalHelp'),
  training('/training'),
  toll('/toll'),
  serviceBook('/service_book'),
  roadCameras('/road_cameras'),
  discounts('/discounts'),
  information('/information');

  const DestinationType(this.path);

  final String path;
}

extension DestinationExtension on DestinationType {
  String localizedName(BuildContext context) => switch (this) {
        DestinationType.mot => context.l10n.navigation_mot,
        DestinationType.parking => context.l10n.navigation_parking,
        DestinationType.subscription => context.l10n.navigation_subscription,
        DestinationType.legalHelp => context.l10n.navigation_legal_help,
        DestinationType.training => context.l10n.navigation_training,
        DestinationType.toll => context.l10n.navigation_toll,
        DestinationType.serviceBook => context.l10n.navigation_service_book,
        DestinationType.roadCameras => context.l10n.navigation_road_cameras,
        DestinationType.home => context.l10n.navigation_home,
        DestinationType.roadAssistance =>
          context.l10n.navigation_road_assistance,
        DestinationType.other => context.l10n.navigation_other,
        DestinationType.discounts => context.l10n.navigation_partners,
        DestinationType.information => context.l10n.navigation_information,
      };

  Widget get icon => Builder(
        builder: (context) {
          final color = IconTheme.of(context).color ?? UIColors.primary;

          return switch (this) {
            DestinationType.mot => Assets.icons.warehouse.svg(
                color: color,
              ),
            DestinationType.parking => Assets.icons.localParking.svg(
                color: color,
              ),
            DestinationType.subscription => Assets.icons.featuredPlayList.svg(
                color: color,
              ),
            DestinationType.legalHelp => Assets.icons.gavel.svg(
                color: color,
              ),
            DestinationType.training => Assets.icons.school.svg(
                color: color,
              ),
            DestinationType.toll => Assets.icons.vignette.svg(
                color: color,
              ),
            DestinationType.serviceBook => Assets.icons.twoPager.svg(
                color: color,
              ),
            DestinationType.roadCameras => Assets.icons.videocam.svg(
                color: color,
              ),
            DestinationType.home => Assets.icons.home.svg(
                color: color,
              ),
            DestinationType.roadAssistance => Assets.icons.carCrash.svg(
                color: color,
              ),
            DestinationType.other => Assets.icons.other.svg(
                color: color,
              ),
            DestinationType.discounts => Assets.icons.partners.svg(
                color: color,
              ),
            DestinationType.information => Assets.icons.information.svg(
                color: color,
              )
          };
        },
      );
}
