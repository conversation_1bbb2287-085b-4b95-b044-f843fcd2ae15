import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/app_router.dart';
import 'package:sba/src/feature/discounts/details/discount_details_screen.dart';
import 'package:sba/src/feature/discounts/view/discounts_screen.dart';
import 'package:sba/src/feature/information/view/information_screen.dart';
import 'package:sba/src/feature/legal_help/view/legal_help_screen.dart';
import 'package:sba/src/feature/main/home/<USER>';
import 'package:sba/src/feature/main/other/other_screen.dart';
import 'package:sba/src/feature/main/shell/shell_screen.dart';
import 'package:sba/src/feature/mot/request/mot_request_screen.dart';
import 'package:sba/src/feature/mot/view/mot_screen.dart';
import 'package:sba/src/feature/notification/view/notification_screen.dart';
import 'package:sba/src/feature/parking/request/request_parking_screen.dart';
import 'package:sba/src/feature/parking/view/parking_screen.dart';
import 'package:sba/src/feature/profile/edit/edit_profile_screen.dart';
import 'package:sba/src/feature/profile/notification_settings/notification_settings_screen.dart';
import 'package:sba/src/feature/profile/password/change_password_screen.dart';
import 'package:sba/src/feature/profile/view/profile_screen.dart';
import 'package:sba/src/feature/road_assistance/request/request_road_assistance_screen.dart';
import 'package:sba/src/feature/road_assistance/request_other/request_road_assistance_for_other_screen.dart';
import 'package:sba/src/feature/road_assistance/view/road_assistance_screen.dart';
import 'package:sba/src/feature/road_camera/view/road_camera_screen.dart';
import 'package:sba/src/feature/service_book/engine_oil/edit/edit_engine_oil_screen.dart';
import 'package:sba/src/feature/service_book/engine_oil/view/engine_oil_screen.dart';
import 'package:sba/src/feature/service_book/fuel/edit/edit_fuel_screen.dart';
import 'package:sba/src/feature/service_book/fuel/view/fuel_screen.dart';
import 'package:sba/src/feature/service_book/mot/edit/edit_mot_service_screen.dart';
import 'package:sba/src/feature/service_book/mot/view/mot_service_screen.dart';
import 'package:sba/src/feature/service_book/other_service/edit/edit_other_service_screen.dart';
import 'package:sba/src/feature/service_book/other_service/view/other_service_screen.dart';
import 'package:sba/src/feature/service_book/tires/buy/edit/edit_tire_screen.dart';
import 'package:sba/src/feature/service_book/tires/buy/view/buy_tire_screen.dart';
import 'package:sba/src/feature/service_book/tires/swap/edit/edit_tire_swap_screen.dart';
import 'package:sba/src/feature/service_book/tires/swap/view/swap_tire_screen.dart';
import 'package:sba/src/feature/service_book/tires/view/tyres_screen.dart';
import 'package:sba/src/feature/service_book/transmission_oil/edit/edit_transmission_oil_screen.dart';
import 'package:sba/src/feature/service_book/transmission_oil/view/transmission_oil_screen.dart';
import 'package:sba/src/feature/service_book/vehicle_service/vehicle_service_screen.dart';
import 'package:sba/src/feature/service_book/view/service_book_screen.dart';
import 'package:sba/src/feature/subscription/activate/activate_subscription_screen.dart';
import 'package:sba/src/feature/subscription/buy/buy_subscription_screen.dart';
import 'package:sba/src/feature/subscription/details/subscription_details_screen.dart';
import 'package:sba/src/feature/subscription/view/subscription_screen.dart';
import 'package:sba/src/feature/toll/buy/toll_buy_screen.dart';
import 'package:sba/src/feature/toll/check/toll_check_screen.dart';
import 'package:sba/src/feature/toll/view/toll_screen.dart';
import 'package:sba/src/feature/training_center/view/training_center_screen.dart';
import 'package:sba/src/feature/vehicle/edit/edit_vehicle_screen.dart';
import 'package:sba/src/feature/vehicle/view/vehicles_screen.dart';
import 'package:sba/src/repository/contact/model/discounts.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/repository/service_book/model/annual_inspection_data.dart';
import 'package:sba/src/repository/service_book/model/engine_oil_data.dart';
import 'package:sba/src/repository/service_book/model/refuel_data.dart';
import 'package:sba/src/repository/service_book/model/transmission_oil_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:sba/src/repository/service_book/model/tyre_swap_data.dart';
import 'package:sba/src/repository/service_book/model/vehicle_service_data.dart';

part '../discounts/discount_routes.dart';
part '../information/information_routes.dart';
part '../legal_help/legal_help_routes.dart';
part '../mot/mot_routes.dart';
part '../notification/notification_routes.dart';
part '../parking/parking_routes.dart';
part '../profile/profile_routes.dart';
part '../road_assistance/road_assistance_routes.dart';
part '../road_camera/road_camera_routes.dart';
part '../service_book/service_book_routes.dart';
part '../subscription/subscription_routes.dart';
part '../toll/toll_routes.dart';
part '../training_center/training_center_routes.dart';
part '../vehicle/vehicle_routes.dart';
part 'main_router.g.dart';

const home = '/home';
const roadAssistance = '/road_assistance';
const mot = '/mot';
const parking = '/parking';
const other = '/other';
const subscription = '/subscription';
const legalHelp = '/legalHelp';
const training = '/training';
const toll = '/toll';
const serviceBook = '/service_book';
const roadCameras = '/road_cameras';
const discounts = '/discounts';
const information = '/information';

@TypedShellRoute<MainRoute>(
  routes: [
    //home
    TypedGoRoute<HomeRoute>(
      path: home,
      routes: [
        TypedGoRoute<ProfileRoute>(
          path: 'profile',
          routes: [
            TypedGoRoute<EditProfileRoute>(path: 'edit'),
            TypedGoRoute<EditPasswordRoute>(path: 'password'),
            TypedGoRoute<NotificationSettingsRoute>(
              path: 'notification_settings',
            ),
            TypedGoRoute<MyVehiclesRoute>(
              path: 'vehicles',
              routes: [
                TypedGoRoute<NewVehicleRoute>(path: 'new'),
                TypedGoRoute<EditVehicleRoute>(path: 'edit'),
              ],
            ),
          ],
        ),
        TypedGoRoute<NotificationRoute>(
          path: 'notification',
        ),
        TypedGoRoute<SubscriptionDetailsRoute>(
          path: 'subscription_details',
        ),
      ],
    ),
    // parking
    TypedGoRoute<ParkingRoute>(
      path: parking,
      routes: [
        TypedGoRoute<RequestParkingRoute>(path: 'request'),
      ],
    ),
    // road assistance
    TypedGoRoute<RoadAssistanceRoute>(
      path: roadAssistance,
      routes: [
        TypedGoRoute<RequestRoadAssistanceRoute>(path: 'request'),
        TypedGoRoute<RequestRoadAssistanceOtherRoute>(path: 'request_other'),
      ],
    ),
    // toll
    TypedGoRoute<TollRoute>(
      path: toll,
      routes: [
        TypedGoRoute<CheckTollRoute>(path: 'check'),
        TypedGoRoute<BuyTollRoute>(path: 'buy'),
      ],
    ),
    // mot
    TypedGoRoute<MotRoute>(
      path: mot,
      routes: [
        TypedGoRoute<MotRequestRoute>(path: 'request'),
      ],
    ),
    // other
    TypedGoRoute<OtherRoute>(path: other),
    // subscription
    TypedGoRoute<SubscriptionRoute>(
      path: subscription,
      routes: [
        TypedGoRoute<ActivateSubscriptionRoute>(path: 'activate'),
        TypedGoRoute<BuySubscriptionRoute>(path: 'buy'),
      ],
    ),
    // training
    TypedGoRoute<TrainingCenterRoute>(path: training),
    // road cameras
    TypedGoRoute<RoadCameraRoute>(path: roadCameras),
    // legal help
    TypedGoRoute<LegalHelpRoute>(path: legalHelp),
    // discounts
    TypedGoRoute<DiscountRoute>(
      path: discounts,
      routes: [
        TypedGoRoute<DiscountDetailsRoute>(path: 'details'),
      ],
    ),
    // information
    TypedGoRoute<InformationRoute>(path: information),
    // service book
    TypedGoRoute<ServiceBookRoute>(
      path: serviceBook,
      routes: [
        TypedGoRoute<VehicleServicesRoute>(
          path: 'service',
          routes: [
            TypedGoRoute<EngineOilChangesRoute>(
              path: 'engine_oil',
              routes: [
                TypedGoRoute<EditEngineOilChangeRoute>(path: 'edit'),
              ],
            ),
            TypedGoRoute<TransmissionOilChangesRoute>(
              path: 'transmission_oil',
              routes: [
                TypedGoRoute<EditTransmissionOilChangeRoute>(path: 'edit'),
              ],
            ),
            TypedGoRoute<TyresRoute>(
              path: 'tyres',
              routes: [
                TypedGoRoute<NewTyresRoute>(
                  path: 'new_tyre',
                  routes: [
                    TypedGoRoute<EditNewTyreRoute>(path: 'edit'),
                  ],
                ),
                TypedGoRoute<TyreSwapsRoute>(
                  path: 'tyre_swap',
                  routes: [
                    TypedGoRoute<EditTyreSwapRoute>(path: 'edit'),
                  ],
                ),
              ],
            ),
            TypedGoRoute<OtherServicesRoute>(
              path: 'other',
              routes: [
                TypedGoRoute<EditOtherServiceRoute>(path: 'edit'),
              ],
            ),
          ],
        ),
        TypedGoRoute<RefuelsRoute>(
          path: 'fuel',
          routes: [
            TypedGoRoute<EditRefuelRoute>(path: 'edit'),
          ],
        ),
        TypedGoRoute<AnnualInspectionRoute>(
          path: 'annual_inspection',
          routes: [
            TypedGoRoute<EditAnnualInspectionRoute>(path: 'edit'),
          ],
        ),
      ],
    ),
  ],
)
class MainRoute extends ShellRouteData {
  const MainRoute();

  static final GlobalKey<NavigatorState> $navigatorKey =
      AppRouter.shellNavigatorKey;

  @override
  Widget builder(BuildContext context, GoRouterState state, Widget navigator) {
    return ShellScreen(
      navigator: navigator,
      state: state,
    );
  }
}

class HomeRoute extends GoRouteData with _$HomeRoute {
  const HomeRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const HomeScreen();
  }
}

class OtherRoute extends GoRouteData with _$OtherRoute {
  const OtherRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const OtherScreen();
  }
}
