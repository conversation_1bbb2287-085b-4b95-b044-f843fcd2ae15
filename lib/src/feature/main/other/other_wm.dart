import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/feature/auth/auth_router.dart';
import 'package:sba/src/feature/main/model/destination.dart';
import 'package:sba/src/feature/main/model/destination_type.dart';
import 'package:sba/src/feature/main/other/other_model.dart';
import 'package:sba/src/feature/main/other/other_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/ui/widget/message_box.dart';

abstract interface class IOtherWidgetModel implements IWidgetModel {
  StateNotifier<List<Destination>> get destinations;

  StateNotifier<Widget?> get warningMessage;

  void onDestinationTap(Destination destination);
}

OtherWidgetModel defaultOtherWidgetModelFactory(BuildContext context) {
  return OtherWidgetModel(
    OtherModel(
      userRepository: get(),
      authRepository: get(),
      errorHandler: get(),
    ),
  );
}

class OtherWidgetModel extends WidgetModel<OtherScreen, OtherModel>
    implements IOtherWidgetModel {
  OtherWidgetModel(super.model);

  final StateNotifier<List<Destination>> _destinations =
      StateNotifier(initValue: _normalDestinations);
  final StateNotifier<Widget?> _warningMessage = StateNotifier();

  final _sub = CompositeSubscription();

  @override
  void initWidgetModel() {
    _sub
      ..add(
        model.type
            .map(
              (type) => switch (type) {
                UserType.normal => null,
                UserType.guest => MessageBox(
                    type: MessageBoxType.error,
                    title: context.l10n.message_feature_locked_title,
                    text: context.l10n.message_feature_locked_text,
                    actionBuilder: (context, color) => [
                      TextButton(
                        onPressed: _onRegisterTap,
                        style: TextButton.styleFrom(foregroundColor: color),
                        child: Text(
                          context.l10n.action_register,
                        ),
                      ),
                      TextButton(
                        onPressed: _onLoginTap,
                        style: TextButton.styleFrom(foregroundColor: color),
                        child: Text(
                          context.l10n.action_login,
                        ),
                      ),
                    ],
                  ),
              },
            )
            .listen(_warningMessage.accept),
      )
      ..add(
        model.type
            .map(
              (type) => switch (type) {
                UserType.normal => _normalDestinations,
                UserType.guest => _guestDestinations
              },
            )
            .listen(_destinations.accept),
      );
    super.initWidgetModel();
  }

  @override
  void dispose() {
    _sub.dispose();
    super.dispose();
  }

  @override
  StateNotifier<List<Destination>> get destinations => _destinations;

  @override
  StateNotifier<Widget?> get warningMessage => _warningMessage;

  @override
  void onDestinationTap(Destination destination) {
    context.go(destination.type.path);
  }

  Future<void> _onLoginTap() async {
    await model.logout();
  }

  Future<void> _onRegisterTap() async {
    await model.logout();
    const RegisterRoute().go(context);
  }

  static const _normalDestinations = [
    Destination(type: DestinationType.mot),
    Destination(type: DestinationType.subscription),
    Destination(type: DestinationType.legalHelp),
    Destination(type: DestinationType.training),
    Destination(type: DestinationType.toll),
    Destination(type: DestinationType.serviceBook),
    Destination(type: DestinationType.roadCameras),
    Destination(type: DestinationType.discounts),
    Destination(type: DestinationType.information),
  ];

  static const _guestDestinations = [
    Destination(type: DestinationType.parking, enabled: false),
    Destination(type: DestinationType.subscription, enabled: false),
    Destination(type: DestinationType.legalHelp, enabled: false),
    Destination(type: DestinationType.training),
    Destination(type: DestinationType.toll, enabled: false),
    Destination(type: DestinationType.serviceBook, enabled: false),
    Destination(type: DestinationType.roadCameras),
    Destination(type: DestinationType.discounts),
    Destination(type: DestinationType.information),
  ];
}
