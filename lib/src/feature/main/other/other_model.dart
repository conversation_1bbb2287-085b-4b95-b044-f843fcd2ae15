import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/user_repository.dart';

class OtherModel extends ElementaryModel {
  OtherModel({
    required UserRepository userRepository,
    required AuthRepository authRepository,
    super.errorHandler,
  })  : _userRepository = userRepository,
        _authRepository = authRepository;

  final UserRepository _userRepository;
  final AuthRepository _authRepository;

  Stream<UserType> get type => _userRepository.type;

  Future<Result<void>> logout() => _authRepository.logout();
}
