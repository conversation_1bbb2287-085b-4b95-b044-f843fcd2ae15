import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/feature/main/model/destination_type.dart';
import 'package:sba/src/feature/main/other/other_wm.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/navigation_box.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class OtherScreen extends ElementaryWidget<IOtherWidgetModel> {
  const OtherScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultOtherWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IOtherWidgetModel wm) {
    return Scaffold(
      body: Builder(
        builder: (context) {
          return NestedScrollView(
            headerSliverBuilder: (context, _) => [
              SliverTitle(
                text: context.l10n.navigation_other,
                padding: UISpacing.defaultScreenPadding,
              ),
            ],
            body: CustomScrollView(
              slivers: [
                StateNotifierBuilder(
                  listenableState: wm.warningMessage,
                  builder: (context, message) => SliverToBoxAdapter(
                    child: message != null
                        ? Padding(
                      padding: UISpacing.defaultElementPaddingWithoutTop,
                      child: message,
                    )
                        : const SizedBox.shrink(),
                  ),
                ),
                StateNotifierBuilder(
                  listenableState: wm.destinations,
                  builder: (context, destinations) => SliverPadding(
                    padding: UISpacing.defaultElementPaddingWithoutTop,
                    sliver: SliverGrid.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        mainAxisSpacing: UISpacing.s,
                        crossAxisSpacing: UISpacing.s,
                      ),
                      itemCount: destinations?.length ?? 0,
                      itemBuilder: (context, i) {
                        final data = destinations![i];
                        return NavigationBox(
                          icon: data.type.icon,
                          text: data.type.localizedName(context),
                          enabled: data.enabled,
                          onTap: () => wm.onDestinationTap(data),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
