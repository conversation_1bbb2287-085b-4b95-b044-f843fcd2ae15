import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/contact/model/training_center.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class CenterBottomSheet extends StatelessWidget {
  const CenterBottomSheet({
    required this.data,
    super.key,
    this.callAction,
    this.emailAction,
    this.onNavigateTap,
  });

  final TrainingCenter data;
  final ValueChanged<String>? callAction;
  final ValueChanged<String>? emailAction;
  final VoidCallback? onNavigateTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: UISpacing.defaultScreenPadding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            data.name ?? '',
            style: context.textTheme.headlineLarge,
            textAlign: TextAlign.center,
          ),
          const Gap(UISpacing.xl),
          ListTile(
            onTap: onNavigateTap,
            leading: Icon(
              Icons.home,
              color: context.theme.primaryColor,
            ),
            title: Text(data.fullAddress),
          ),
          if (data.phone?.isNotEmpty ?? false)
            ListTile(
              onTap: () => callAction?.call(data.phone!),
              leading: Icon(
                Icons.phone,
                color: context.theme.primaryColor,
              ),
              title: Text(data.phone!),
            ),
          if (data.mobile?.isNotEmpty ?? false)
            ListTile(
              onTap: () => callAction?.call(data.mobile!),
              leading: Icon(
                Icons.phone_android,
                color: context.theme.primaryColor,
              ),
              title: Text(data.mobile!),
            ),
          if (data.email?.isNotEmpty ?? false)
            ListTile(
              onTap: () => emailAction?.call(data.email!),
              leading: Icon(
                Icons.email_outlined,
                color: context.theme.primaryColor,
              ),
              title: Text(data.email!),
            ),
          const Gap(UISpacing.xl),
          FilledButton(
            onPressed: onNavigateTap,
            child: Text(context.l10n.action_navigate),
          ),
        ],
      ),
    );
  }
}
