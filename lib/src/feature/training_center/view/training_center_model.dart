import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/contact/contact_repository.dart';
import 'package:sba/src/repository/contact/model/training_center.dart';

class TrainingCenterModel extends ElementaryModel {
  TrainingCenterModel({
    required ContactRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final ContactRepository _repository;

  Future<Result<List<TrainingCenter>>> getTrainingCenters() =>
      _repository.getTrainingCenters();
}
