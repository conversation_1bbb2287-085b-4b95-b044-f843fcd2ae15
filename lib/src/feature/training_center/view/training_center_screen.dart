import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/training_center/view/training_center_wm.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';
import 'package:sba/src/ui/widget/custom_expansion_tile.dart';
import 'package:sba/src/ui/widget/open_street_map.dart';
import 'package:sba/src/ui/widget/sliver_subtitle.dart';
import 'package:sba/src/ui/widget/sliver_text.dart';
import 'package:sba/src/ui/widget/sliver_title.dart';

class TrainingCenterScreen
    extends ElementaryWidget<ITrainingCenterWidgetModel> {
  const TrainingCenterScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultTrainingCenterWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(ITrainingCenterWidgetModel wm) {
    return Scaffold(
      body: Builder(
        builder: (context) {
          return NestedScrollView(
            headerSliverBuilder: (context, _) => [
              SliverTitle(
                text: context.l10n.navigation_training,
                padding: UISpacing.defaultScreenPadding,
              ),
            ],
            body: CustomScrollView(
              slivers: [
                SliverSubtitle(
                  text: context.l10n.training_map_subtitle,
                  padding: UISpacing.defaultElementHorizontalPadding,
                ),
                const SliverGap(UISpacing.l),
                SliverToBoxAdapter(
                  child: EntityStateNotifierBuilder(
                    listenableEntityState: wm.markerState,
                    builder: (context, data) {
                      return OpenStreetMap(
                        padding: UISpacing.defaultElementHorizontalPadding,
                        markers: data,
                        animateMapBoundsToFitMarkers: true,
                        onMarkerTap: (data) =>
                            wm.onTrainingCenterTap(data.data),
                      );
                    },
                  ),
                ),
                const SliverGap(UISpacing.xl),
                SliverSubtitle(
                  text: context.l10n.training_description_subtitle,
                  padding: UISpacing.defaultElementHorizontalPadding,
                ),
                const SliverGap(UISpacing.l),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: UISpacing.defaultElementHorizontalPadding,
                    child: Assets.images.trainingCenterImage.image(),
                  ),
                ),
                const SliverGap(UISpacing.l),
                SliverText(
                  text: context.l10n.training_description_text,
                  padding: UISpacing.defaultElementHorizontalPadding,
                ),
                const SliverGap(UISpacing.l),
                SliverPadding(
                  padding: UISpacing.defaultElementPaddingWithoutTop,
                  sliver: SliverList.list(
                    children: [
                      CustomExpansionTile(
                        title: context.l10n.training_item_1_title,
                        text: context.l10n.training_item_1_text,
                      ),
                      const Divider(),
                      CustomExpansionTile(
                        title: context.l10n.training_item_2_title,
                        text: context.l10n.training_item_2_text,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
