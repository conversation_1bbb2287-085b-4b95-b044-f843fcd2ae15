import 'dart:async';

import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/external_launcher/external_launcher.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/training_center/view/training_center_model.dart';
import 'package:sba/src/feature/training_center/view/training_center_screen.dart';
import 'package:sba/src/feature/training_center/widget/center_bottom_sheet.dart';
import 'package:sba/src/repository/contact/model/training_center.dart';
import 'package:sba/src/ui/widget/open_street_map.dart';

abstract interface class ITrainingCenterWidgetModel implements IWidgetModel {
  EntityStateNotifier<List<MarkerData<TrainingCenter>>> get markerState;

  void onTrainingCenterTap(TrainingCenter data);
}

TrainingCenterWidgetModel defaultTrainingCenterWidgetModelFactory(
  BuildContext context,
) {
  return TrainingCenterWidgetModel(
    TrainingCenterModel(
      repository: get(),
      errorHandler: get(),
    ),
  );
}

class TrainingCenterWidgetModel
    extends WidgetModel<TrainingCenterScreen, TrainingCenterModel>
    implements ITrainingCenterWidgetModel {
  TrainingCenterWidgetModel(super.model);

  final EntityStateNotifier<List<MarkerData<TrainingCenter>>> _markerState =
      EntityStateNotifier();

  @override
  void initWidgetModel() {
    loadData();
    super.initWidgetModel();
  }

  Future<void> loadData() async {
    _markerState.loading();

    final result = await model.getTrainingCenters();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result as Failure);
    }

    _markerState.content(
      result.maybeValue
              ?.map(
                (e) => MarkerData(coordinates: e.coordinates, data: e),
              )
              .toList(growable: false) ??
          List.empty(),
    );
  }

  @override
  EntityStateNotifier<List<MarkerData<TrainingCenter>>> get markerState =>
      _markerState;

  @override
  void onTrainingCenterTap(TrainingCenter data) {
    context.showModalBottomSheet<void>(
      builder: (context) => CenterBottomSheet(
        data: data,
        callAction: ExternalLauncher.call,
        emailAction: ExternalLauncher.mail,
        onNavigateTap: () =>
            ExternalLauncher.launchNavigation(data.coordinates),
      ),
    );
  }
}
