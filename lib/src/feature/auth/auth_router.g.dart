// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_router.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$signInRoute];

RouteBase get $signInRoute => GoRouteData.$route(
  path: '/auth',
  factory: _$SignInRoute._fromState,
  routes: [
    GoRouteData.$route(path: 'recovery', factory: _$RecoveryRoute._fromState),
    GoRouteData.$route(path: 'register', factory: _$RegisterRoute._fromState),
  ],
);

mixin _$SignInRoute on GoRouteData {
  static SignInRoute _fromState(GoRouterState state) => const SignInRoute();

  @override
  String get location => GoRouteData.$location('/auth');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$RecoveryRoute on GoRouteData {
  static RecoveryRoute _fromState(GoRouterState state) => const RecoveryRoute();

  @override
  String get location => GoRouteData.$location('/auth/recovery');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$RegisterRoute on GoRouteData {
  static RegisterRoute _fromState(GoRouterState state) => const RegisterRoute();

  @override
  String get location => GoRouteData.$location('/auth/register');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
