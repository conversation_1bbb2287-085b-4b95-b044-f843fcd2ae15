import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class AuthHeader extends StatelessWidget {
  const AuthHeader({required this.title, required this.subtitle, super.key});

  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: UISpacing.defaultElementHorizontalPadding,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const MaxGap(54),
          Assets.images.appLogo.image(
            width: 120,
            height: 120,
          ),
          const MaxGap(54),
          Text(
            title,
            style: context.textTheme.headlineLarge,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const Gap(UISpacing.m),
          Text(
            subtitle,
            style: context.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
