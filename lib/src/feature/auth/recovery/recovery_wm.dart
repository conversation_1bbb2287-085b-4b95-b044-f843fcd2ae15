import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/auth/auth_router.dart';
import 'package:sba/src/feature/auth/recovery/recovery_model.dart';
import 'package:sba/src/feature/auth/recovery/recovery_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';

abstract interface class IRecoveryWidgetModel implements IWidgetModel {
  Future<void> onRecoveryTap({required String email});
}

RecoveryWidgetModel defaultRecoveryWidgetModelFactory(BuildContext context) {
  return RecoveryWidgetModel(
    RecoveryModel(authRepository: get(), errorHandler: get()),
  );
}

class RecoveryWidgetModel extends WidgetModel<RecoveryScreen, RecoveryModel>
    implements IRecoveryWidgetModel {
  RecoveryWidgetModel(super.model);

  @override
  Future<void> onRecoveryTap({required String email}) async {
    await context.showLoadingDialog();
    final result = await model.sendRecoveryEmail(email);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(
        failure: result,
        httpErrorBuilder: (code) => switch (code) {
          404 => MessageDialog.action(
              type: MessageDialogType.error,
              title: context.l10n.recovery_title,
              text: context.l10n.error_email_not_found,
              primaryActionText: context.l10n.action_send,
              secondaryActionText: context.l10n.action_cancel,
              primaryAction: () => _sendActivationMail(email),
            ),
          _ => null
        },
      );

      return;
    }

    await context.showMessageDialog(
      builder: (context) => MessageDialog.info(
        type: MessageDialogType.success,
        title: context.l10n.recovery_title,
        text: context.l10n.recovery_message_success_text,
        actionText: context.l10n.action_ok,
      ),
    );

    const SignInRoute().go(context);
  }

  Future<void> _sendActivationMail(String email) async {
    await context.showLoadingDialog();
    final result = await model.sendActivationEmail(email);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    await context.showMessageDialog(
      builder: (context) => MessageDialog.info(
        type: MessageDialogType.success,
        title: context.l10n.login_send_message_success_title,
        text: context.l10n.login_send_message_success_text,
        actionText: context.l10n.action_ok,
      ),
    );
  }
}
