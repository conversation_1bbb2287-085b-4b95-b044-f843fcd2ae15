import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/auth/recovery/recovery_wm.dart';
import 'package:sba/src/feature/auth/recovery/widget/recovery_form.dart';
import 'package:sba/src/feature/auth/widget/auth_header.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';

class RecoveryScreen extends ElementaryWidget<IRecoveryWidgetModel> {
  const RecoveryScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultRecoveryWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IRecoveryWidgetModel wm) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: UISpacing.defaultScreenPadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Builder(
                builder: (context) => AuthHeader(
                  title: context.l10n.recovery_title,
                  subtitle: context.l10n.recovery_subtitle,
                ),
              ),
              const Gap(UISpacing.xxl),
              RecoveryForm(
                callback: (data) => wm.onRecoveryTap(email: data.email),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
