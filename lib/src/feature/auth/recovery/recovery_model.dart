import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';

class RecoveryModel extends ElementaryModel {
  RecoveryModel({required AuthRepository authRepository, super.errorHandler})
      : _authRepository = authRepository;

  final AuthRepository _authRepository;

  Future<Result<void>> sendRecoveryEmail(String email) =>
      _authRepository.forgottenPassword(email: email);

  Future<Result<void>> sendActivationEmail(String email) =>
      _authRepository.resendActivation(email: email);
}
