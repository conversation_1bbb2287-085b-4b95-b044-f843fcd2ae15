import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/auth/auth_router.dart';
import 'package:sba/src/feature/auth/login/login_model.dart';
import 'package:sba/src/feature/auth/login/login_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/auth/model/login_data.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';

abstract interface class ILoginWidgetModel implements IWidgetModel {
  Future<void> onLoginClick(LoginData data);

  Future<void> onGuestLoginClick();

  void onRegisterClick();

  void onForgottenPasswordClick();
}

LoginWidgetModel defaultLoginWidgetModelFactory(BuildContext context) {
  return LoginWidgetModel(
    LoginModel(
      authRepository: get(),
      userRepository: get(),
      errorHandler: get(),
    ),
  );
}

class LoginWidgetModel extends WidgetModel<LoginScreen, LoginModel>
    implements ILoginWidgetModel {
  LoginWidgetModel(super.model);

  @override
  void onForgottenPasswordClick() {
    const RecoveryRoute().go(context);
  }

  @override
  void onRegisterClick() {
    const RegisterRoute().go(context);
  }

  @override
  Future<void> onGuestLoginClick() async {
    await context.showLoadingDialog();
    final result = await model.guestLogin();
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
    }
  }

  @override
  Future<void> onLoginClick(LoginData data) async {
    await context.showLoadingDialog();
    final result = await model.login(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(
        failure: result,
        httpErrorBuilder: (code) => switch (code) {
          403 => MessageDialog.action(
              type: MessageDialogType.error,
              title: context.l10n.error_title,
              text: context.l10n.error_activation,
              primaryActionText: context.l10n.action_send,
              secondaryActionText: context.l10n.action_cancel,
              primaryAction: () => _sendActivationMail(data.email),
            ),
          404 => MessageDialog.error(
              context: context,
              text: context.l10n.error_wrong_username,
            ),
          _ => null
        },
      );
    }
  }

  Future<void> _sendActivationMail(String email) async {
    await context.showLoadingDialog();
    final result = await model.sendActivationEmail(email);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
      return;
    }

    await context.showMessageDialog(
      builder: (context) => MessageDialog.info(
        type: MessageDialogType.success,
        title: context.l10n.login_send_message_success_title,
        text: context.l10n.login_send_message_success_text,
        actionText: context.l10n.action_ok,
      ),
    );
  }
}
