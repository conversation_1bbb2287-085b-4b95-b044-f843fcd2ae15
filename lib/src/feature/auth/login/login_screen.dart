import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/auth/login/login_wm.dart';
import 'package:sba/src/feature/auth/login/widget/login_form.dart';
import 'package:sba/src/feature/auth/widget/auth_header.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';

class LoginScreen extends ElementaryWidget<ILoginWidgetModel> {
  const LoginScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultLoginWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(ILoginWidgetModel wm) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: UISpacing.defaultScreenPadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Builder(
                builder: (context) => AuthHeader(
                  title: context.l10n.login_title,
                  subtitle: context.l10n.login_subtitle,
                ),
              ),
              const Gap(UISpacing.xxl),
              LoginForm(
                onLoginTap: wm.onLoginClick,
                onForgottenPasswordTap: wm.onForgottenPasswordClick,
                onLoginGuestTap: wm.onGuestLoginClick,
                onRegisterTap: wm.onRegisterClick,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
