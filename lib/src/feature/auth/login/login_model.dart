import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/auth/model/login_data.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/user_repository.dart';

class LoginModel extends ElementaryModel {
  LoginModel({
    required AuthRepository authRepository,
    required UserRepository userRepository,
    super.errorHandler,
  })  : _authRepository = authRepository,
        _userRepository = userRepository;

  final AuthRepository _authRepository;
  final UserRepository _userRepository;

  Future<Result<void>> login(LoginData data) async {
    final result = await _authRepository.login(data: data);
    await _userRepository.updateType(UserType.normal);
    return result.map((e) => {});
  }

  Future<Result<void>> guestLogin() async {
    final result = await _authRepository.guestLogin();
    await _userRepository.updateType(UserType.guest);
    return result;
  }

  Future<Result<void>> sendActivationEmail(String email) =>
      _authRepository.resendActivation(email: email);
}
