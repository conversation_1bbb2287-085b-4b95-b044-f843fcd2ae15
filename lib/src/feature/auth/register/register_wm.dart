import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/feature/auth/auth_router.dart';
import 'package:sba/src/feature/auth/register/register_model.dart';
import 'package:sba/src/feature/auth/register/register_screen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/auth/model/register_data.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';

abstract interface class IRegisterWidgetModel implements IWidgetModel {
  StateNotifier<List<Place>> get places;

  Future<void> onRegisterTap(RegisterData data);

  Future<bool> checkEmail(String email);

  void onSignInTap();
}

RegisterWidgetModel defaultRegisterWidgetModelFactory(BuildContext context) {
  return RegisterWidgetModel(
    RegisterModel(
      authRepository: get(),
      generalRepository: get(),
      errorHandler: get(),
    ),
  );
}

class RegisterWidgetModel extends WidgetModel<RegisterScreen, RegisterModel>
    implements IRegisterWidgetModel {
  RegisterWidgetModel(super.model);

  final _places = StateNotifier<List<Place>>();

  @override
  void initWidgetModel() async {
    _places.accept(await model.getPlaces());
    super.initWidgetModel();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Future<void> onRegisterTap(RegisterData data) async {
    await context.showLoadingDialog();
    final result = await model.register(data);
    context.hideLoadingDialog();

    if (result is Failure) {
      await context.showGeneralErrorDialog(failure: result);
    }

    await context.showMessageDialog(
      builder: (context) => MessageDialog.info(
        type: MessageDialogType.success,
        title: context.l10n.register_message_success_title,
        text: context.l10n.register_message_success_text,
        actionText: context.l10n.action_ok,
      ),
    );

    const SignInRoute().go(context);
  }

  @override
  Future<bool> checkEmail(String email) async {
    await context.showLoadingDialog();
    final result = await model.checkEmail(email);
    context.hideLoadingDialog();
    return result;
  }

  @override
  void onSignInTap() {
    const SignInRoute().go(context);
  }

  @override
  StateNotifier<List<Place>> get places => _places;
}
