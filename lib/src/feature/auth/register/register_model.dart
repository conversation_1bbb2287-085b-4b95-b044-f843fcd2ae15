import 'package:elementary/elementary.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/auth/model/register_data.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/general/model/place.dart';

class RegisterModel extends ElementaryModel {
  RegisterModel({
    required AuthRepository authRepository,
    required GeneralRepository generalRepository,
    super.errorHandler,
  })  : _authRepository = authRepository,
        _generalRepository = generalRepository;

  final AuthRepository _authRepository;
  final GeneralRepository _generalRepository;

  Future<Result<void>> register(RegisterData data) =>
      _authRepository.register(data: data);

  Future<bool> checkEmail(String email) =>
      _authRepository.checkEmail(email: email);

  Future<List<Place>?> getPlaces() =>
      _generalRepository.getPlaces().then((e) => e.maybeValue);
}
