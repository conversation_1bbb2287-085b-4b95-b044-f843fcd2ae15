import 'package:elementary/elementary.dart';
import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/feature/auth/register/register_wm.dart';
import 'package:sba/src/feature/auth/register/widget/register_form.dart';
import 'package:sba/src/feature/auth/widget/auth_header.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';

class RegisterScreen extends ElementaryWidget<IRegisterWidgetModel> {
  const RegisterScreen({
    Key? key,
    WidgetModelFactory wmFactory = defaultRegisterWidgetModelFactory,
  }) : super(wmFactory, key: key);

  @override
  Widget build(IRegisterWidgetModel wm) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: UISpacing.defaultScreenPadding,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Builder(
                builder: (context) => AuthHeader(
                  title: context.l10n.register_title,
                  subtitle: context.l10n.register_subtitle,
                ),
              ),
              const Gap(UISpacing.xxl),
              StateNotifierBuilder(
                listenableState: wm.places,
                builder: (context, data) => RegisterForm(
                  onSignInClick: wm.onSignInTap,
                  onRegisterClick: wm.onRegisterTap,
                  checkEmail: wm.checkEmail,
                  places: data,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
