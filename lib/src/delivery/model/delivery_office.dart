import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/delivery/delivery_office_dto.dart';
import 'package:sba/src/api/model/request/delivery_request_dto.dart';

@immutable
final class DeliveryOffice {
  const DeliveryOffice({
    required this.id,
    required this.siteId,
    required this.name,
    required this.address,
  });

  factory DeliveryOffice.fromDto(DeliveryOfficeDto dto) => DeliveryOffice(
        id: dto.id,
        siteId: dto.siteId,
        name: dto.name,
        address: dto.address.fullAddressString,
      );

  static DeliveryOffice? fromRequestDto(DeliveryRequestDto dto) =>
      dto.speedyOfficeId == null
          ? null
          : DeliveryOffice(
              id: dto.speedyOfficeId!,
              name: dto.speedyOffice ?? '',
              siteId: 0,
              address: '',
            );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeliveryOffice &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  final int id;
  final int siteId;
  final String name;
  final String address;
}
