import 'package:sba/src/api/model/request/delivery_request_dto.dart';
import 'package:sba/src/delivery/model/delivery_address_item.dart';

final class AddressDeliveryData {
  AddressDeliveryData({
    required this.city,
    required this.street,
    required this.complex,
    required this.block,
    required this.number,
    required this.entrance,
    required this.floor,
    required this.apartment,
    required this.note,
  });

  static AddressDeliveryData? fromDto(DeliveryRequestDto dto) =>
      dto.cityId == null || dto.streetId == null
          ? null
          : AddressDeliveryData(
              city: DeliveryAddressItem(
                id: dto.cityId!,
                name: dto.city ?? '',
              ),
              street: DeliveryAddressItem(
                id: dto.streetId!,
                name: dto.street ?? '',
              ),
              complex: dto.complexId == null
                  ? null
                  : DeliveryAddressItem(
                      id: dto.complexId!,
                      name: dto.complex ?? '',
                    ),
              block: dto.blockId == null
                  ? null
                  : DeliveryAddressItem(
                      id: dto.blockId!,
                      name: dto.block ?? '',
                    ),
              number: dto.streetNumber,
              entrance: dto.entranceNumber,
              floor: dto.floor,
              apartment: dto.apartmentNumber,
              note: dto.note,
            );

  final DeliveryAddressItem city;
  final DeliveryAddressItem street;
  final DeliveryAddressItem? complex;
  final DeliveryAddressItem? block;
  final String? number;
  final String? entrance;
  final String? floor;
  final String? apartment;
  final String? note;
}
