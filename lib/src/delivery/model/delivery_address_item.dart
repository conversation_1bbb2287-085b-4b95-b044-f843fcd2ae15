import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/delivery/delivery_block_dto.dart';
import 'package:sba/src/api/model/delivery/delivery_city_dto.dart';
import 'package:sba/src/api/model/delivery/delivery_complex_dto.dart';
import 'package:sba/src/api/model/delivery/delivery_street_dto.dart';

@immutable
final class DeliveryAddressItem {
  const DeliveryAddressItem({
    required this.id,
    required this.name,
  });

  factory DeliveryAddressItem.fromCity(DeliveryCityDto dto) =>
      DeliveryAddressItem(
        id: dto.id,
        name: '${dto.name}, ${dto.municipality}',
      );

  factory DeliveryAddressItem.fromStreet(DeliveryStreetDto dto) =>
      DeliveryAddressItem(
        id: dto.id,
        name: dto.name,
      );

  factory DeliveryAddressItem.fromComplex(DeliveryComplexDto dto) =>
      DeliveryAddressItem(
        id: dto.id,
        name: dto.name,
      );

  factory DeliveryAddressItem.fromBlock(DeliveryBlockDto dto) =>
      DeliveryAddressItem(
        id: dto.siteId,
        name: dto.name,
      );

  final int id;
  final String name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeliveryAddressItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
