import 'package:logger/logger.dart';
import 'package:sba/src/api/delivery_api.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/delivery/model/delivery_address_item.dart';
import 'package:sba/src/delivery/model/delivery_office.dart';

const _tag = 'DeliveryRepository';

final class DeliveryRepository {
  DeliveryRepository({
    required DeliveryApi api,
    required Logger logger,
  })  : _api = api,
        _logger = logger;

  final DeliveryApi _api;
  final Logger _logger;

  Future<Result<List<DeliveryAddressItem>>> findCity(String name) async {
    try {
      final result = await callWithResult(() => _api.findCity(name: name));
      return result.map(
        (e) => e.data.map(DeliveryAddressItem.fromCity).toList(),
      );
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<DeliveryAddressItem>>> findStreet({
    required DeliveryAddressItem city,
    required String name,
  }) async {
    try {
      final result = await callWithResult(
        () => _api.findStreet(name: name, siteId: city.id),
      );
      return result.map(
        (e) => e.data.map(DeliveryAddressItem.fromStreet).toList(),
      );
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<DeliveryAddressItem>>> findComplex({
    required DeliveryAddressItem city,
    required String name,
  }) async {
    try {
      final result = await callWithResult(
        () => _api.findComplex(name: name, siteId: city.id),
      );
      return result.map(
        (e) => e.data.map(DeliveryAddressItem.fromComplex).toList(),
      );
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<DeliveryAddressItem>>> findBlock({
    required DeliveryAddressItem city,
    required String name,
  }) async {
    try {
      final result = await callWithResult(
        () => _api.findBlock(name: name, siteId: city.id),
      );
      return result.map(
        (e) => e.data.map(DeliveryAddressItem.fromBlock).toList(),
      );
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<DeliveryOffice>>> findDeliveryOffice(String name) async {
    try {
      final result = await callWithResult(() => _api.findOffice(name: name));
      return result.map(
        (e) => e.data.map(DeliveryOffice.fromDto).toList(),
      );
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
