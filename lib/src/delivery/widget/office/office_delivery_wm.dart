import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/delivery/model/delivery_office.dart';
import 'package:sba/src/delivery/widget/office/office_delivery_form.dart';
import 'package:sba/src/delivery/widget/office/office_delivery_model.dart';

abstract interface class IOfficeDeliveryWidgetModel implements IWidgetModel {
  Future<List<DeliveryOffice>?> findDelivery(String query);
}

OfficeDeliveryWidgetModel defaultOfficeDeliveryWidgetModelFactory(
  BuildContext context,
) {
  return OfficeDeliveryWidgetModel(
    OfficeDeliveryModel(
      repository: get(),
      errorHandler: get(),
    ),
  );
}

class OfficeDeliveryWidgetModel
    extends WidgetModel<OfficeDeliveryWidget, OfficeDeliveryModel>
    implements IOfficeDeliveryWidgetModel {
  OfficeDeliveryWidgetModel(super.model);

  @override
  Future<List<DeliveryOffice>?> findDelivery(String query) =>
      model.findOffice(query);
}
