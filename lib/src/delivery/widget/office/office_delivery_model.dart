import 'package:elementary/elementary.dart';
import 'package:sba/src/delivery/model/delivery_office.dart';
import 'package:sba/src/delivery/repository/delivery_repository.dart';

class OfficeDeliveryModel extends ElementaryModel {
  OfficeDeliveryModel({
    required DeliveryRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final DeliveryRepository _repository;

  Future<List<DeliveryOffice>?> findOffice(String query) =>
      _repository.findDeliveryOffice(query).then(
            (e) => e.maybeValue,
          );
}
