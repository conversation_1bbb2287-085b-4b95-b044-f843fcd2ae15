import 'package:elementary/elementary.dart';
import 'package:sba/src/delivery/model/delivery_address_item.dart';
import 'package:sba/src/delivery/repository/delivery_repository.dart';

class AddressDeliveryModel extends ElementaryModel {
  AddressDeliveryModel({
    required DeliveryRepository repository,
    super.errorHandler,
  }) : _repository = repository;

  final DeliveryRepository _repository;

  Future<List<DeliveryAddressItem>?> findCity(String query) =>
      _repository.findCity(query).then(
            (e) => e.maybeValue,
          );

  Future<List<DeliveryAddressItem>?> findStreet(
    DeliveryAddressItem city,
    String query,
  ) =>
      _repository.findStreet(city: city, name: query).then(
            (e) => e.maybeValue,
          );

  Future<List<DeliveryAddressItem>?> findComplex(
    DeliveryAddressItem city,
    String query,
  ) =>
      _repository.findComplex(city: city, name: query).then(
            (e) => e.maybeValue,
          );

  Future<List<DeliveryAddressItem>?> findBlock(
    DeliveryAddressItem city,
    String query,
  ) =>
      _repository.findBlock(city: city, name: query).then(
            (e) => e.maybeValue,
          );
}
