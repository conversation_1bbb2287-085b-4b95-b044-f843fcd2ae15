import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/delivery/model/address_delivery_data.dart';
import 'package:sba/src/delivery/model/delivery_address_item.dart';
import 'package:sba/src/delivery/widget/address/address_delivery_wm.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/form_builder_configured_typeahead.dart';
import 'package:sba/src/ui/widget/value_notifier_listener.dart';

const ({
  String apartment,
  String block,
  String city,
  String complex,
  String entry,
  String floor,
  String note,
  String number,
  String street,
})
_keys = (
  city: 'city',
  street: 'street',
  complex: 'complex',
  block: 'block',
  floor: 'floor',
  apartment: 'apartment',
  number: 'number',
  entry: 'entry',
  note: 'note',
);

class AddressDeliveryForm extends StatefulWidget {
  const AddressDeliveryForm({super.key});

  @override
  State<AddressDeliveryForm> createState() => AddressDeliveryFormState();
}

class AddressDeliveryFormState extends State<AddressDeliveryForm> {
  final GlobalKey<FormBuilderState> _formKey = GlobalKey();

  AddressDeliveryData? saveAndValidate() {
    if (_formKey.currentState!.saveAndValidate()) {
      return AddressDeliveryData(
        city: _formKey.currentState!.value[_keys.city] as DeliveryAddressItem,
        street:
            _formKey.currentState!.value[_keys.street] as DeliveryAddressItem,
        complex:
            _formKey.currentState!.value[_keys.complex] as DeliveryAddressItem?,
        block:
            _formKey.currentState!.value[_keys.block] as DeliveryAddressItem?,
        number: _formKey.currentState!.value[_keys.number] as String,
        entrance: _formKey.currentState!.value[_keys.entry] as String?,
        floor: _formKey.currentState!.value[_keys.floor] as String?,
        apartment: _formKey.currentState!.value[_keys.apartment] as String?,
        note: _formKey.currentState!.value[_keys.note] as String?,
      );
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return AddressDeliveryWidget(
      formKey: _formKey,
    );
  }
}

class AddressDeliveryWidget
    extends ElementaryWidget<IAddressDeliveryWidgetModel> {
  AddressDeliveryWidget({
    required this.formKey,
    Key? key,
    WidgetModelFactory wmFactory = defaultAddressDeliveryWidgetModelFactory,
  }) : super(wmFactory, key: key);

  final GlobalKey<FormBuilderState> formKey;

  final ValueNotifier<DeliveryAddressItem?> _selectedCity = ValueNotifier(null);

  @override
  Widget build(IAddressDeliveryWidgetModel wm) {
    return Builder(
      builder: (context) => ValueNotifierListener(
        listenableState: _selectedCity,
        onChange: (value) {
          formKey.currentState?.fields[_keys.street]?.reset();
          formKey.currentState?.fields[_keys.complex]?.reset();
          formKey.currentState?.fields[_keys.block]?.reset();
        },
        child: FormBuilder(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              FormBuilderConfiguredTypeahead<DeliveryAddressItem>(
                name: _keys.city,
                asyncFilter: wm.findCity,
                textTransformer: (p) => p.name,
                decoration: InputDecoration(
                  labelText: context.l10n.form_city,
                  hintText: context.l10n.form_city_hint,
                ),
                validator: FormBuilderValidators.required(
                  errorText: context.l10n.form_validation_required,
                ),
                onChanged: (v) => _selectedCity.value = v,
              ),
              const Gap(UISpacing.l),
              ValueListenableBuilder(
                valueListenable: _selectedCity,
                builder: (context, city, _) =>
                    FormBuilderConfiguredTypeahead<DeliveryAddressItem>(
                      name: _keys.complex,
                      asyncFilter: (query) => wm.findComplex(city!, query),
                      textTransformer: (p) => p.name,
                      enabled: city != null,
                      decoration: InputDecoration(
                        labelText: context.l10n.form_complex,
                        hintText: context.l10n.form_complex_hint,
                      ),
                    ),
              ),
              const Gap(UISpacing.l),
              Row(
                children: [
                  Expanded(
                    child: ValueListenableBuilder(
                      valueListenable: _selectedCity,
                      builder: (context, city, _) =>
                          FormBuilderConfiguredTypeahead<DeliveryAddressItem>(
                            name: _keys.street,
                            asyncFilter: (query) => wm.findStreet(city!, query),
                            textTransformer: (p) => p.name,
                            enabled: city != null,
                            decoration: InputDecoration(
                              labelText: context.l10n.form_street,
                              hintText: context.l10n.form_street_hint,
                            ),
                          ),
                    ),
                  ),
                  const Gap(UISpacing.l),
                  SizedBox(
                    width: 85,
                    child: FormBuilderTextField(
                      name: _keys.number,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.next,
                      decoration: InputDecoration(
                        labelText: context.l10n.form_street_number,
                        hintText: context.l10n.form_street_number_hint,
                      ),
                      validator: FormBuilderValidators.required(
                        errorText: context.l10n.form_validation_required,
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(UISpacing.l),
              ValueListenableBuilder(
                valueListenable: _selectedCity,
                builder: (context, city, _) =>
                    FormBuilderConfiguredTypeahead<DeliveryAddressItem>(
                      name: _keys.block,
                      asyncFilter: (query) => wm.findBlock(city!, query),
                      textTransformer: (p) => p.name,
                      enabled: city != null,
                      decoration: InputDecoration(
                        labelText: context.l10n.form_block,
                        hintText: context.l10n.form_block_hint,
                      ),
                    ),
              ),
              const Gap(UISpacing.l),
              Row(
                children: [
                  Expanded(
                    child: FormBuilderTextField(
                      name: _keys.entry,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.next,
                      decoration: InputDecoration(
                        labelText: context.l10n.form_entry,
                        hintText: context.l10n.form_entry_hint,
                      ),
                    ),
                  ),
                  const Gap(UISpacing.l),
                  Expanded(
                    child: FormBuilderTextField(
                      name: _keys.floor,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.next,
                      decoration: InputDecoration(
                        labelText: context.l10n.form_floor,
                        hintText: context.l10n.form_floor_hint,
                      ),
                    ),
                  ),
                  const Gap(UISpacing.l),
                  Expanded(
                    child: FormBuilderTextField(
                      name: _keys.apartment,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.next,
                      decoration: InputDecoration(
                        labelText: context.l10n.form_apartment,
                        hintText: context.l10n.form_apartment_hint,
                      ),
                    ),
                  ),
                ],
              ),
              const Gap(UISpacing.l),
              FormBuilderTextField(
                name: _keys.note,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.next,
                decoration: InputDecoration(
                  labelText: context.l10n.form_note,
                  hintText: context.l10n.form_note_hint,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
