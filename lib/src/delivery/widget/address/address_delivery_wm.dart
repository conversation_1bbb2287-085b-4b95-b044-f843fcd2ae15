import 'package:elementary/elementary.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/delivery/model/delivery_address_item.dart';
import 'package:sba/src/delivery/widget/address/address_delivery_form.dart';
import 'package:sba/src/delivery/widget/address/address_delivery_model.dart';

abstract interface class IAddressDeliveryWidgetModel implements IWidgetModel {
  Future<List<DeliveryAddressItem>?> findCity(String query);

  Future<List<DeliveryAddressItem>?> findStreet(
    DeliveryAddressItem city,
    String query,
  );

  Future<List<DeliveryAddressItem>?> findComplex(
    DeliveryAddressItem city,
    String query,
  );

  Future<List<DeliveryAddressItem>?> findBlock(
    DeliveryAddressItem city,
    String query,
  );
}

AddressDeliveryWidgetModel defaultAddressDeliveryWidgetModelFactory(
  BuildContext context,
) {
  return AddressDeliveryWidgetModel(
    AddressDeliveryModel(
      repository: get(),
      errorHandler: get(),
    ),
  );
}

class AddressDeliveryWidgetModel
    extends WidgetModel<AddressDeliveryWidget, AddressDeliveryModel>
    implements IAddressDeliveryWidgetModel {
  AddressDeliveryWidgetModel(super.model);

  @override
  Future<List<DeliveryAddressItem>?> findBlock(
    DeliveryAddressItem city,
    String query,
  ) =>
      model.findBlock(city, query);

  @override
  Future<List<DeliveryAddressItem>?> findCity(String query) =>
      model.findCity(query);

  @override
  Future<List<DeliveryAddressItem>?> findComplex(
    DeliveryAddressItem city,
    String query,
  ) =>
      model.findComplex(city, query);

  @override
  Future<List<DeliveryAddressItem>?> findStreet(
    DeliveryAddressItem city,
    String query,
  ) =>
      model.findStreet(city, query);
}
