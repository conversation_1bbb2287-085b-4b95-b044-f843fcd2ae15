import 'package:flutter/material.dart';

abstract class UISpacing {
  static const xs = 4.0;
  static const s = 8.0;
  static const m = 12.0;
  static const l = 16.0;
  static const ll = 18.0;
  static const xl = 24.0;
  static const xxl = 36.0;

  static const defaultScreenPadding =
      EdgeInsets.symmetric(vertical: xxl, horizontal: ll);

  static const defaultElementPaddingWithoutTop =
      EdgeInsets.only(bottom: xxl, right: ll, left: ll);

  static const defaultElementPaddingWithoutBottom =
      EdgeInsets.only(top: xxl, right: ll, left: ll);

  static const defaultElementHorizontalPadding =
      EdgeInsets.symmetric(horizontal: ll);
}
