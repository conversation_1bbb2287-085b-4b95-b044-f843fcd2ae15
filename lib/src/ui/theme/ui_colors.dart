import 'dart:ui';

abstract class UIColors {
  static const primary = Color(0xffEAAB00);
  static const background = Color(0xffFEFAF0);
  static const border = Color(0xffD6D6D6);
  static const heading = Color(0xff000000);
  static const paragraph = Color(0xff4C4C4C);
  static const green = Color(0xff1DB924);
  static const greenBackground = Color(0xffF4FCF4);
  static const red = Color(0xffE12222);
  static const redBackground = Color(0xffFCE9E9);
  static const menuBackground = Color(0xffF7F7F7);
  static const menuForeground = Color(0xff828282);
  static const imageMask = Color(0x99000000);
  static const carouselBackground = Color(0xffD6D6D6);
  static const carouseIndicator = Color(0xff000000);

  static const subscriptionSilver = Color(0xff949699);
  static const subscriptionSilverPlus = Color(0xff0D85AE);
  static const subscriptionGold= Color(0xffCDA32D);
  static const subscriptionPlatinum= Color(0xff7497A7);
}
