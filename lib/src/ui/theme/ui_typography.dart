import 'package:flutter/material.dart';
import 'package:sba/src/generated/fonts.gen.dart';
import 'package:sba/src/ui/theme/ui_font_weight.dart';

abstract class UITypography {
  static const h1 = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 20,
    fontWeight: UIFontWeight.semiBold,
  );

  static const h2 = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 16,
    fontWeight: UIFontWeight.semiBold,
  );

  static const h3 = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 14,
    fontWeight: UIFontWeight.semiBold,
  );

  static const p = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 15,
    fontWeight: UIFontWeight.regular,
  );

  static const pSmall = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 13,
    fontWeight: UIFontWeight.regular,
  );

  static const button = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 14,
    fontWeight: UIFontWeight.semiBold,
  );

  static const inputLabel = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 14,
    fontWeight: UIFontWeight.regular,
  );

  static const menuLabel = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 12,
    fontWeight: UIFontWeight.regular,
  );

  static const selectedMenuLabel = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 12,
    fontWeight: UIFontWeight.semiBold,
  );

  static const caption = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 10,
    fontWeight: UIFontWeight.semiBold,
  );

  static const listTileSubtitle = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 13,
    fontWeight: UIFontWeight.light,
  );

  static const dialogTitle = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 18,
    fontWeight: UIFontWeight.semiBold,
  );

  static const dialogText = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 14,
    fontWeight: UIFontWeight.regular,
  );

  static const tileTrailing = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 14,
    fontWeight: UIFontWeight.bold,
  );

  static const barcodeText = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 17,
    fontWeight: UIFontWeight.regular,
    letterSpacing: 3,
    height: 20,
  );

  static const subscriptionText = TextStyle(
    fontFamily: FontFamily.openSans,
    fontSize: 22,
    fontWeight: UIFontWeight.semiBold,
  );
}
