import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class LoadingDialog extends StatelessWidget {
  const LoadingDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Card(
        color: context.theme.dialogTheme.backgroundColor,
        child: const Padding(
          padding: EdgeInsets.all(UISpacing.xl),
          child: SizedBox(
            width: 48,
            height: 48,
            child: CircularProgressIndicator(
              strokeWidth: 6,
              strokeCap: StrokeCap.round,
            ),
          ),
        ),
      ),
    );
  }
}
