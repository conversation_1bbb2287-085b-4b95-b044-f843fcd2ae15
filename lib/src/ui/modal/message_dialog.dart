import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

typedef ActionBuilder = List<Widget> Function(
  BuildContext context,
  Color color,
);

enum MessageDialogType { success, info, error }

extension MessageBoxTypeExtension on MessageDialogType {
  Color get primaryColor => switch (this) {
        MessageDialogType.success => UIColors.primary,
        MessageDialogType.info => UIColors.primary,
        MessageDialogType.error => UIColors.red,
      };

  Color get buttonColor => switch (this) {
        MessageDialogType.success => UIColors.primary,
        MessageDialogType.info => UIColors.primary,
        MessageDialogType.error => UIColors.red,
      };

  IconData get icon => switch (this) {
        MessageDialogType.success => Icons.check_circle,
        MessageDialogType.info => Icons.info,
        MessageDialogType.error => Icons.error,
      };
}

class MessageDialog extends StatelessWidget {
  const MessageDialog({
    required this.type,
    required this.title,
    required this.text,
    this.centerText = true,
    this.actionBuilder,
    super.key,
  });

  factory MessageDialog.error({
    required BuildContext context,
    required String text,
    VoidCallback? action,
    bool centerText = true,
  }) =>
      MessageDialog(
        type: MessageDialogType.error,
        title: context.l10n.error_title,
        text: text,
        centerText: centerText,
        actionBuilder: (context, color) => [
          FilledButton(
            style: FilledButton.styleFrom(backgroundColor: color),
            onPressed: () async {
              await context.navigator.maybePop();
              action?.call();
            },
            child: Text(context.l10n.action_ok),
          ),
        ],
      );

  factory MessageDialog.info({
    required MessageDialogType type,
    required String title,
    required String text,
    required String actionText,
    VoidCallback? action,
    bool centerText = true,
  }) =>
      MessageDialog(
        type: type,
        title: title,
        text: text,
        centerText: centerText,
        actionBuilder: (context, color) => [
          FilledButton(
            style: FilledButton.styleFrom(backgroundColor: color),
            onPressed: () async {
              await context.navigator.maybePop();
              action?.call();
            },
            child: Text(actionText),
          ),
        ],
      );

  factory MessageDialog.action({
    required MessageDialogType type,
    required String title,
    required String text,
    required String primaryActionText,
    required String secondaryActionText,
    VoidCallback? primaryAction,
    VoidCallback? secondaryAction,
    bool centerText = true,
  }) =>
      MessageDialog(
        type: type,
        title: title,
        text: text,
        centerText: centerText,
        actionBuilder: (context, color) => [
          OutlinedButton(
            style: OutlinedButton.styleFrom(
              foregroundColor: color,
              side: BorderSide(color: color),
            ),
            onPressed: () async {
              await context.navigator.maybePop();
              secondaryAction?.call();
            },
            child: Text(secondaryActionText),
          ),
          FilledButton(
            style: FilledButton.styleFrom(backgroundColor: color),
            onPressed: () async {
              await context.navigator.maybePop();
              primaryAction?.call();
            },
            child: Text(primaryActionText),
          ),
        ],
      );

  final MessageDialogType type;
  final String title;
  final String text;
  final bool centerText;
  final ActionBuilder? actionBuilder;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: UISpacing.l,
          vertical: UISpacing.ll,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(type.icon, color: type.primaryColor, size: 38),
            const Gap(UISpacing.s),
            Text(
              title,
              textAlign: TextAlign.center,
              style: context.theme.dialogTheme.titleTextStyle,
            ),
            const Gap(UISpacing.s),
            Text(
              text,
              textAlign: centerText ? TextAlign.center : TextAlign.start,
              style: context.theme.dialogTheme.contentTextStyle,
            ),
            if (actionBuilder != null) const Gap(UISpacing.ll),
            if (actionBuilder != null)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: actionBuilder!(
                  context,
                  type.buttonColor,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
