import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:skeletonizer/skeletonizer.dart';

class NavigationBox extends StatelessWidget {
  const NavigationBox({
    required this.icon,
    required this.text,
    super.key,
    this.onTap,
    this.enabled = true,
    this.loading = false,
  });

  final Widget icon;
  final String text;
  final VoidCallback? onTap;
  final bool enabled;
  final bool loading;

  @override
  Widget build(BuildContext context) {
    final active = onTap != null && enabled;
    final effectiveIconColor = active ? UIColors.primary : UIColors.border;
    final effectiveTextColor = active ? UIColors.paragraph : UIColors.border;

    return Skeletonizer(
      enabled: loading,
      enableSwitchAnimation: true,
      child: InkWell(
        onTap: active ? onTap : null,
        child: Container(
          padding: const EdgeInsets.all(UISpacing.s),
          decoration: BoxDecoration(border: Border.all(color: UIColors.border)),
          alignment: Alignment.center,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Skeleton.replace(
                width: 35,
                height: 35,
                child: ColorFiltered(
                  colorFilter: ColorFilter.mode(
                    effectiveIconColor,
                    BlendMode.srcIn,
                  ),
                  child: icon,
                ),
              ),
              const Gap(UISpacing.s),
              Text(
                text,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style:
                    UITypography.menuLabel.copyWith(color: effectiveTextColor),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
