import 'package:flutter/material.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/ui/theme/theme.dart';

class PaymentTileBox extends StatelessWidget {
  const PaymentTileBox._({
    required this.selected,
    required this.children,
  });

  factory PaymentTileBox.card({required bool selected}) => PaymentTileBox._(
        selected: selected,
        children: [
          Assets.images.visa.image(),
          Assets.images.mastercard.image(),
        ],
      );

  final bool selected;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: UISpacing.m,
        vertical: UISpacing.s,
      ),
      shape: Border.all(color: UIColors.border),
      leading: IgnorePointer(
        child: Radio(
          value: true,
          groupValue: selected,
          onChanged: (_) {},
        ),
      ),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        spacing: UISpacing.m,
        children: children,
      ),
    );
  }
}
