import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class UriImage extends StatelessWidget {
  const UriImage({
    required this.placeholder,
    super.key,
    this.imageUri,
    this.fit = BoxFit.contain,
    this.size,
    this.color,
    this.blendMode,
  });

  final String? imageUri;
  final String placeholder;
  final BoxFit fit;
  final Size? size;
  final Color? color;
  final BlendMode? blendMode;

  @override
  Widget build(BuildContext context) {
    final placeholderWidget = Image.asset(
      placeholder,
      fit: fit,
      width: size?.width,
      height: size?.height,
      color: color,
      colorBlendMode: blendMode,
    );

    if (imageUri?.isEmpty ?? true) {
      return placeholderWidget;
    }

    return CachedNetworkImage(
      imageUrl: imageUri ?? '',
      useOldImageOnUrlChange: true,
      placeholder: (_, _) => placeholderWidget,
      errorWidget: (_, _, _) => placeholderWidget,
      fit: fit,
      width: size?.width,
      height: size?.height,
      color: color,
      colorBlendMode: blendMode,
    );
  }
}
