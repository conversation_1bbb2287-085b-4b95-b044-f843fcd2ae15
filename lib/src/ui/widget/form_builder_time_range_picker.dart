import 'dart:core';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:time_range_picker/time_range_picker.dart';

/// Field for selecting a range of dates
class FormBuilderTimeRangePicker extends FormBuilderFieldDecoration<TimeRange> {
  /// Creates field for selecting a range of dates
  FormBuilderTimeRangePicker({
    required super.name,
    super.key,
    super.validator,
    super.initialValue,
    super.decoration,
    super.onChanged,
    super.valueTransformer,
    super.enabled,
    super.onSaved,
    super.autovalidateMode = AutovalidateMode.disabled,
    super.onReset,
    super.focusNode,
    super.restorationId,
    this.start,
    this.end,
    this.maxLines = 1,
    this.obscureText = false,
    this.textCapitalization = TextCapitalization.none,
    this.scrollPadding = const EdgeInsets.all(20),
    this.enableInteractiveSelection = true,
    this.maxLengthEnforcement,
    this.textAlign = TextAlign.start,
    this.textAlignVertical,
    this.autofocus = false,
    this.autocorrect = true,
    this.cursorWidth = 2.0,
    this.keyboardType,
    this.style,
    this.controller,
    this.textInputAction,
    this.strutStyle,
    this.textDirection,
    this.maxLength,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.inputFormatters,
    this.cursorRadius,
    this.cursorColor,
    this.keyboardAppearance,
    this.buildCounter,
    this.mouseCursor,
    this.expands = false,
    this.minLines,
    this.showCursor = false,
    this.allowClear = false,
    this.clearIcon,
  }) : super(
          builder: (FormFieldState<TimeRange?> field) {
            final state = field as _FormBuilderTimeRangePickerState;

            return TextField(
              enabled: state.enabled,
              style: style,
              focusNode: state.effectiveFocusNode,
              decoration: state.decoration,
              // initialValue: "${_initialValue ?? ''}",
              maxLines: maxLines,
              keyboardType: keyboardType,
              obscureText: obscureText,
              onEditingComplete: onEditingComplete,
              controller: state._effectiveController,
              autocorrect: autocorrect,
              autofocus: autofocus,
              buildCounter: buildCounter,
              mouseCursor: mouseCursor,
              cursorColor: cursorColor,
              cursorRadius: cursorRadius,
              cursorWidth: cursorWidth,
              enableInteractiveSelection: enableInteractiveSelection,
              maxLength: maxLength,
              inputFormatters: inputFormatters,
              keyboardAppearance: keyboardAppearance,
              maxLengthEnforcement: maxLengthEnforcement,
              scrollPadding: scrollPadding,
              textAlign: textAlign,
              textCapitalization: textCapitalization,
              textDirection: textDirection,
              textInputAction: textInputAction,
              textAlignVertical: textAlignVertical,
              strutStyle: strutStyle,
              readOnly: true,
              expands: expands,
              minLines: minLines,
              showCursor: showCursor,
            );
          },
        );
  final int maxLines;
  final TextInputType? keyboardType;
  final bool obscureText;
  final TextStyle? style;
  final TextEditingController? controller;
  final TextCapitalization textCapitalization;
  final TextInputAction? textInputAction;
  final StrutStyle? strutStyle;
  final TextDirection? textDirection;
  final TextAlign textAlign;
  final TextAlignVertical? textAlignVertical;
  final bool autofocus;
  final bool autocorrect;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? maxLength;
  final VoidCallback? onEditingComplete;
  final ValueChanged<DateTimeRange?>? onFieldSubmitted;
  final List<TextInputFormatter>? inputFormatters;
  final double cursorWidth;
  final Radius? cursorRadius;
  final Color? cursorColor;
  final Brightness? keyboardAppearance;
  final EdgeInsets scrollPadding;
  final bool enableInteractiveSelection;
  final InputCounterWidgetBuilder? buildCounter;
  final MouseCursor? mouseCursor;
  final bool expands;
  final int? minLines;
  final bool showCursor;
  final TimeOfDay? start;
  final TimeOfDay? end;
  final bool allowClear;
  final Widget? clearIcon;

  @override
  FormBuilderFieldDecorationState<FormBuilderTimeRangePicker, TimeRange>
      createState() => _FormBuilderTimeRangePickerState();
}

class _FormBuilderTimeRangePickerState extends FormBuilderFieldDecorationState<
    FormBuilderTimeRangePicker, TimeRange> {
  late TextEditingController _effectiveController;

  @override
  void initState() {
    super.initState();
    _effectiveController =
        widget.controller ?? TextEditingController(text: _valueToText());
    effectiveFocusNode.addListener(_handleFocus);
  }

  @override
  void dispose() {
    effectiveFocusNode.removeListener(_handleFocus);
    // Dispose the _effectiveController when initState created it
    if (null == widget.controller) {
      _effectiveController.dispose();
    }
    super.dispose();
  }

  Future<void> _handleFocus() async {
    if (effectiveFocusNode.hasFocus && enabled) {
      effectiveFocusNode.unfocus();

      final picked = await showTimeRangePicker(
        context: context,
        start: widget.start,
        end: widget.end,
        fromText: 'От',
        toText: 'До',
        ticks: 12,
        backgroundColor: UIColors.border,
        interval: const Duration(minutes: 30),
        handlerColor: UIColors.primary,
        selectedColor: UIColors.primary,
        labels: [
          '24',
          '3',
          '6',
          '9',
          '12',
          '15',
          '18',
          '21',
        ].asMap().entries.map((e) {
          return ClockLabel.fromIndex(idx: e.key, length: 8, text: e.value);
        }).toList(),
        clockRotation: 180,
      ) as TimeRange?;

      if (picked != null) {
        didChange(picked);
      }
    }
  }

  String _valueToText() {
    if (value == null) {
      return '';
    }

    return '''
${value!.startTime.format(context)} - ${value!.endTime.format(context)}
''';
  }

  void _setTextFieldString() {
    setState(() => _effectiveController.text = _valueToText());
  }

  @override
  void didChange(TimeRange? value) {
    super.didChange(value);
    _setTextFieldString();
  }

  @override
  void reset() {
    super.reset();
    _setTextFieldString();
  }

  @override
  InputDecoration get decoration => widget.allowClear
      ? super.decoration.copyWith(
            suffix: IconButton(
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(maxWidth: 24, maxHeight: 24),
              onPressed: () {
                focus();
                didChange(null);
                effectiveFocusNode.unfocus();
              },
              icon: widget.clearIcon ?? const Icon(Icons.clear),
            ),
          )
      : super.decoration;
}
