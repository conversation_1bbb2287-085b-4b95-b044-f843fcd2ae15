import 'package:flutter/material.dart';
import 'package:form_builder_extra_fields/form_builder_extra_fields.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';

typedef FilterCallback<T> = bool Function(T data, String suggestion);
typedef TextTransformer<T> = String Function(T data);
typedef AsyncFilterCallback<T> = Future<List<T>?> Function(String suggestion);

class FormBuilderConfiguredTypeahead<T> extends StatelessWidget {
  const FormBuilderConfiguredTypeahead({
    required this.name,
    required this.textTransformer,
    this.filter,
    super.key,
    this.hintTransformer,
    this.initialValue,
    this.data,
    this.asyncFilter,
    this.decoration = const InputDecoration(),
    this.validator,
    this.onChanged,
    this.enabled = true,
  }) : assert(
          filter != null || asyncFilter != null,
          'Either filter or asyncFilter must be provided.',
        );

  final String name;
  final T? initialValue;
  final List<T>? data;
  final FilterCallback<T>? filter;

  final AsyncFilterCallback<T>? asyncFilter;
  final TextTransformer<T> textTransformer;
  final TextTransformer<T>? hintTransformer;
  final InputDecoration decoration;
  final FormFieldValidator<T>? validator;
  final ValueChanged<T?>? onChanged;

  final bool enabled;

  @override
  Widget build(BuildContext context) {
    return FormBuilderTypeAhead<T>(
      name: name,
      initialValue: initialValue,
      suggestionsCallback: (suggestion) => suggestion.isNullOrEmpty
          ? []
          : asyncFilter?.call(suggestion) ??
              data?.where((e) => filter?.call(e, suggestion) ?? false).toList(),
      itemBuilder: (context, place) => Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: UISpacing.l,
          vertical: UISpacing.s,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(textTransformer(place)),
            if (hintTransformer != null)
              Text(
                hintTransformer!(place),
                style: UITypography.menuLabel.copyWith(
                  color: UIColors.menuForeground,
                ),
              ),
          ],
        ),
      ),
      emptyBuilder: (context) => Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: UISpacing.l,
          vertical: UISpacing.s,
        ),
        child: Text(context.l10n.empty_search),
      ),
      selectionToTextTransformer: textTransformer,
      decoration: decoration,
      enabled: enabled,
      validator: validator,
      onChanged: onChanged,
    );
  }
}
