import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class LabelWithText extends StatelessWidget {
  const LabelWithText({
    required this.label,
    required this.text,
    super.key,
    this.maxLines = 1,
    this.textColor,
    this.defaultColor,
    this.defaultStyle,
  });

  final String label;
  final String text;

  final int maxLines;
  final Color? defaultColor;
  final Color? textColor;
  final TextStyle? defaultStyle;

  @override
  Widget build(BuildContext context) {
    return RichText(
      maxLines: maxLines,
      overflow: TextOverflow.ellipsis,
      text: TextSpan(
        text: '$label ',
        style: (defaultStyle ?? context.textTheme.bodySmall)?.copyWith(
          color: defaultColor,
        ),
        children: [
          TextSpan(
            text: text,
            style: TextStyle(
              fontWeight: UIFontWeight.semiBold,
              color: textColor ?? defaultColor,
            ),
          ),
        ],
      ),
    );
  }
}
