import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:skeletonizer/skeletonizer.dart';

typedef ItemBuilder<T> = Widget Function(BuildContext context, T data);

class SmartWrapView<T> extends StatelessWidget {
  const SmartWrapView({
    required this.loading,
    required this.builder,
    this.emptyText,
    super.key,
    this.data,
    this.skeletonData,
    this.padding = EdgeInsets.zero,
    this.spacing = UISpacing.s,
    this.runSpacing = UISpacing.s,
    this.alignment = WrapAlignment.start,
    this.crossAlignment = WrapCrossAlignment.start,
  });

  final List<T>? data;
  final List<T>? skeletonData;
  final bool loading;
  final ItemBuilder<T> builder;
  final double spacing;
  final double runSpacing;
  final WrapAlignment alignment;
  final WrapCrossAlignment crossAlignment;
  final String? emptyText;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    final odl = data?.length ?? 0;
    final isEmpty = !loading && odl <= 0;
    final currData = (loading ? skeletonData : data) ?? [];

    if (isEmpty) {
      return Padding(
        padding: padding,
        child: Text(
          emptyText ?? '',
          style: context.textTheme.bodyMedium,
        ),
      );
    }

    return Skeletonizer(
      enabled: loading,
      enableSwitchAnimation: true,
      child: Padding(
        padding: padding,
        child: Wrap(
          spacing: spacing,
          runSpacing: runSpacing,
          alignment: alignment,
          crossAxisAlignment: crossAlignment,
          children: [for (final element in currData) builder(context, element)],
        ),
      ),
    );
  }
}
