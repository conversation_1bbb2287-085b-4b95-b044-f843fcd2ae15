import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_map_animations/flutter_map_animations.dart';
import 'package:flutter_map_location_marker/flutter_map_location_marker.dart';
import 'package:flutter_map_marker_popup/flutter_map_marker_popup.dart';
import 'package:latlong2/latlong.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/ui/theme/ui_colors.dart';

typedef MarkerCallback<T> = void Function(MarkerData<T> data);
typedef MarkerPopupBuilder<T> =
    Widget Function(
      BuildContext context,
      MarkerData<T> data,
    );

//bulgaria camera fit
final _defaultCameraFit = CameraFit.bounds(
  bounds: LatLngBounds(
    const LatLng(41.2344859889, 22.3805257504),
    const LatLng(44.2349230007, 28.5580814959),
  ),
  padding: const EdgeInsets.all(8),
);

const int _interactionFlags = InteractiveFlag.drag | InteractiveFlag.pinchZoom;

final class MarkerData<T> {
  MarkerData({required this.coordinates, required this.data, this.icon});

  final LatLng coordinates;
  final Widget? icon;
  final T data;
}

class OpenStreetMap<T> extends StatefulWidget {
  const OpenStreetMap({
    super.key,
    this.markers,
    this.onMarkerTap,
    this.popupBuilder,
    this.constraints = const BoxConstraints(maxHeight: 224),
    this.padding = EdgeInsets.zero,
    this.showCurrentLocation = false,
    this.animateMapBoundsToFitMarkers = false,
    this.enableInteractions = true,
    this.onCurrentLocationUpdate,
  });

  final List<MarkerData<T>>? markers;
  final MarkerCallback<T>? onMarkerTap;
  final MarkerPopupBuilder<T>? popupBuilder;
  final BoxConstraints constraints;
  final EdgeInsets padding;
  final bool showCurrentLocation;
  final bool animateMapBoundsToFitMarkers;
  final bool enableInteractions;
  final ValueChanged<LatLng>? onCurrentLocationUpdate;

  @override
  State<OpenStreetMap<T>> createState() => _OpenStreetMapState<T>();
}

class _OpenStreetMapState<T> extends State<OpenStreetMap<T>>
    with TickerProviderStateMixin {
  final PopupController _popupLayerController = PopupController();
  final CompositeSubscription _subscription = CompositeSubscription();
  late final _controller = AnimatedMapController(
    vsync: this,
    cancelPreviousAnimations: true,
  );
  late final Stream<LocationMarkerPosition?>? _positionStream;

  bool isMapReady = false;

  @override
  void dispose() {
    _popupLayerController.dispose();
    _subscription.clear();
    super.dispose();
  }

  @override
  void initState() {
    if (widget.showCurrentLocation) {
      _positionStream = const LocationMarkerDataStreamFactory()
          .fromGeolocatorPositionStream();
      _subscription.add(
        _positionStream!
            .where((e) => e != null)
            .map((e) => e!)
            .map((e) => LatLng(e.latitude, e.longitude))
            .doOnData((data) => widget.onCurrentLocationUpdate?.call(data))
            .publish()
            .connect(),
      );
    }
    super.initState();
  }

  @override
  void didUpdateWidget(covariant OpenStreetMap<T> oldWidget) {
    if (oldWidget.markers != widget.markers) {
      _animateCameraBoundsToFitMarkers();
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding,
      child: ConstrainedBox(
        constraints: widget.constraints,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: FlutterMap(
            mapController: _controller.mapController,
            options: MapOptions(
              interactionOptions: InteractionOptions(
                flags: widget.enableInteractions
                    ? _interactionFlags
                    : InteractiveFlag.none,
              ),
              initialCameraFit: _defaultCameraFit,
              onTap: (_, _) => _popupLayerController.hideAllPopups(),
              onMapReady: () {
                isMapReady = true;
                _animateCameraBoundsToFitMarkers();
              },
            ),
            children: [
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.sba.test.app',
                tileProvider: NetworkTileProvider(),
              ),
              if (widget.onMarkerTap != null && widget.popupBuilder == null)
                MarkerLayer(
                  markers:
                      widget.markers
                          ?.map(
                            (data) => Marker(
                              point: data.coordinates,
                              child: GestureDetector(
                                onTap: () => widget.onMarkerTap?.call(data),
                                child: data.icon ?? _MarkerIcon(),
                              ),
                            ),
                          )
                          .toList(growable: false) ??
                      List.empty(),
                ),
              if (widget.popupBuilder != null)
                PopupMarkerLayer(
                  options: PopupMarkerLayerOptions(
                    markers:
                        widget.markers
                            ?.map(
                              (data) => Marker(
                                key: ValueKey(data),
                                point: data.coordinates,
                                child: data.icon ?? _MarkerIcon(),
                              ),
                            )
                            .toList() ??
                        [],
                    popupDisplayOptions: PopupDisplayOptions(
                      builder: (context, marker) => widget.popupBuilder!.call(
                        context,
                        (marker.key! as ValueKey<MarkerData<T>>).value,
                      ),
                      animation: const PopupAnimation.fade(
                        duration: Duration(milliseconds: 100),
                      ),
                    ),
                    onPopupEvent: (event, selectedMarkers) {
                      final data =
                          selectedMarkers.firstOrNull?.key
                              as ValueKey<MarkerData<T>>?;
                      if (data != null) widget.onMarkerTap?.call(data.value);
                    },
                  ),
                ),
              if (widget.showCurrentLocation)
                CurrentLocationLayer(
                  alignPositionOnUpdate: widget.markers?.isEmpty ?? true
                      ? AlignOnUpdate.always
                      : AlignOnUpdate.never,
                  positionStream: _positionStream,
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _animateCameraBoundsToFitMarkers() {
    if (!widget.animateMapBoundsToFitMarkers ||
        widget.showCurrentLocation ||
        !isMapReady) {
      return;
    }

    if (widget.markers?.isEmpty ?? true) {
      _controller.animatedFitCamera(cameraFit: _defaultCameraFit);
      return;
    }

    if (widget.markers!.length == 1) {
      _controller.animateTo(dest: widget.markers!.first.coordinates, zoom: 17);
      return;
    }

    final newBounds = LatLngBounds.fromPoints(
      widget.markers!.map((e) => e.coordinates).toList(growable: false),
    );

    final distVertical =
        max(newBounds.north, newBounds.south) -
        min(newBounds.north, newBounds.south);

    final distHorizontal =
        max(newBounds.east, newBounds.west) -
        min(newBounds.east, newBounds.west);

    var padding = 30 - (distHorizontal + distVertical);

    padding = padding < 0 ? 4 : padding;

    _controller.animatedFitCamera(
      cameraFit: CameraFit.bounds(
        bounds: newBounds,
        padding: EdgeInsets.all(padding),
      ),
    );
  }
}

class _MarkerIcon extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Icon(
      Icons.location_pin,
      color: UIColors.red,
    );
  }
}
