import 'package:flutter/material.dart';

class MaintainState extends StatefulWidget {
  const MaintainState({
    required this.child,
    super.key,
  });

  final Widget child;

  @override
  State<MaintainState> createState() => _MaintainStateState();
}

class _MaintainStateState extends State<MaintainState>
    with AutomaticKeepAliveClientMixin<MaintainState> {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
