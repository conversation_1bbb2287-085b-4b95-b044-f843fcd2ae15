import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class CustomExpansionTile extends StatelessWidget {
  const CustomExpansionTile({
    required this.title,
    required this.text,
    super.key,
  });

  final String title;
  final String text;

  @override
  Widget build(BuildContext context) {
    return ExpansionTile(
      title: Text(
        title,
        style: context.textTheme.headlineSmall,
      ),
      children: [
        Text(
          text,
          style: context.textTheme.bodyMedium,
        ),
      ],
    );
  }
}
