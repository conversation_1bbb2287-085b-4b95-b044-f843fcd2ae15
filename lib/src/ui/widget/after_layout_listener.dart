import 'package:after_layout/after_layout.dart';
import 'package:flutter/material.dart';

class AfterLayoutListener extends StatefulWidget {
  const AfterLayoutListener({
    required this.afterLayout,
    required this.child,
    super.key,
  });

  final ValueChanged<BuildContext> afterLayout;
  final Widget child;

  @override
  State<AfterLayoutListener> createState() => _AfterLayoutListenerState();
}

class _AfterLayoutListenerState extends State<AfterLayoutListener>
    with AfterLayoutMixin<AfterLayoutListener> {
  @override
  void afterFirstLayout(BuildContext context) {
    widget.afterLayout(context);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
