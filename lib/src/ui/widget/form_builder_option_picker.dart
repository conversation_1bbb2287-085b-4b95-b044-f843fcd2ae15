import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/theme.dart';

typedef ElementBuilder<T> = Widget Function(T data, bool selected);

class FormBuilderOptionPicker<T> extends FormBuilderFieldDecoration<T> {
  FormBuilderOptionPicker({
    required super.name,
    required this.data,
    required this.elementBuilder,
    super.autovalidateMode = AutovalidateMode.disabled,
    super.enabled,
    super.focusNode,
    super.onSaved,
    super.validator,
    super.decoration = const InputDecoration(
      border: InputBorder.none,
      focusedBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      enabledBorder: InputBorder.none,
    ),
    super.key,
    this.label,
    super.initialValue,
    super.restorationId,
    super.onChanged,
    super.valueTransformer,
    super.onReset,
    this.alignment = WrapAlignment.spaceBetween,
    this.crossAxisAlignment = WrapCrossAlignment.start,
    this.direction = Axis.horizontal,
    this.runAlignment = WrapAlignment.spaceBetween,
    this.runSpacing = 6.0,
    this.spacing = 6.0,
  }) : super(
          builder: (FormFieldState<T?> field) {
            final state = field as _FormBuilderOptionPickerState<T>;

            return InputDecorator(
              decoration: state.decoration,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (label != null)
                    Text(
                      label,
                      style: state.decoration.hintStyle,
                    ),
                  const Gap(UISpacing.s),
                  Wrap(
                    direction: direction,
                    alignment: alignment,
                    crossAxisAlignment: crossAxisAlignment,
                    runAlignment: runAlignment,
                    runSpacing: runSpacing,
                    spacing: spacing,
                    children: <Widget>[
                      for (final element in data)
                        InkWell(
                          onTap: () {
                            final choice =
                                field.value == element ? null : element;
                            state.didChange(choice);
                          },
                          child:
                              elementBuilder(element, field.value == element),
                        ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
  final Axis direction;
  final WrapAlignment alignment;
  final double spacing;
  final WrapAlignment runAlignment;
  final double runSpacing;
  final WrapCrossAlignment crossAxisAlignment;
  final List<T> data;
  final ElementBuilder<T> elementBuilder;
  final String? label;

  @override
  FormBuilderFieldDecorationState<FormBuilderOptionPicker<T>, T>
      createState() => _FormBuilderOptionPickerState<T>();
}

class _FormBuilderOptionPickerState<T>
    extends FormBuilderFieldDecorationState<FormBuilderOptionPicker<T>, T> {}
