import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

// Listener that uses [ListenableState] as a source of data.
/// Usually can be helpful with the [StateNotifier].
class ValueListener<T> extends StatefulWidget {
  /// Creates an instance of [StateNotifierBuilder].
  const ValueListener({
    required this.value,
    required this.child,
    required this.onChange,
    super.key,
  });

  /// Source that used to detect change and rebuild.
  final T value;
  final ValueChanged<T?> onChange;
  final Widget child;

  @override
  State<ValueListener<T>> createState() => _ValueListenerState<T>();
}

class _ValueListenerState<T> extends State<ValueListener<T>> {
  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      widget.onChange(widget.value);
    });
  }

  @override
  void didUpdateWidget(ValueListener<T> oldWidget) {
    if (oldWidget.value != widget.value) {
      widget.onChange(widget.value);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
