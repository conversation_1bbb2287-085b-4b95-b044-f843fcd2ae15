import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:url_launcher/url_launcher_string.dart';

class HtmlView extends StatelessWidget {
  const HtmlView({
    required this.html,
    super.key,
    this.mode = RenderMode.column,
  });

  final String html;
  final RenderMode mode;

  @override
  Widget build(BuildContext context) {
    return HtmlWidget(
      html,
      renderMode: mode,
      onTapUrl: launchUrlString,
      textStyle: context.textTheme.bodySmall?.copyWith(
        color: UIColors.paragraph,
      ),
    );
  }
}
