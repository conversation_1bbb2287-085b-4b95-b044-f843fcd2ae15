import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme.dart';

class CircleButton extends StatelessWidget {
  const CircleButton({required this.icon, super.key, this.onTap, this.color});

  final Widget icon;
  final VoidCallback? onTap;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return FilledButton(
      onPressed: onTap, // icon of the button
      style: FilledButton.styleFrom(
        // styling the button
        shape: const CircleBorder(),
        minimumSize: Size.zero,
        backgroundColor: color,
        padding: const EdgeInsets.all(UISpacing.s),
      ),
      child: icon,
    );
  }
}
