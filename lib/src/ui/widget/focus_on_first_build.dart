import 'dart:async';

import 'package:after_layout/after_layout.dart';
import 'package:flutter/cupertino.dart';

class FocusOnFirstBuild extends StatefulWidget {
  const FocusOnFirstBuild({
    required this.child,
    super.key,
    this.duration = const Duration(milliseconds: 300),
  });

  final Widget child;

  final Duration duration;

  @override
  State<FocusOnFirstBuild> createState() => _FocusOnFirstBuildState();
}

class _FocusOnFirstBuildState extends State<FocusOnFirstBuild> with AfterLayoutMixin {
  @override
  FutureOr<void> afterFirstLayout(BuildContext context) {
    Scrollable.ensureVisible(context, duration: widget.duration);
  }

  @override
  void didUpdateWidget(covariant FocusOnFirstBuild oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.child != widget.child) {
      Scrollable.ensureVisible(context, duration: widget.duration);
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
