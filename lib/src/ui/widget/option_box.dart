import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/theme.dart';

class OptionBox extends StatelessWidget {
  const OptionBox({
    required this.icon,
    required this.text,
    super.key,
    this.selected = false,
  });

  final Widget icon;
  final String text;
  final bool selected;

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor =
        selected ? UIColors.background : Colors.white;
    final effectiveBorderColor = selected ? UIColors.primary : UIColors.border;

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: Container(
        key: ValueKey(selected),
        padding: const EdgeInsets.all(UISpacing.s),
        decoration: BoxDecoration(
          color: effectiveBackgroundColor,
          border: Border.all(color: effectiveBorderColor),
        ),
        alignment: Alignment.center,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 35,
              height: 35,
              child: ColorFiltered(
                colorFilter: const ColorFilter.mode(
                  UIColors.primary,
                  BlendMode.srcIn,
                ),
                child: icon,
              ),
            ),
            const Gap(UISpacing.s),
            Text(
              text,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: UITypography.menuLabel.copyWith(color: UIColors.paragraph),
            ),
          ],
        ),
      ),
    );
  }
}
