import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

typedef ActionBuilder = List<Widget> Function(
  BuildContext context,
  Color color,
);

enum MessageBoxType { success, info, error }

extension MessageBoxTypeExtension on MessageBoxType {
  Color get primaryColor => switch (this) {
        MessageBoxType.success => UIColors.green,
        MessageBoxType.info => UIColors.primary,
        MessageBoxType.error => UIColors.red,
      };

  Color get backgroundColor => switch (this) {
        MessageBoxType.success => UIColors.greenBackground,
        MessageBoxType.info => UIColors.background,
        MessageBoxType.error => UIColors.redBackground,
      };

  IconData get icon => switch (this) {
        MessageBoxType.success => Icons.check_circle,
        MessageBoxType.info => Icons.info,
        MessageBoxType.error => Icons.error,
      };
}

class MessageBox extends StatelessWidget {
  const MessageBox({
    required this.title,
    required this.type,
    super.key,
    this.text,
    this.textWidget,
    this.caption,
    this.actionBuilder,
    this.hasIcon = true,
  });

  final String title;
  final String? text;

  final Widget? textWidget;
  final String? caption;
  final bool hasIcon;
  final MessageBoxType type;
  final ActionBuilder? actionBuilder;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: type.backgroundColor,
        border: Border.all(color: type.primaryColor),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: UISpacing.l,
        vertical: UISpacing.m,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (hasIcon)
            Transform.translate(
              offset: const Offset(0, 2),
              child: Icon(type.icon, color: type.primaryColor, size: 24),
            ),
          if (hasIcon) const Gap(UISpacing.s),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  title,
                  style: context.textTheme.headlineMedium
                      ?.copyWith(color: type.primaryColor),
                ),
                if (text != null) const Gap(UISpacing.s),
                if (text != null)
                  Text(
                    text!,
                    style: context.textTheme.bodyMedium,
                  ),
                if (textWidget != null) const Gap(UISpacing.s),
                if (textWidget != null) textWidget!,
                if (caption != null) const Gap(UISpacing.s),
                if (caption != null)
                  Text(
                    caption!,
                    style: UITypography.caption,
                  ),
                if (actionBuilder != null) const Gap(UISpacing.ll),
                if (actionBuilder != null)
                  Row(
                    spacing: UISpacing.xs,
                    children: actionBuilder!(
                      context,
                      type.primaryColor,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
