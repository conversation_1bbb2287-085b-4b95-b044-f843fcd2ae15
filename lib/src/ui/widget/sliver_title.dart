import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class SliverTitle extends StatelessWidget {
  const SliverTitle({
    required this.text,
    super.key,
    this.action,
    this.padding = EdgeInsets.zero,
  });

  final String text;
  final Widget? action;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: padding,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                text,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.headlineLarge,
              ),
            ),
            const Gap(UISpacing.xl),
            AnimatedCrossFade(
              firstChild: const SizedBox.shrink(),
              secondChild: action ?? const SizedBox.shrink(),
              alignment: Alignment.center,
              crossFadeState: action == null
                  ? CrossFadeState.showFirst
                  : CrossFadeState.showSecond,
              duration: const Duration(milliseconds: 300),
            ),
          ],
        ),
      ),
    );
  }
}
