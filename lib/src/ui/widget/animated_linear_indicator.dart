import 'package:flutter/material.dart';

class AnimatedLinearIndicator extends StatelessWidget {
  const AnimatedLinearIndicator({required this.progress, super.key});

  final double progress;

  @override
  Widget build(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 250),
      curve: Curves.easeInOut,
      tween: Tween<double>(
        begin: 0,
        end: progress,
      ),
      builder: (context, value, _) => LinearProgressIndicator(
        value: value,
        borderRadius: BorderRadius.circular(6),
      ),
    );
  }
}
