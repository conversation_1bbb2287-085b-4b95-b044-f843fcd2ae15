import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:skeletonizer/skeletonizer.dart';

typedef ItemBuilder<T> = Widget Function(BuildContext context, T data);

class SmartPageView<T> extends StatefulWidget {
  const SmartPageView({
    required this.loading,
    required this.builder,
    super.key,
    this.data,
    this.skeletonData,
    this.padding = EdgeInsets.zero,
    this.onSelectionChange,
  }) : sliver = false;

  const SmartPageView.sliver({
    required this.loading,
    required this.builder,
    super.key,
    this.data,
    this.skeletonData,
    this.padding = EdgeInsets.zero,
    this.onSelectionChange,
  }) : sliver = true;

  final List<T>? data;
  final List<T>? skeletonData;
  final bool loading;
  final ItemBuilder<T> builder;
  final ValueChanged<T>? onSelectionChange;
  final bool sliver;
  final EdgeInsets padding;

  @override
  State<SmartPageView<T>> createState() => _SmartPageViewState<T>();
}

class _SmartPageViewState<T> extends State<SmartPageView<T>> {
  late PageController _controller;
  int _page = 0;

  double _pageDelta = 0;

  @override
  void initState() {
    _controller = PageController(
      viewportFraction: 0.9,
    );

    _controller.addListener(_onControllerUpdate);
    super.initState();
  }

  void _onControllerUpdate() {
    setState(() {
      final index = _controller.page ?? 0.0;
      _page = index.floor();
      _pageDelta = index - _page;
    });
  }

  @override
  void dispose() {
    _controller
      ..removeListener(_onControllerUpdate)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final carousel = !widget.loading && (widget.data?.isEmpty ?? true)
        ? const SizedBox.shrink()
        : Padding(
            padding: widget.padding,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Skeletonizer(
                  enabled: widget.loading,
                  enableSwitchAnimation: true,
                  child: ExpandablePageView.builder(
                    controller: _controller,
                    itemCount: (widget.loading
                            ? widget.skeletonData?.length
                            : widget.data?.length) ??
                        0,
                    itemBuilder: (context, i) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: widget.builder(
                        context,
                        widget.loading
                            ? widget.skeletonData![i]
                            : widget.data![i],
                      ),
                    ),
                  ),
                ),
                if (!widget.loading) const Gap(UISpacing.s),
                if (!widget.loading)
                  CircularSlideIndicator(
                    slideIndicatorOptions: const SlideIndicatorOptions(
                      indicatorRadius: 4,
                      itemSpacing: 12,
                      currentIndicatorColor: UIColors.carouseIndicator,
                      indicatorBackgroundColor: UIColors.carouselBackground,
                    ),
                  ).build(
                    _page,
                    _pageDelta,
                    widget.data?.length ?? 0,
                  ),
              ],
            ),
          );

    return widget.sliver
        ? SliverToBoxAdapter(
            child: carousel,
          )
        : carousel;
  }
}
