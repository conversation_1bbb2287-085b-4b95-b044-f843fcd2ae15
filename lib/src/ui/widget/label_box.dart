import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

enum LabelBoxType { success, info, error }

extension LabelBoxTypeExtension on LabelBoxType {
  Color get primaryColor => switch (this) {
        LabelBoxType.success => UIColors.green,
        LabelBoxType.info => UIColors.primary,
        LabelBoxType.error => UIColors.red,
      };

  Color get backgroundColor => switch (this) {
        LabelBoxType.success => UIColors.greenBackground,
        LabelBoxType.info => UIColors.background,
        LabelBoxType.error => UIColors.redBackground,
      };
}

class LabelBox extends StatelessWidget {
  const LabelBox({
    required this.label,
    required this.type,
    super.key,
  });

  final String label;
  final LabelBoxType type;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: type.backgroundColor,
        border: Border.all(color: type.primaryColor),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: UISpacing.m,
        vertical: UISpacing.s,
      ),
      child: Text(
        label,
        style: context.textTheme.bodyMedium?.copyWith(color: type.primaryColor),
      ),
    );
  }
}
