import 'package:flutter/material.dart';
import 'package:flutter_carousel_widget/flutter_carousel_widget.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:skeletonizer/skeletonizer.dart';

typedef ItemBuilder<T> = Widget Function(BuildContext context, T data);

class SmartCarousel<T> extends StatelessWidget {
  const SmartCarousel({
    required this.loading,
    required this.builder,
    super.key,
    this.data,
    this.skeletonData,
    this.height,
    this.padding = EdgeInsets.zero,
    this.onSelectionChange,
  }) : sliver = false;

  const SmartCarousel.sliver({
    required this.loading,
    required this.builder,
    super.key,
    this.data,
    this.skeletonData,
    this.height,
    this.padding = EdgeInsets.zero,
    this.onSelectionChange,
  }) : sliver = true;

  final List<T>? data;
  final List<T>? skeletonData;
  final bool loading;
  final ItemBuilder<T> builder;
  final ValueChanged<T>? onSelectionChange;
  final double? height;
  final bool sliver;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    final carousel = !loading && (data?.isEmpty ?? true)
        ? const SizedBox.shrink()
        : Padding(
            padding: padding,
            child: Skeletonizer(
              enabled: loading,
              enableSwitchAnimation: true,
              child: FlutterCarousel.builder(
                options: FlutterCarouselOptions(
                  height: height,
                  floatingIndicator: false,
                  indicatorMargin: UISpacing.l,
                  slideIndicator: CircularSlideIndicator(
                    slideIndicatorOptions: const SlideIndicatorOptions(
                      indicatorRadius: 4,
                      itemSpacing: 12,
                      currentIndicatorColor: UIColors.carouseIndicator,
                      indicatorBackgroundColor: UIColors.carouselBackground,
                    ),
                  ),
                  enlargeFactor: 0.2,
                  viewportFraction: 0.9,
                  enlargeCenterPage: true,
                  onPageChanged: (index, _) => loading
                      ? null
                      : onSelectionChange?.call(
                          data![index],
                        ),
                ),
                itemCount: loading ? skeletonData?.length : data?.length,
                itemBuilder: (context, i, _) => builder(
                  context,
                  loading ? skeletonData![i] : data![i],
                ),
              ),
            ),
          );

    return sliver
        ? SliverToBoxAdapter(
            child: carousel,
          )
        : carousel;
  }
}
