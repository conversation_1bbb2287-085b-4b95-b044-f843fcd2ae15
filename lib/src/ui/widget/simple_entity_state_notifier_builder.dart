import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';

class SimpleEntityStateNotifierBuilder<T> extends StatelessWidget {
  /// Creates an instance of [EntityStateNotifierBuilder].
  const SimpleEntityStateNotifierBuilder({
    required this.listenableEntityState,
    required this.builder,
    super.key,
  });

  /// Source that used to detect change and rebuild.
  final ValueListenable<EntityState<T>> listenableEntityState;

  /// Default builder that is used for the content state and all other states if
  /// no special builders are specified.
  final DataWidgetBuilder<T> builder;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<EntityState<T>>(
      valueListenable: listenableEntityState,
      builder: (ctx, entity, _) {
        return builder(
          ctx,
          entity.isLoadingState,
          entity.errorOrNull,
          entity.data,
        );
      },
    );
  }
}

/// Builder function for content state.
///
/// See also:
/// * [EntityState] - State of some logical entity.
typedef DataWidgetBuilder<T> = Widget Function(
  BuildContext context,
  bool loading,
  Exception? e,
  T? data,
);
