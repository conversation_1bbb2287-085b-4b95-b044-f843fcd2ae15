import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class SliverSubtitle extends StatelessWidget {
  const SliverSubtitle({
    required this.text,
    super.key,
    this.padding = EdgeInsets.zero,
  });

  final String text;
  final EdgeInsets padding;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: padding,
        child: Text(
          text,
          style: context.textTheme.headlineMedium,
        ),
      ),
    );
  }
}
