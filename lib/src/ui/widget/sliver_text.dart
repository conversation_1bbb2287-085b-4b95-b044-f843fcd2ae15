import 'package:flutter/material.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class SliverText extends StatelessWidget {
  const SliverText({
    required this.text,
    super.key,
    this.padding = EdgeInsets.zero,
    this.style,
  });

  final String text;
  final EdgeInsets padding;

  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: padding,
        child: Text(
          text,
          style: style ?? context.textTheme.bodyMedium,
        ),
      ),
    );
  }
}
