import 'package:flutter/material.dart';

import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';

// ignore_for_file: type=lint
/// Field to select one value from a list of Radio Widgets
class FormBuilderCustomRadioGroup<T> extends FormBuilderFieldDecoration<T> {
  final Axis wrapDirection;
  final Color? activeColor;
  final Color? focusColor;
  final Color? hoverColor;
  final ControlAffinity controlAffinity;
  final double wrapRunSpacing;
  final double wrapSpacing;
  final List<FormBuilderFieldOption<T>> options;
  final List<T>? disabled;
  final MaterialTapTargetSize? materialTapTargetSize;
  final OptionsOrientation orientation;
  final TextDirection? wrapTextDirection;
  final VerticalDirection wrapVerticalDirection;
  final Widget? separator;
  final WrapAlignment wrapAlignment;
  final WrapAlignment wrapRunAlignment;
  final WrapCrossAlignment wrapCrossAxisAlignment;

  /// Added to each item if provided.
  /// [GroupedRadio] applies the [itemDecorator] to each Radio
  final BoxDecoration? itemDecoration;

  final String? label;

  /// Creates field to select one value from a list of Radio Widgets
  FormBuilderCustomRadioGroup(
      {super.autovalidateMode = AutovalidateMode.disabled,
      super.enabled,
      super.focusNode,
      super.onSaved,
      super.validator,
      super.decoration = const InputDecoration(
        border: InputBorder.none,
        focusedBorder: InputBorder.none,
        errorBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
      ),
      super.key,
      required super.name,
      required this.options,
      super.initialValue,
      this.activeColor,
      this.controlAffinity = ControlAffinity.leading,
      this.disabled,
      this.focusColor,
      this.hoverColor,
      this.materialTapTargetSize,
      this.orientation = OptionsOrientation.wrap,
      this.separator,
      this.wrapAlignment = WrapAlignment.spaceBetween,
      this.wrapCrossAxisAlignment = WrapCrossAlignment.start,
      this.wrapDirection = Axis.horizontal,
      this.wrapRunAlignment = WrapAlignment.spaceBetween,
      this.wrapRunSpacing = 6.0,
      this.wrapSpacing = 6.0,
      this.wrapTextDirection,
      this.wrapVerticalDirection = VerticalDirection.down,
      super.onChanged,
      super.valueTransformer,
      super.onReset,
      super.restorationId,
      this.itemDecoration,
      this.label})
      : super(
          builder: (FormFieldState<T?> field) {
            final state = field as _FormBuilderCustomRadioGroupState<T>;

            return InputDecorator(
              decoration: state.decoration,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (label != null)
                    Text(
                      label,
                      style: state.decoration.hintStyle,
                    ),
                  const Gap(UISpacing.s),
                  _GroupedRadio<T>(
                    activeColor: activeColor,
                    controlAffinity: controlAffinity,
                    disabled: state.enabled
                        ? disabled
                        : options.map((option) => option.value).toList(),
                    focusColor: focusColor,
                    hoverColor: hoverColor,
                    materialTapTargetSize: materialTapTargetSize,
                    onChanged: (value) {
                      state.didChange(value);
                    },
                    options: options,
                    orientation: orientation,
                    separator: separator,
                    value: state.value,
                    wrapAlignment: wrapAlignment,
                    wrapCrossAxisAlignment: wrapCrossAxisAlignment,
                    wrapDirection: wrapDirection,
                    wrapRunAlignment: wrapRunAlignment,
                    wrapRunSpacing: wrapRunSpacing,
                    wrapSpacing: wrapSpacing,
                    wrapTextDirection: wrapTextDirection,
                    wrapVerticalDirection: wrapVerticalDirection,
                    itemDecoration: itemDecoration,
                  ),
                ],
              ),
            );
          },
        );

  @override
  FormBuilderFieldDecorationState<FormBuilderCustomRadioGroup<T>, T>
      createState() => _FormBuilderCustomRadioGroupState<T>();
}

class _FormBuilderCustomRadioGroupState<T>
    extends FormBuilderFieldDecorationState<FormBuilderCustomRadioGroup<T>,
        T> {}

class _GroupedRadio<T> extends StatefulWidget {
  /// A list of string that describes each checkbox. Each item must be distinct.
  final List<FormBuilderFieldOption<T>> options;

  /// A list of string which specifies automatically checked checkboxes.
  /// Every element must match an item from itemList.
  final T? value;

  /// Specifies which radio option values should be disabled.
  /// If this is null, then no radio options will be disabled.
  final List<T>? disabled;

  /// Specifies the orientation of the elements in itemList.
  final OptionsOrientation orientation;

  /// Called when the value of the checkbox group changes.
  final ValueChanged<T?> onChanged;

  /// The color to use when this checkbox is checked.
  ///
  /// Defaults to [ColorScheme.secondary].
  final Color? activeColor;

  /// Configures the minimum size of the tap target.
  final MaterialTapTargetSize? materialTapTargetSize;

  /// The color for the checkbox's Material when it has the input focus.
  final Color? focusColor;

  /// The color for the checkbox's Material when a pointer is hovering over it.
  final Color? hoverColor;

  //.......................WRAP ORIENTATION.....................................

  /// The direction to use as the main axis.
  ///
  /// For example, if [wrapDirection] is [Axis.horizontal], the default, the
  /// children are placed adjacent to one another in a horizontal run until the
  /// available horizontal space is consumed, at which point a subsequent
  /// children are placed in a new run vertically adjacent to the previous run.
  final Axis wrapDirection;

  /// How the children within a run should be placed in the main axis.
  ///
  /// For example, if [wrapAlignment] is [WrapAlignment.center], the children in
  /// each run are grouped together in the center of their run in the main axis.
  ///
  /// Defaults to [WrapAlignment.start].
  ///
  /// See also:
  ///
  ///  * [wrapRunAlignment], which controls how the runs are placed relative to each
  ///    other in the cross axis.
  ///  * [wrapCrossAxisAlignment], which controls how the children within each run
  ///    are placed relative to each other in the cross axis.
  final WrapAlignment wrapAlignment;

  /// How much space to place between children in a run in the main axis.
  ///
  /// For example, if [wrapSpacing] is 10.0, the children will be spaced at least
  /// 10.0 logical pixels apart in the main axis.
  ///
  /// If there is additional free space in a run (e.g., because the wrap has a
  /// minimum size that is not filled or because some runs are longer than
  /// others), the additional free space will be allocated according to the
  /// [wrapAlignment].
  ///
  /// Defaults to 0.0.
  final double wrapSpacing;

  /// How the runs themselves should be placed in the cross axis.
  ///
  /// For example, if [wrapRunAlignment] is [WrapAlignment.center], the runs are
  /// grouped together in the center of the overall [Wrap] in the cross axis.
  ///
  /// Defaults to [WrapAlignment.start].
  ///
  /// See also:
  ///
  ///  * [wrapAlignment], which controls how the children within each run are placed
  ///    relative to each other in the main axis.
  ///  * [wrapCrossAxisAlignment], which controls how the children within each run
  ///    are placed relative to each other in the cross axis.
  final WrapAlignment wrapRunAlignment;

  /// How much space to place between the runs themselves in the cross axis.
  ///
  /// For example, if [wrapRunSpacing] is 10.0, the runs will be spaced at least
  /// 10.0 logical pixels apart in the cross axis.
  ///
  /// If there is additional free space in the overall [Wrap] (e.g., because
  /// the wrap has a minimum size that is not filled), the additional free space
  /// will be allocated according to the [wrapRunAlignment].
  ///
  /// Defaults to 0.0.
  final double wrapRunSpacing;

  /// How the children within a run should be aligned relative to each other in
  /// the cross axis.
  ///
  /// For example, if this is set to [WrapCrossAlignment.end], and the
  /// [wrapDirection] is [Axis.horizontal], then the children within each
  /// run will have their bottom edges aligned to the bottom edge of the run.
  ///
  /// Defaults to [WrapCrossAlignment.start].
  ///
  /// See also:
  ///
  ///  * [wrapAlignment], which controls how the children within each run are placed
  ///    relative to each other in the main axis.
  ///  * [wrapRunAlignment], which controls how the runs are placed relative to each
  ///    other in the cross axis.
  final WrapCrossAlignment wrapCrossAxisAlignment;

  /// Determines the order to lay children out horizontally and how to interpret
  /// `start` and `end` in the horizontal direction.
  ///
  /// Defaults to the ambient [Directionality].
  ///
  /// If the [wrapDirection] is [Axis.horizontal], this controls order in which the
  /// children are positioned (left-to-right or right-to-left), and the meaning
  /// of the [wrapAlignment] property's [WrapAlignment.start] and
  /// [WrapAlignment.end] values.
  ///
  /// If the [wrapDirection] is [Axis.horizontal], and either the
  /// [wrapAlignment] is either [WrapAlignment.start] or [WrapAlignment.end], or
  /// there's more than one child, then the [wrapTextDirection] (or the ambient
  /// [Directionality]) must not be null.
  ///
  /// If the [wrapDirection] is [Axis.vertical], this controls the order in which
  /// runs are positioned, the meaning of the [wrapRunAlignment] property's
  /// [WrapAlignment.start] and [WrapAlignment.end] values, as well as the
  /// [wrapCrossAxisAlignment] property's [WrapCrossAlignment.start] and
  /// [WrapCrossAlignment.end] values.
  ///
  /// If the [wrapDirection] is [Axis.vertical], and either the
  /// [wrapRunAlignment] is either [WrapAlignment.start] or [WrapAlignment.end], the
  /// [wrapCrossAxisAlignment] is either [WrapCrossAlignment.start] or
  /// [WrapCrossAlignment.end], or there's more than one child, then the
  /// [wrapTextDirection] (or the ambient [Directionality]) must not be null.
  final TextDirection? wrapTextDirection;

  /// Determines the order to lay children out vertically and how to interpret
  /// `start` and `end` in the vertical direction.
  ///
  /// If the [wrapDirection] is [Axis.vertical], this controls which order children
  /// are painted in (down or up), the meaning of the [wrapAlignment] property's
  /// [WrapAlignment.start] and [WrapAlignment.end] values.
  ///
  /// If the [wrapDirection] is [Axis.vertical], and either the [wrapAlignment]
  /// is either [WrapAlignment.start] or [WrapAlignment.end], or there's
  /// more than one child, then the [wrapVerticalDirection] must not be null.
  ///
  /// If the [wrapDirection] is [Axis.horizontal], this controls the order in which
  /// runs are positioned, the meaning of the [wrapRunAlignment] property's
  /// [WrapAlignment.start] and [WrapAlignment.end] values, as well as the
  /// [wrapCrossAxisAlignment] property's [WrapCrossAlignment.start] and
  /// [WrapCrossAlignment.end] values.
  ///
  /// If the [wrapDirection] is [Axis.horizontal], and either the
  /// [wrapRunAlignment] is either [WrapAlignment.start] or [WrapAlignment.end], the
  /// [wrapCrossAxisAlignment] is either [WrapCrossAlignment.start] or
  /// [WrapCrossAlignment.end], or there's more than one child, then the
  /// [wrapVerticalDirection] must not be null.
  final VerticalDirection wrapVerticalDirection;

  final Widget? separator;

  final ControlAffinity controlAffinity;

  /// Applied to a [Container] wrapping each item if provided
  ///
  /// If the [orientation] is set to [OptionsOrientation.vertical] then
  /// [wrapSpacing] is used as inter-item bottom margin
  ///
  /// If the [orientation] is set to [OptionsOrientation.horizontal] then
  /// [wrapSpacing] is used as inter-item right margin
  final BoxDecoration? itemDecoration;

  const _GroupedRadio({
    super.key,
    required this.options,
    required this.orientation,
    required this.onChanged,
    this.value,
    this.disabled,
    this.activeColor,
    this.focusColor,
    this.hoverColor,
    this.materialTapTargetSize,
    this.wrapDirection = Axis.horizontal,
    this.wrapAlignment = WrapAlignment.start,
    this.wrapSpacing = 0.0,
    this.wrapRunAlignment = WrapAlignment.start,
    this.wrapRunSpacing = 0.0,
    this.wrapCrossAxisAlignment = WrapCrossAlignment.start,
    this.wrapTextDirection,
    this.wrapVerticalDirection = VerticalDirection.down,
    this.separator,
    this.controlAffinity = ControlAffinity.leading,
    this.itemDecoration,
  });

  @override
  State<_GroupedRadio<T?>> createState() => _GroupedRadioState<T>();
}

class _GroupedRadioState<T> extends State<_GroupedRadio<T?>> {
  @override
  Widget build(BuildContext context) {
    final widgetList = <Widget>[];
    for (int i = 0; i < widget.options.length; i++) {
      widgetList.add(buildItem(i));
    }

    switch (widget.orientation) {
      case OptionsOrientation.auto:
        return OverflowBar(
          alignment: MainAxisAlignment.spaceBetween,
          spacing: widget.wrapSpacing,
          overflowSpacing: widget.wrapRunSpacing,
          children: widgetList,
        );
      case OptionsOrientation.vertical:
        return SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: widget.wrapRunSpacing,
            children: widgetList,
          ),
        );
      case OptionsOrientation.horizontal:
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: widgetList,
            spacing: widget.wrapSpacing,
          ),
        );
      case OptionsOrientation.wrap:
        return SingleChildScrollView(
          child: Wrap(
            spacing: widget.wrapSpacing,
            runSpacing: widget.wrapRunSpacing,
            textDirection: widget.wrapTextDirection,
            crossAxisAlignment: widget.wrapCrossAxisAlignment,
            verticalDirection: widget.wrapVerticalDirection,
            alignment: widget.wrapAlignment,
            direction: Axis.horizontal,
            runAlignment: widget.wrapRunAlignment,
            children: widgetList,
          ),
        );
    }
  }

  /// the composite of all the components for the option at index
  Widget buildItem(int index) {
    final option = widget.options[index];
    final optionValue = option.value;
    final isOptionDisabled = true == widget.disabled?.contains(optionValue);
    final control = Radio<T?>(
      groupValue: widget.value,
      activeColor: widget.activeColor,
      focusColor: widget.focusColor,
      hoverColor: widget.hoverColor,
      materialTapTargetSize: widget.materialTapTargetSize,
      value: optionValue,
      onChanged: isOptionDisabled
          ? null
          : (T? selected) {
              widget.onChanged(selected);
            },
    );

    final label = GestureDetector(
      onTap: isOptionDisabled
          ? null
          : () {
              widget.onChanged(optionValue);
            },
      child: option,
    );

    Widget compositeItem = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.controlAffinity == ControlAffinity.leading) control,
            Flexible(
                child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: label,
            )),
            if (widget.controlAffinity == ControlAffinity.trailing) control,
            if (widget.orientation != OptionsOrientation.vertical &&
                widget.separator != null &&
                index != widget.options.length - 1)
              widget.separator!,
          ],
        ),
        if (widget.orientation == OptionsOrientation.vertical &&
            widget.separator != null &&
            index != widget.options.length - 1)
          widget.separator!,
      ],
    );

    if (widget.itemDecoration != null) {
      compositeItem = Container(
        decoration: widget.itemDecoration,
        margin: EdgeInsets.only(
          bottom: widget.orientation == OptionsOrientation.vertical
              ? widget.wrapSpacing
              : 0.0,
          right: widget.orientation == OptionsOrientation.horizontal
              ? widget.wrapSpacing
              : 0.0,
        ),
        child: compositeItem,
      );
    }

    return compositeItem;
  }
}
