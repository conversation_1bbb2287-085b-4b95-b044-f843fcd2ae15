import 'package:flutter/material.dart';
import 'package:sba/src/common/extension/int_extension.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:skeletonizer/skeletonizer.dart';

typedef ItemBuilder<T> = Widget Function(BuildContext context, T data);

class SmartGridView<T> extends StatelessWidget {
  const SmartGridView({
    required this.loading,
    required this.delegate,
    required this.builder,
    this.emptyText,
    this.centerEmptyText = true,
    super.key,
    this.data,
    this.skeletonData,
    this.padding = EdgeInsets.zero,
    this.action,
    this.showActionWhenData = true,
  }) : sliver = false;

  const SmartGridView.sliver({
    required this.loading,
    required this.delegate,
    required this.builder,
    this.emptyText,
    this.centerEmptyText = true,
    super.key,
    this.data,
    this.skeletonData,
    this.padding = EdgeInsets.zero,
    this.action,
    this.showActionWhenData = true,
  }) : sliver = true;

  final SliverGridDelegate delegate;
  final List<T>? data;
  final List<T>? skeletonData;
  final bool loading;
  final ItemBuilder<T> builder;
  final Widget? action;
  final String? emptyText;
  final bool centerEmptyText;
  final EdgeInsets padding;
  final bool showActionWhenData;
  final bool sliver;

  @override
  Widget build(BuildContext context) {
    final odl = data?.length ?? 0;
    final dataLength =
        odl + (action != null && (showActionWhenData || odl == 0) ? 1 : 0);
    final isEmpty = !loading && dataLength <= 0;

    if (isEmpty) {
      final text = Padding(
        padding: padding,
        child: Text(
          emptyText ?? '',
          style: context.textTheme.bodyMedium,
        ),
      );

      final textWidget = centerEmptyText
          ? Center(
              child: text,
            )
          : text;

      return sliver
          ? centerEmptyText
              ? SliverFillRemaining(
                  hasScrollBody: false,
                  child: textWidget,
                )
              : SliverToBoxAdapter(
                  child: textWidget,
                )
          : textWidget;
    }

    if (sliver) {
      return SliverPadding(
        padding: padding,
        sliver: Skeletonizer.sliver(
          enabled: loading,
          child: SliverGrid.builder(
            gridDelegate: delegate,
            itemCount: loading ? skeletonData?.length ?? 0 : dataLength,
            itemBuilder: (context, i) {
              if (i.isLastIndexOf(dataLength) && action != null && !loading) {
                if (showActionWhenData || odl == 0) return action;
              }

              final element =
                  (loading ? skeletonData : data)?.elementAtOrNull(i);
              return element == null
                  ? const SizedBox.shrink()
                  : builder(context, element);
            },
          ),
        ),
      );
    }

    return Skeletonizer(
      enabled: loading,
      enableSwitchAnimation: true,
      child: GridView.builder(
        gridDelegate: delegate,
        padding: padding,
        itemCount: loading ? skeletonData?.length ?? 0 : dataLength,
        itemBuilder: (context, i) {
          if (i.isLastIndexOf(dataLength) && action != null && !loading) {
            if (showActionWhenData || odl == 0) return action;
          }

          final element = (loading ? skeletonData : data)?.elementAtOrNull(i);
          return element == null
              ? const SizedBox.shrink()
              : builder(context, element);
        },
      ),
    );
  }
}
