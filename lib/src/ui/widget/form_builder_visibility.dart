import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class FormBuilderVisibility extends StatelessWidget {
  const FormBuilderVisibility({
    required this.visible,
    required this.child,
    this.padding = EdgeInsets.zero,
    super.key,
  });

  final ValueListenable<bool> visible;
  final EdgeInsets padding;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: visible,
      builder: (context, show, child) => show
          ? Padding(
              padding: padding,
              child: child,
            )
          : const SizedBox.shrink(),
      child: child,
    );
  }
}
