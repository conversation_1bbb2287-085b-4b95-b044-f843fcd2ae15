import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/components/dashed_border.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';
import 'package:sba/src/ui/theme/ui_colors.dart';
import 'package:sba/src/ui/theme/ui_spacing.dart';

class ActionBox extends StatelessWidget {
  const ActionBox({
    required this.icon,
    required this.text,
    super.key,
    this.onTap,
  });

  final IconData icon;
  final String text;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: IgnorePointer(
        child: Container(
          decoration: BoxDecoration(
            color: UIColors.background,
            border: const DashedBorder.fromBorderSide(
              dashLength: 10,
              spaceLength: 5,
              side: BorderSide(
                color: UIColors.primary,
              ),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(
            horizontal: UISpacing.l,
            vertical: UISpacing.m,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              FloatingActionButton(
                onPressed: () {},
                child: Icon(icon),
              ),
              const Gap(UISpacing.s),
              Text(
                text,
                style: context.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
