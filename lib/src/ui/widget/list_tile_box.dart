import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class ListTileBox extends StatelessWidget {
  const ListTileBox({
    required this.title,
    super.key,
    this.icon,
    this.subtitle,
    this.description,
    this.trailing,
    this.selected = false,
    this.warning = false,
    this.onTap,
  });

  const ListTileBox.navigation({
    required this.title,
    super.key,
    this.icon,
    this.subtitle,
    this.description,
    this.warning = false,
    this.onTap,
  })  : trailing = const Icon(
          Icons.arrow_forward_ios_sharp,
          size: 18,
        ),
        selected = false;

  final Widget? icon;
  final String title;
  final String? subtitle;
  final String? description;
  final Widget? trailing;
  final bool selected;

  final bool warning;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor =
        selected ? UIColors.background : Colors.white;
    final effectiveBorderColor = selected ? UIColors.primary : UIColors.border;

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: ListTile(
        onTap: onTap,
        tileColor: effectiveBackgroundColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: UISpacing.m,
          vertical: UISpacing.xs,
        ),
        shape: Border.all(color: effectiveBorderColor),
        leading: icon != null
            ? SizedBox(
                width: 36,
                height: 36,
                child: ColorFiltered(
                  colorFilter: ColorFilter.mode(
                    context.theme.colorScheme.primary,
                    BlendMode.srcIn,
                  ),
                  child: icon,
                ),
              )
            : null,
        title: Row(
          mainAxisSize: MainAxisSize.min,
          spacing: UISpacing.s,
          children: [
            Text(
              title,
              maxLines: 1,
            ),
            if (warning)
              Icon(
                Icons.error,
                color: context.theme.colorScheme.error,
                size: 14,
              ),
          ],
        ),
        subtitle: (subtitle != null || description != null)
            ? Padding(
                padding: const EdgeInsets.only(top: UISpacing.s),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    if (subtitle != null)
                      Text(
                        subtitle!,
                        maxLines: 1,
                      ),
                    if (description != null && subtitle != null)
                      const Gap(UISpacing.xs),
                    if (description != null)
                      Text(
                        description!,
                        maxLines: 1,
                      ),
                  ],
                ),
              )
            : null,
        trailing: trailing,
      ),
    );
  }
}
