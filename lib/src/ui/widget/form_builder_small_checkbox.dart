import 'package:flutter/material.dart';

import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/ui/theme/theme.dart';

// ignore_for_file: type=lint
class FormBuilderSmallCheckbox extends FormBuilderFieldDecoration<bool> {
  final String title;

  final EdgeInsets contentPadding;

  /// Normally, this property is left to its default value, false.
  final bool selected;

  final bool expanded;

  /// Creates a single Checkbox field
  FormBuilderSmallCheckbox({
    super.key,
    required super.name,
    super.validator,
    super.initialValue,
    super.decoration = const InputDecoration(
      border: InputBorder.none,
      focusedBorder: InputBorder.none,
      enabledBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      disabledBorder: InputBorder.none,
    ),
    super.onChanged,
    super.valueTransformer,
    super.enabled,
    super.onSaved,
    super.autovalidateMode = AutovalidateMode.disabled,
    super.onReset,
    super.focusNode,
    super.restorationId,
    required this.title,
    this.contentPadding = EdgeInsets.zero,
    this.selected = false,
    this.expanded = false,
  }) : super(
          builder: (FormFieldState<bool?> field) {
            final state = field as _FormBuilderSmallCheckboxState;

            final text = Text(
              title,
              style: state.decoration.hintStyle,
            );

            return InputDecorator(
              decoration: state.decoration,
              child: GestureDetector(
                onTap: state.enabled
                    ? () => state.didChange(!(state.value ?? false))
                    : null,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Checkbox(
                      value: state.value ?? false,
                      onChanged: state.enabled ? state.didChange : null,
                    ),
                    Gap(UISpacing.s),
                    if (expanded) Expanded(child: text) else text,
                  ],
                ),
              ),
            );
          },
        );

  @override
  FormBuilderFieldDecorationState<FormBuilderSmallCheckbox, bool>
      createState() => _FormBuilderSmallCheckboxState();
}

class _FormBuilderSmallCheckboxState
    extends FormBuilderFieldDecorationState<FormBuilderSmallCheckbox, bool> {}
