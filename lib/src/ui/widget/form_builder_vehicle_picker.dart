import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/ui/widget/value_listener.dart';

class FormBuilderVehiclePicker extends StatelessWidget {
  const FormBuilderVehiclePicker({
    required this.name,
    super.key,
    this.vehicles,
    this.plateNumber,
    this.onOtherSelected,
  });

  final String name;
  final List<VehicleData>? vehicles;
  final String? plateNumber;
  final ValueChanged<bool>? onOtherSelected;

  @override
  Widget build(BuildContext context) {
    final data = vehicles?.map((e) => e.plateNumber).toList().asCopy() ??
        List.empty(growable: true)
      ..add(context.l10n.form_type_option_vehicle_other);

    final initialValue = plateNumber == null
        ? null
        : data.firstWhere(
            (e) => e == plateNumber,
            orElse: () => context.l10n.form_type_option_vehicle_other,
          );

    return ValueListener(
      value: initialValue,
      onChange: (e) => onOtherSelected
          ?.call(e == context.l10n.form_type_option_vehicle_other),
      child: FormBuilderDropdown<String>(
        name: name,
        initialValue: initialValue,
        decoration: InputDecoration(
          labelText: context.l10n.form_vehicle,
          hintText: context.l10n.form_vehicle_hint,
        ),
        items: data
            .map((data) => DropdownMenuItem(value: data, child: Text(data)))
            .toList(),
        validator: FormBuilderValidators.required(
          errorText: context.l10n.form_validation_required,
        ),
        valueTransformer: (p) =>
            vehicles?.firstWhereOrNull((e) => e.plateNumber == p),
        onChanged: (data) => onOtherSelected
            ?.call(data == context.l10n.form_type_option_vehicle_other),
      ),
    );
  }
}
