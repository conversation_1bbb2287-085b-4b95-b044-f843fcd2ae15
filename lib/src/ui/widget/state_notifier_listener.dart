import 'package:elementary_helper/elementary_helper.dart';
import 'package:flutter/material.dart';

// Listener that uses [ListenableState] as a source of data.
/// Usually can be helpful with the [StateNotifier].
class StateNotifierListener<T> extends StatefulWidget {
  /// Creates an instance of [StateNotifierBuilder].
  const StateNotifierListener({
    required this.listenableState,
    required this.child,
    required this.onChange,
    super.key,
  });

  /// Source that used to detect change and rebuild.
  final ListenableState<T> listenableState;
  final ValueChanged<T?> onChange;
  final Widget child;

  @override
  State<StateNotifierListener<T>> createState() =>
      _StateNotifierListenerState<T>();
}

class _StateNotifierListenerState<T> extends State<StateNotifierListener<T>> {
  @override
  void initState() {
    super.initState();
    widget.listenableState.addListener(_valueChanged);
    widget.onChange(widget.listenableState.value);
  }

  @override
  void didUpdateWidget(StateNotifierListener<T> oldWidget) {
    if (oldWidget.listenableState != widget.listenableState) {
      oldWidget.listenableState.removeListener(_valueChanged);
      widget.listenableState.addListener(_valueChanged);
      widget.onChange(widget.listenableState.value);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.listenableState.removeListener(_valueChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  void _valueChanged() {
    widget.onChange(widget.listenableState.value);
  }
}
