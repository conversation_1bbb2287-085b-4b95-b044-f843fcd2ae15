import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class FormBuilderMultiVisibility extends StatelessWidget {
  const FormBuilderMultiVisibility({
    required this.visible,
    required this.children,
    this.padding = EdgeInsets.zero,
    super.key,
  });

  final ValueListenable<bool> visible;
  final EdgeInsets padding;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: visible,
      builder: (context, show, child) => show
          ? Padding(
              padding: padding,
              child: child,
            )
          : const SizedBox.shrink(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children,
      ),
    );
  }
}
