import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_sms/flutter_sms.dart';
import 'package:logger/logger.dart';
import 'package:sba/src/common/extension/build_context_extension.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/common/external_launcher/external_launcher.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/modal/message_dialog.dart';

const _tag = 'SmsHandler';

final class SmsHandler {
  SmsHandler({required Logger logger}) : _logger = logger;

  final Logger _logger;

  Future<Result<void>> sendSms(
    BuildContext context,
    String number,
    String text,
  ) async {
    try {
      var result = Platform.isAndroid
          ? await _sendInternal(number, text)
          : Result<void>.otherError('Internal sending not supported');

      if (result.isFailure) result = await _sendFallback(context, number, text);

      return result;
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> _sendInternal(String number, String body) async {
    try {
      final hasPermissions = await canSendSMS();
      if (!hasPermissions) {
        return Result.otherError('No permissions');
      }

      final result =
          await sendSMS(message: body, recipients: [number], sendDirect: true)
              .then((value) {
                _logger.d('$_tag: $value');
                return value;
              })
              .then((value) => value.containsIgnoreCase('Sent'))
              .catchError((_) => false);

      return result
          ? Result.success(null)
          : Result.otherError('Failed to send');
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> _sendFallback(
    BuildContext context,
    String number,
    String body,
  ) async {
    final result = await ExternalLauncher.sms(number, body);
    if (!result) {
      return Result.otherError('SMS not sent');
    }

    if (!context.mounted) {
      return Result.canceled();
    }

    final completer = Completer<Result<void>>();

    unawaited(
      context.showMessageDialog(
        builder: (context) => MessageDialog.action(
          type: MessageDialogType.info,
          title: context.l10n.parking_sms_message_title,
          text: context.l10n.parking_sms_message_test,
          primaryActionText: context.l10n.action_yes,
          secondaryActionText: context.l10n.action_no,
          secondaryAction: () => completer.complete(Result.canceled()),
          primaryAction: () => completer.complete(Result.success(null)),
        ),
      ),
    );

    return completer.future;
  }
}
