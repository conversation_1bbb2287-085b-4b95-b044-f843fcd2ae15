import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:memory_cache/memory_cache.dart';
import 'package:sba/src/api/model/toll/toll_error_dto.dart';
import 'package:sba/src/common/result/result.dart';

//ignore_for_file: avoid_dynamic_calls

typedef CallFunction<T> = FutureOr<T> Function();

Future<Result<T>> callWithCachedResult<T extends dynamic>(
  CallFunction<T> call, {
  String? cacheKey,
  Duration? ttl,
}) async {
  final key = cacheKey ?? call.toString();

  final result =
      MemoryCache.instance.read<Result<T>>(key) ?? await callWithResult(call);

  if (result.isSuccess) {
    MemoryCache.instance.createIfAbsent(key, result, expiry: ttl);
  }

  return result;
}

Future<Result<T>> callWithResult<T extends dynamic>(
  CallFunction<T> call,
) async {
  try {
    final result = await call();
    return Result.success(result);
  } on DioException catch (e) {
    final code = e.response?.statusCode ?? 0;
    final message = e.response?.statusMessage ?? '';
    final data = e.response?.data;

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
      case DioExceptionType.badCertificate:
        return Result.networkError();
      case DioExceptionType.badResponse:
        return Result.httpError(code, message, data: data);
      case DioExceptionType.cancel:
        return Result.canceled();
      case DioExceptionType.unknown:
        return Result.otherError(e);
    }
  } catch (e) {
    return Result.otherError(e);
  }
}

Future<Result<T>> callWithTollResult<T extends dynamic>(
  CallFunction<T> call,
) async {
  try {
    final result = await callWithResult(call);
    if (result is HttpError) {
      final error = result as HttpError;
      if (error.code == 500) {
        final jsonString = error.data.substring(
          error.data.indexOf('{'),
          error.data.lastIndexOf('}') + 1,
        );
        final json = jsonDecode(jsonString.toString()) as Map<String, dynamic>;
        final first = (json['errors'] as List?)?.firstOrNull;

        if (first != null) {
          return Result.tollError(
            TollErrorDto.fromJson(first as Map<String, dynamic>),
          );
        }
      }
    }

    return result;
  } catch (e) {
    return Result.otherError(e);
  }
}
