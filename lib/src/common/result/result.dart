import 'package:sba/src/api/model/toll/toll_error_dto.dart';

sealed class Result<S> {
  const Result();

  factory Result.success(S data) => Success(data);

  factory Result.networkError() => NetworkError<S>();

  factory Result.httpError(int code, String message, {dynamic data}) =>
      HttpError<S>(code: code, message: message, data: data);

  factory Result.canceled({S? data}) => Cancelled<S>(data);

  factory Result.paymentError() => PaymentError<S>();

  factory Result.tollError(TollErrorDto error) => TollError<S>(error: error);

  factory Result.otherError(Object error) => OtherError<S>(error: error);

  bool get isSuccess => this is Success<S>;

  bool get isFailure => this is Failure<S>;

  bool get isCancelled => this is Cancelled<S>;

  S? get maybeValue => this is Success<S>
      ? (this as Success<S>).value
      : this is Cancelled<S>
          ? (this as Cancelled<S>).value
          : null;
}

final class Success<S> extends Result<S> {
  const Success(this.value);

  final S value;
}

final class Cancelled<S> extends Result<S> {
  const Cancelled(this.value);

  final S? value;
}

sealed class Failure<S> extends Result<S> {}

final class HttpError<S> extends Failure<S> {
  HttpError({required this.code, required this.message, this.data});

  final int code;
  final String message;

  final dynamic data;
}

final class NetworkError<S> extends Failure<S> {}

final class PaymentError<S> extends Failure<S> {}

final class TollError<S> extends Failure<S> {
  TollError({required this.error});

  final TollErrorDto error;
}

final class OtherError<S> extends Failure<S> {
  OtherError({required this.error});

  final Object error;
}
