import 'package:flutter/widgets.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/localization/localization_extension.dart';

typedef ResultTransformer<S, Q> = Q Function(S data);

typedef HttpCodeTextBuilder = String? Function(int code);

typedef Combiner<S> = S Function(S v, S o);

extension ResultExtension<S> on Result<S> {
  Result<void> toVoid() => map((_) => {});

  Result<S> combine(Result<S> other, {Combiner<S>? combiner}) {
    if (isSuccess && other.isSuccess) {
      return combiner == null
          ? this
          : Result.success(
              combiner(
                maybeValue as S,
                other.maybeValue as S,
              ),
            );
    }

    if (isFailure) return this;

    return other;
  }

  Result<Q> map<Q>(ResultTransformer<S, Q> transformer) => switch (this) {
        Success(value: final value) => Result.success(transformer(value)),
        HttpError(code: final code, message: final message, data: final data) =>
          Result.httpError(code, message, data: data),
        NetworkError() => Result.networkError(),
        Cancelled(value: final value) =>
          Result.canceled(data: value == null ? null : transformer(value)),
        OtherError(error: final error) => Result.otherError(error),
        TollError(error: final error) => Result.tollError(error),
        PaymentError() => Result.paymentError(),
      };

  Result<Q?> mapNullable<Q>(ResultTransformer<S, Q?> transformer) =>
      switch (this) {
        Success(value: final value) => Result.success(transformer(value)),
        HttpError(code: final code, message: final message, data: final data) =>
          Result.httpError(code, message, data: data),
        NetworkError() => Result.networkError(),
        Cancelled(value: final value) =>
          Result.canceled(data: value == null ? null : transformer(value)),
        OtherError(error: final error) => Result.otherError(error),
        TollError(error: final error) => Result.tollError(error),
        PaymentError() => Result.paymentError(),
      };

  String failureLocalizedText(
    BuildContext context, {
    HttpCodeTextBuilder? codeTextBuilder,
  }) =>
      switch (this) {
        Success() || Cancelled() => '',
        HttpError(code: final code, message: final message) =>
          codeTextBuilder?.call(code) ?? '$code: $message',
        NetworkError() => context.l10n.error_network_text,
        PaymentError() => context.l10n.error_payment_text,
        TollError(error: final error) => switch (error.code) {
            202 || 206 || 207 => context.l10n.error_toll_start_date,
            203 => context.l10n.error_toll_email,
            204 => context.l10n.error_toll_vehicle,
            210 => context.l10n.error_toll_activated,
            217 => context.l10n.error_toll_has_prucase,
            _ => context.l10n.error_general_text,
          },
        OtherError() => context.l10n.error_general_text,
      };
}

extension ResultListExtension<S> on Result<List<S>> {
  Result<List<Q>> listMap<Q>(
    ResultTransformer<S, Q> transformer, {
    bool growable = false,
  }) =>
      map(
        (data) => data.map((e) => transformer(e)).toList(growable: growable),
      );
}

extension FailureExtension<S> on Failure<S> {
  Failure<Q> transform<Q>() => switch (this) {
        HttpError(code: final code, message: final message, data: final data) =>
          HttpError(code: code, message: message, data: data),
        NetworkError() => NetworkError(),
        OtherError(error: final error) => OtherError(error: error),
        PaymentError() => PaymentError(),
        TollError(error: final error) => TollError(error: error),
      };
}
