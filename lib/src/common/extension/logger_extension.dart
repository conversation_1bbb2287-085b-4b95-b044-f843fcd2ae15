import 'package:logger/logger.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

Level levelFromCodeBuilder(int value) =>
    Level.values.firstWhere((it) => it.value == value, orElse: () => Level.all);

OSLogLevel osLogLevelFromCodeBuilder(int value) {
  final level = levelFromCodeBuilder(value);

  return switch (level) {
    Level.all => OSLogLevel.verbose,
    Level.debug => OSLogLevel.debug,
    Level.info => OSLogLevel.info,
    Level.warning => OSLogLevel.warn,
    Level.error || Level.trace => OSLogLevel.error,
    Level.fatal => OSLogLevel.fatal,
    Level.off => OSLogLevel.none,
    _ => OSLogLevel.none,
  };
}
