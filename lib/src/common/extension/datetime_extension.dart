// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:sba/src/localization/localization_extension.dart';
import 'package:timeago/timeago.dart' as timeago;

extension DatetimeExtension on DateTime {
  String formatTimeAgo(BuildContext context) => timeago.format(
        toLocal(),
        locale: Localizations.maybeLocaleOf(context)?.languageCode,
      );

  String formatTime(BuildContext context) =>
      DateFormat.Hm(context.locale.toLanguageTag()).format(toLocal());

  String formatDate(BuildContext context) =>
      DateFormat.yMd(context.locale.toLanguageTag()).format(toLocal());

  String formatDateAndTime(BuildContext context) =>
      DateFormat.yMd(context.locale.toLanguageTag()).add_Hm().format(toLocal());

  bool isLessThan(Duration duration) {
    final now = DateTime.now();

    return now.isAfter(toLocal()) || now.difference(this).abs() < duration;
  }
}
