import 'package:flutter/cupertino.dart';

extension IterableExtension<T> on Iterable<T> {
  Iterable<T> intersperse(T element) sync* {
    final iterator = this.iterator;
    if (iterator.moveNext()) {
      yield iterator.current;
      while (iterator.moveNext()) {
        yield element;
        yield iterator.current;
      }
    }
  }
}

extension IterableWidgetExtension on Iterable<Widget> {
  Iterable<Widget> intersperseWidget(Widget element) => intersperse(element);
}
