import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';
import 'package:sba/src/localization/localization_extension.dart';

extension NumExtension on num {
  String formatToMileage(BuildContext context) {
    final value = NumberFormat.decimalPattern(context.locale.toLanguageTag())
        .format(this);
    return '$value км';
  }

  String formatLiters(BuildContext context) =>
      NumberFormat.decimalPatternDigits(
        locale: context.locale.toLanguageTag(),
        decimalDigits: 2,
      ).format(this);

  String formatCurrency(BuildContext context, {String symbol = 'лв'}) =>
      NumberFormat.currency(
        locale: context.locale.toLanguageTag(),
        symbol: symbol,
      ).format(this);
}
