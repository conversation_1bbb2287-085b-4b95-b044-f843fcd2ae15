import 'dart:math';

extension StringExtension on String {
  String getInitials({int limitTo = 2}) {
    if (isEmpty) return this;

    final buffer = StringBuffer();
    final split = this.split(' ');
    if (split.isEmpty) {
      return this[0];
    }

    final limit = min(limitTo, split.length);
    for (var i = 0; i < limit; i++) {
      if (split[i].isNotEmpty) buffer.write(split[i][0]);
    }

    return buffer.toString();
  }

  bool containsIgnoreCase(String other) =>
      toLowerCase().contains(other.toLowerCase());
}

extension StringNullableExtension on String? {
  bool get isNullOrEmpty => this?.isEmpty ?? true;
}
