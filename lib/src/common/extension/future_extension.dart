import 'dart:async';

const defaultRefreshDuration = Duration(seconds: 2);

extension FutureOrExtensionM<T> on FutureOr<T> {
  Future<T> withMinimumDuration({
    Duration duration = defaultRefreshDuration,
  }) async {
    final stopwatch = Stopwatch()..start();
    final result = await this;
    stopwatch.stop();
    final elapsed = stopwatch.elapsed;
    if (elapsed < duration) {
      await Future<void>.delayed(duration - elapsed);
    }
    return result;
  }
}
