extension ListExtension<T> on List<T> {
  int indexOfNullable(T? t, {int? def}) {
    final ind = t == null ? -1 : indexOf(t);
    return ind == -1 ? def ?? -1 : ind;
  }

  void replaceWhere(bool Function(T) test, T replace) {
    final index = indexWhere(test);
    if (index < 0) return;

    this[index] = replace;
  }

  List<T> replaceWhereOrAdd(bool Function(T) test, T replace) {
    final index = indexWhere(test);
    if (index < 0) return [...this, replace];

    this[index] = replace;
    return this;
  }

  List<T> asCopy() => List.of(this);

  List<T?> addFirst(T? element) => [element, ...this];

  List<T?> addLast(T? element) => [...this, element];

  List<T> unique<Id>(Id Function(T element)? id) {
    final ids = <Id>{};
    final list = List<T>.from(this)
      ..retainWhere((x) => ids.add(id != null ? id(x) : x as Id));
    return list;
  }
}
