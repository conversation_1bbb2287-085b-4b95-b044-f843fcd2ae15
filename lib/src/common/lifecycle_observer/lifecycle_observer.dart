import 'package:flutter/scheduler.dart';
import 'package:flutter/widgets.dart';
import 'package:rxdart/rxdart.dart';

final class LifecycleObserver {
  factory LifecycleObserver() => _instance ??= LifecycleObserver._();

  LifecycleObserver._() {
    _listener = AppLifecycleListener(onStateChange: _stream.add);
  }

  static LifecycleObserver? _instance;
  late AppLifecycleListener _listener;

  final _stream = BehaviorSubject<AppLifecycleState>();

  Stream<AppLifecycleState> get lifecycleStream => _stream.startWith(
        SchedulerBinding.instance.lifecycleState ?? AppLifecycleState.inactive,
      );

  void dispose() {
    _listener.dispose();
    _stream.close();
  }
}
