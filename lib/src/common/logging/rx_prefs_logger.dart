import 'package:logger/logger.dart';
import 'package:rx_shared_preferences/rx_shared_preferences.dart';

class RxPrefsLogger extends RxStorageDefaultLogger<String, void>
    implements RxSharedPreferencesLogger {
  /// Construct a [RxSharedPreferencesDefaultLogger].
  const RxPrefsLogger(
    this.logger, {
    super.tag = defaultTag,
    super.trimValueOutput,
  });

  /// Default logger tag.
  static const defaultTag = '⚡ RxSharedPreferences';

  final Logger logger;

  @override
  bool handleLogEvent(RxStorageLoggerEvent<String, void> event) {
    const rightArrow = RxStorageDefaultLogger.rightArrow;
    const leftArrow = RxStorageDefaultLogger.leftArrow;

    switch (event) {
      case ReloadFailureEvent():
        logger.e('$tag $leftArrow Reload $rightArrow ${event.error}');
      case ClearFailureEvent():
        logger.e('$tag $leftArrow Clear $rightArrow ${event.error}');
      case WriteFailureEvent():
        logger.e('$tag $leftArrow Write $rightArrow ${event.error}');
      case ReadValueFailureEvent():
        logger.e('$tag $leftArrow Read $rightArrow ${event.error}');
      case RemoveFailureEvent():
        logger.e('$tag $leftArrow Remove $rightArrow ${event.error}');
    }

    return true;
  }
}
