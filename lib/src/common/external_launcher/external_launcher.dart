import 'dart:io' show Platform;
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';

class ExternalLauncher {
  static Future<bool> launchNavigation(LatLng coord) {
    if (Platform.isAndroid) {
      return launchUrl(
        Uri(
          scheme: 'geo',
          host: '0.0',
          queryParameters: {
            'q': '${coord.latitude},${coord.longitude}',
          },
        ),
      );
    }
    if (Platform.isIOS) {
      return launchUrl(
        Uri.https('maps.apple.com', '/', {
          'll': '${coord.latitude},${coord.longitude}',
          'q': '${coord.latitude},${coord.longitude}',
        }),
      );
    }

    return launchUrl(
      Uri.https(
        'www.google.com',
        '/maps/search/',
        {'api': '1', 'query': '${coord.latitude},${coord.longitude}'},
      ),
    );
  }

  static Future<bool> call(String number) {
    return launchUrl(
      Uri(scheme: 'tel', path: number),
    );
  }

  static Future<bool> mail(String email) {
    return launchUrl(
      Uri(
        scheme: 'mailto',
        path: 'email',
      ),
    );
  }

  static Future<bool> sms(String number, String body) {
    return launchUrl(
      Uri(
        scheme: 'sms',
        path: number,
        queryParameters: {'body': body},
      ),
    );
  }

  static Future<bool> uri(
    String uri, {
    LaunchMode mode = LaunchMode.inAppBrowserView,
  }) {
    return launchUrl(Uri.parse(uri), mode: mode);
  }
}
