import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

// ignore_for_file: type=lint, strict_raw_type, inference_failure_on_instance_creation

typedef FieldStateGetter = FormBuilderFieldState Function();

abstract class CustomValidators {
  static FormFieldValidator<T> passwordMatch<T>({
    required FieldStateGetter passwordField,
    String? errorText,
    bool checkNullOrEmpty = true,
  }) =>
      PasswordMatchValidator(
        passwordField: passwordField,
        errorText: errorText,
        checkNullOrEmpty: checkNullOrEmpty,
      ).validate;
}

class PasswordMatchValidator<T> extends BaseValidator<T> {
  PasswordMatchValidator({
    required this.passwordField,
    super.errorText,
    super.checkNullOrEmpty,
  });

  final FieldStateGetter passwordField;

  @override
  String? validateValue(T valueCandidate) {
    return passwordField().value == valueCandidate ? null : errorText;
  }
}
