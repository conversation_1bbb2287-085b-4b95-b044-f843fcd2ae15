import 'dart:async';

import 'package:flutter/material.dart';

class StreamListenable<T> extends ChangeNotifier {
  StreamListenable(this._stream) {
    notifyListeners();
    _subscription = _stream.asBroadcastStream().listen(
      (T t) {
        notifyListeners();
      },
    );
  }

  late final Stream<T> _stream;
  late final StreamSubscription<T> _subscription;
  Future<T> get state => _stream.first;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
