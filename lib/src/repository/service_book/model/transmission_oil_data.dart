import 'package:flutter/material.dart';
import 'package:sba/src/api/model/service_book/transmission_oil_change_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class TransmissionOilData {
  const TransmissionOilData({
    required this.changeDate,
    required this.autoService,
    required this.oilType,
    required this.oilQuantity,
    required this.mileage,
    required this.price,
    required this.notes,
    this.id,
    this.vehicleId,
  });

  factory TransmissionOilData.fromDto(TransmissionOilChangeDto dto) =>
      TransmissionOilData(
        id: dto.id,
        vehicleId: dto.vehicleId,
        changeDate: dto.changeDate,
        autoService: dto.autoService,
        oilType: dto.oilType,
        oilQuantity: dto.oilQuantity,
        mileage: dto.mileage,
        price: dto.price,
        notes: dto.notes,
      );

  factory TransmissionOilData.fake() => TransmissionOilData(
        changeDate: DateTime.now(),
        autoService: BoneMock.name,
        oilType: BoneMock.name,
        oilQuantity: 0,
        mileage: 0,
        price: 0,
        notes: '',
      );

  factory TransmissionOilData.empty() => TransmissionOilData(
        changeDate: DateTime.now(),
        autoService: '',
        oilType: '',
        oilQuantity: 0,
        mileage: 0,
        price: 0,
        notes: '',
      );

  final int? id;
  final int? vehicleId;
  final DateTime changeDate;
  final String autoService;
  final String oilType;
  final double oilQuantity;
  final int mileage;
  final double price;
  final String? notes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransmissionOilData &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  TransmissionOilData copyWith({
    int? id,
    int? vehicleId,
    DateTime? changeDate,
    String? autoService,
    String? oilType,
    double? oilQuantity,
    int? mileage,
    double? price,
    String? notes,
  }) {
    return TransmissionOilData(
      id: id ?? this.id,
      vehicleId: vehicleId ?? this.vehicleId,
      changeDate: changeDate ?? this.changeDate,
      autoService: autoService ?? this.autoService,
      oilType: oilType ?? this.oilType,
      oilQuantity: oilQuantity ?? this.oilQuantity,
      mileage: mileage ?? this.mileage,
      price: price ?? this.price,
      notes: notes ?? this.notes,
    );
  }
}

extension TransmissionOilDataExtension on TransmissionOilData {
  TransmissionOilChangeDto toDto() => TransmissionOilChangeDto(
        id: id,
        vehicleId: vehicleId,
        changeDate: changeDate,
        autoService: autoService,
        oilType: oilType,
        oilQuantity: oilQuantity,
        mileage: mileage,
        price: price,
    notes: notes,
      );
}
