import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/api/model/service_book/vehicle_tyre_dto.dart';
import 'package:sba/src/repository/general/model/tyre_brand.dart';
import 'package:sba/src/repository/service_book/model/types/vehicle_tyre_type.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class TyreData {
  const TyreData({
    required this.id,
    required this.vehicleId,
    required this.brand,
    required this.type,
    required this.model,
    required this.dot,
    required this.buyDate,
    required this.mileage,
    required this.dealer,
    required this.width,
    required this.height,
    required this.diameter,
  });

  factory TyreData.fromData({
    required VehicleTyreDto dto,
    List<TyreBrand>? brands,
  }) =>
      TyreData(
        id: dto.id,
        vehicleId: dto.vehicleId,
        brand: brands?.firstWhereOrNull((it) => it.id == dto.tyreBrandId),
        type: VehicleTyreType.values.elementAtOrNull(dto.tyreType),
        model: dto.tyreModel,
        dot: dto.tyreDot,
        buyDate: dto.buyDate,
        mileage: dto.mileage,
        dealer: dto.dealer,
        width: dto.tyreWidth,
        height: dto.tyreHeight,
        diameter: dto.tyreDiameter,
      );

  factory TyreData.fake() => TyreData(
        id: null,
        vehicleId: null,
        brand: TyreBrand(id: 0, name: BoneMock.name),
        type: VehicleTyreType.winter,
        model: BoneMock.name,
        dot: BoneMock.name,
        buyDate: DateTime.now(),
        mileage: 0,
        dealer: BoneMock.name,
        width: 0,
        height: 0,
        diameter: 0,
      );

  factory TyreData.empty() => TyreData(
        id: null,
        vehicleId: null,
        brand: null,
        type: null,
        model: '',
        dot: '',
        buyDate: DateTime.now(),
        mileage: 0,
        dealer: '',
        width: 0,
        height: 0,
        diameter: 0,
      );

  final int? id;
  final int? vehicleId;
  final TyreBrand? brand;
  final VehicleTyreType? type;
  final String model;
  final String dot;
  final DateTime buyDate;
  final int mileage;
  final String dealer;
  final double width;
  final double height;
  final double diameter;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TyreData && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  TyreData copyWith({
    int? id,
    int? vehicleId,
    TyreBrand? tyreBrand,
    VehicleTyreType? tyreType,
    String? tyreModel,
    String? tyreDot,
    DateTime? buyDate,
    int? mileage,
    String? dealer,
    double? width,
    double? height,
    double? diameter,
  }) {
    return TyreData(
      id: id ?? this.id,
      vehicleId: vehicleId ?? this.vehicleId,
      brand: tyreBrand ?? brand,
      type: tyreType ?? type,
      model: tyreModel ?? model,
      dot: tyreDot ?? dot,
      buyDate: buyDate ?? this.buyDate,
      mileage: mileage ?? this.mileage,
      dealer: dealer ?? this.dealer,
      width: width ?? this.width,
      height: height ?? this.height,
      diameter: diameter ?? this.diameter,
    );
  }
}

extension TyreDataExtension on TyreData {
  VehicleTyreDto toDto() => VehicleTyreDto(
        id: id,
        vehicleId: vehicleId,
        tyreBrandId: brand?.id ?? 0,
        tyreType: type?.index ?? 0,
        tyreModel: model,
        tyreDot: dot,
        buyDate: buyDate,
        mileage: mileage,
        dealer: dealer,
        tyreWidth: width,
        tyreHeight: height,
        tyreDiameter: diameter,
      );

  String get fullName => '${brand?.name}\n$model\n$dot';

  String get size => '$width/$height R$diameter';
}
