import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/api/model/service_book/vehicle_refuel_dto.dart';
import 'package:sba/src/repository/general/model/fuel_trader.dart';

@immutable
final class RefuelData {
  const RefuelData({
    required this.id,
    required this.vehicleId,
    required this.date,
    required this.trader,
    required this.fuel,
    required this.quantity,
    required this.mileage,
    required this.price,
    required this.notes,
  });

  factory RefuelData.fromData({
    required VehicleRefuelDto dto,
    List<FuelTrader>? traders,
  }) {
    final trader = traders?.firstWhereOrNull((e) => e.id == dto.traderId);
    final fuel = trader?.fuels.firstWhereOrNull((e) => e.id == dto.fuelId);

    return RefuelData(
      id: dto.id,
      vehicleId: dto.vehicleId,
      date: dto.date,
      trader: trader,
      fuel: fuel,
      quantity: dto.quantity,
      mileage: dto.mileage,
      price: dto.price,
      notes: dto.notes,
    );
  }

  factory RefuelData.fake() => RefuelData(
        id: null,
        vehicleId: null,
        date: DateTime.now(),
        trader: FuelTrader.fake(),
        fuel: Fuel.fake(),
        quantity: 0,
        mileage: 0,
        price: 0,
        notes: null,
      );

  factory RefuelData.empty() => RefuelData(
        id: null,
        vehicleId: null,
        date: DateTime.now(),
        trader: null,
        fuel: null,
        quantity: 0,
        mileage: 0,
        price: 0,
        notes: null,
      );

  final int? id;
  final int? vehicleId;
  final DateTime date;
  final FuelTrader? trader;
  final Fuel? fuel;
  final double quantity;
  final int mileage;
  final double price;
  final String? notes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RefuelData && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  RefuelData copyWith({
    int? id,
    int? vehicleId,
    DateTime? date,
    FuelTrader? trader,
    Fuel? fuel,
    double? quantity,
    int? mileage,
    double? price,
    String? notes,
  }) {
    return RefuelData(
      id: id ?? this.id,
      vehicleId: vehicleId ?? this.vehicleId,
      date: date ?? this.date,
      trader: trader ?? this.trader,
      fuel: fuel ?? this.fuel,
      quantity: quantity ?? this.quantity,
      mileage: mileage ?? this.mileage,
      price: price ?? this.price,
      notes: notes ?? this.notes,
    );
  }
}

extension RefuelDataExtension on RefuelData {
  double get value => quantity * price;

  VehicleRefuelDto toDto() => VehicleRefuelDto(
        id: id,
        vehicleId: vehicleId,
        date: date,
        traderId: trader?.id ?? 0,
        fuelId: fuel?.id ?? 0,
        quantity: quantity,
        mileage: mileage,
        price: price,
        value: value,
        notes: notes,
      );
}
