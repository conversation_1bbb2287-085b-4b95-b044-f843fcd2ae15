import 'package:flutter/material.dart';
import 'package:sba/src/api/model/service_book/oil_change_dto.dart';
import 'package:sba/src/repository/service_book/model/types/vehicle_filter_type.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class EngineOilData {
  const EngineOilData({
    required this.changeDate,
    required this.autoService,
    required this.oilType,
    required this.oilQuantity,
    required this.mileage,
    required this.changedFilters,
    required this.price,
    required this.notes,
    this.id,
    this.vehicleId,
  });

  factory EngineOilData.fromDto(OilChangeDto dto) => EngineOilData(
        id: dto.id,
        vehicleId: dto.vehicleId,
        changeDate: dto.changeDate,
        autoService: dto.autoService,
        oilType: dto.oilType,
        oilQuantity: dto.oilQuantity,
        mileage: dto.mileage,
        changedFilters: {
          if (dto.airFilterIsChanged) VehicleFilterType.air,
          if (dto.cabinFilterIsChanged) VehicleFilterType.cabin,
          if (dto.fuelFilterIsChanged) VehicleFilterType.fuel,
          if (dto.oilFilterIsChanged) VehicleFilterType.oil,
        },
        price: dto.price,
        notes: dto.notes,
      );

  factory EngineOilData.fake() => EngineOilData(
        changeDate: DateTime.now(),
        autoService: BoneMock.name,
        oilType: BoneMock.name,
        oilQuantity: 0,
        mileage: 0,
        changedFilters: const {},
        price: 0,
        notes: '',
      );

  factory EngineOilData.empty() => EngineOilData(
        changeDate: DateTime.now(),
        autoService: '',
        oilType: '',
        oilQuantity: 0,
        mileage: 0,
        changedFilters: const {},
        price: 0,
        notes: '',
      );

  final int? id;
  final int? vehicleId;
  final DateTime changeDate;
  final String autoService;
  final String oilType;
  final double oilQuantity;
  final int mileage;
  final Set<VehicleFilterType> changedFilters;
  final double price;
  final String? notes;

  EngineOilData copyWith({
    int? id,
    int? vehicleId,
    DateTime? changeDate,
    String? autoService,
    String? oilType,
    double? oilQuantity,
    int? mileage,
    Set<VehicleFilterType>? changedFilters,
    double? price,
    String? notes,
  }) {
    return EngineOilData(
      id: id ?? this.id,
      vehicleId: vehicleId ?? this.vehicleId,
      changeDate: changeDate ?? this.changeDate,
      autoService: autoService ?? this.autoService,
      oilType: oilType ?? this.oilType,
      oilQuantity: oilQuantity ?? this.oilQuantity,
      mileage: mileage ?? this.mileage,
      changedFilters: changedFilters ?? this.changedFilters,
      price: price ?? this.price,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EngineOilData &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

extension EngineOilDataExtension on EngineOilData {
  OilChangeDto toDto() => OilChangeDto(
        id: id,
        vehicleId: vehicleId,
        changeDate: changeDate,
        autoService: autoService,
        oilType: oilType,
        oilQuantity: oilQuantity,
        mileage: mileage,
        oilFilterIsChanged: changedFilters.contains(VehicleFilterType.oil),
        airFilterIsChanged: changedFilters.contains(VehicleFilterType.air),
        cabinFilterIsChanged: changedFilters.contains(VehicleFilterType.cabin),
        fuelFilterIsChanged: changedFilters.contains(VehicleFilterType.fuel),
        price: price,
        notes: notes,
      );
}
