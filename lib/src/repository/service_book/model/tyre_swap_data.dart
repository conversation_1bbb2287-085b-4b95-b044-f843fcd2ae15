import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/api/model/service_book/vehicle_tyre_swap_dto.dart';
import 'package:sba/src/repository/service_book/model/tyre_data.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class TyreSwapData {
  const TyreSwapData({
    required this.id,
    required this.vehicleId,
    required this.tyres,
    required this.autoService,
    required this.swapDate,
  });

  factory TyreSwapData.fromData({
    required VehicleTyreSwapDto dto,
    List<TyreData>? tyres,
  }) =>
      TyreSwapData(
        id: dto.id,
        vehicleId: dto.vehicleId,
        tyres: tyres?.firstWhereOrNull((it) => it.id == dto.tyresId),
        autoService: dto.autoservice,
        swapDate: dto.switchDate,
      );

  factory TyreSwapData.fake() => TyreSwapData(
        id: null,
        vehicleId: null,
        tyres: TyreData.fake(),
        autoService: BoneMock.name,
        swapDate: DateTime.now(),
      );

  factory TyreSwapData.empty() => TyreSwapData(
        id: null,
        vehicleId: null,
        tyres: null,
        autoService: '',
        swapDate: DateTime.now(),
      );

  final int? id;
  final int? vehicleId;
  final TyreData? tyres;
  final String autoService;
  final DateTime swapDate;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TyreSwapData &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  TyreSwapData copyWith({
    int? id,
    int? vehicleId,
    TyreData? tyres,
    String? autoService,
    DateTime? swapDate,
  }) {
    return TyreSwapData(
      id: id ?? this.id,
      vehicleId: vehicleId ?? this.vehicleId,
      tyres: tyres ?? this.tyres,
      autoService: autoService ?? this.autoService,
      swapDate: swapDate ?? this.swapDate,
    );
  }
}

extension TyreSwapDataExtension on TyreSwapData {
  VehicleTyreSwapDto toDto() => VehicleTyreSwapDto(
        id: id,
        vehicleId: vehicleId,
        tyresId: tyres?.id ?? 0,
        autoservice: autoService,
        switchDate: swapDate,
      );
}
