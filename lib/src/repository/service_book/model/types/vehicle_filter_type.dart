import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum VehicleFilterType {
  air,
  oil,
  cabin,
  fuel,
}

extension VehicleFilterTypeExtension on VehicleFilterType {
  String localizedText(BuildContext context) => switch (this) {
        VehicleFilterType.air => context.l10n.form_type_option_air_filter,
        VehicleFilterType.oil => context.l10n.form_type_option_oil_filter,
        VehicleFilterType.cabin => context.l10n.form_type_option_cabin_filter,
        VehicleFilterType.fuel => context.l10n.form_type_option_fuel_filter,
      };
}
