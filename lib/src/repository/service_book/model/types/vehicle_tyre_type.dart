import 'package:flutter/cupertino.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum VehicleTyreType {
  winter,
  summer,
  seasonal,
}

extension VehicleTyreTypeExtension on VehicleTyreType {
  Widget get icon => switch (this) {
        VehicleTyreType.winter => Assets.icons.tireWinter.svg(),
        VehicleTyreType.summer => Assets.icons.tireSummer.svg(),
        VehicleTyreType.seasonal => Assets.icons.tireSeasonal.svg(),
      };

  String localizedText(BuildContext context) => switch (this) {
        VehicleTyreType.winter => context.l10n.form_type_option_winter_tire,
        VehicleTyreType.summer => context.l10n.form_type_option_summer_tire,
        VehicleTyreType.seasonal => context.l10n.form_type_option_seasonal_tire,
      };
}
