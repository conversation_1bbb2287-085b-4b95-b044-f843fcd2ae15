import 'package:flutter/material.dart';
import 'package:sba/src/api/model/service_book/vehicle_service_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class VehicleServiceData {
  const VehicleServiceData({
    required this.serviceDate,
    required this.name,
    this.id,
    this.vehicleId,
  });

  factory VehicleServiceData.fromDto(VehicleServiceDto dto) =>
      VehicleServiceData(
        id: dto.id,
        vehicleId: dto.vehicleId,
        serviceDate: dto.serviceDate,
        name: dto.name,
      );

  factory VehicleServiceData.fake() =>
      VehicleServiceData(serviceDate: DateTime.now(), name: BoneMock.name);

  factory VehicleServiceData.empty() => VehicleServiceData(
        serviceDate: DateTime.now(),
        name: '',
      );

  final int? id;
  final int? vehicleId;
  final DateTime serviceDate;
  final String name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VehicleServiceData &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  VehicleServiceData copyWith({
    int? id,
    int? vehicleId,
    DateTime? serviceDate,
    String? name,
  }) {
    return VehicleServiceData(
      id: id ?? this.id,
      vehicleId: vehicleId ?? this.vehicleId,
      serviceDate: serviceDate ?? this.serviceDate,
      name: name ?? this.name,
    );
  }
}

extension VehicleServiceDataExtension on VehicleServiceData {
  VehicleServiceDto toDto() => VehicleServiceDto(
        id: id,
        vehicleId: vehicleId,
        serviceDate: serviceDate,
        name: name,
      );
}
