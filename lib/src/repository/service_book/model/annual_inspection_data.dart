import 'package:flutter/material.dart';
import 'package:sba/src/api/model/service_book/annual_inspection_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class AnnualInspectionData {
  const AnnualInspectionData({
    required this.id,
    required this.vehicleId,
    required this.date,
    required this.end,
    required this.durationMonths,
    required this.station,
    required this.mileage,
    required this.price,
    required this.notes,
  });

  factory AnnualInspectionData.fromDto(AnnualInspectionDto dto) =>
      AnnualInspectionData(
        id: dto.id,
        vehicleId: dto.vehicleId,
        date: dto.date,
        end: dto.end,
        durationMonths: dto.durationMonths,
        station: dto.station,
        mileage: dto.mileage,
        price: dto.price,
        notes: dto.notes,
      );

  factory AnnualInspectionData.fake() => AnnualInspectionData(
        id: 0,
        vehicleId: 0,
        date: DateTime.now(),
        end: DateTime.now(),
        durationMonths: 0,
        station: BoneMock.name,
        mileage: 0,
        price: 0,
        notes: '',
      );

  factory AnnualInspectionData.empty() => AnnualInspectionData(
        id: null,
        vehicleId: null,
        date: DateTime.now(),
        end: DateTime.now(),
        durationMonths: null,
        station: '',
        mileage: 0,
        price: 0,
        notes: '',
      );

  final int? id;
  final int? vehicleId;
  final DateTime date;
  final DateTime end;
  final int? durationMonths;
  final String station;
  final int mileage;
  final double price;
  final String? notes;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnnualInspectionData &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  AnnualInspectionData copyWith({
    int? id,
    int? vehicleId,
    DateTime? date,
    DateTime? end,
    int? durationMonths,
    String? station,
    int? mileage,
    double? price,
    String? notes,
  }) {
    return AnnualInspectionData(
      id: id ?? this.id,
      vehicleId: vehicleId ?? this.vehicleId,
      date: date ?? this.date,
      end: end ?? this.end,
      durationMonths: durationMonths ?? this.durationMonths,
      station: station ?? this.station,
      mileage: mileage ?? this.mileage,
      price: price ?? this.price,
      notes: notes ?? this.notes,
    );
  }
}

extension AnnualInspectionDataExtension on AnnualInspectionData {
  AnnualInspectionDto toDto() => AnnualInspectionDto(
        id: id,
        vehicleId: vehicleId,
        date: date,
        end: end,
        durationMonths: durationMonths,
        station: station,
        mileage: mileage,
        price: price,
        notes: notes,
      );
}
