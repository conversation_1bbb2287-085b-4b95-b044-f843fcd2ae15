import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/avi_api.dart';
import 'package:sba/src/api/model/avi/avi_booking_dto.dart';
import 'package:sba/src/common/extension/future_extension.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/avi/model/avi_booking_data.dart';
import 'package:sba/src/repository/avi/model/avi_point_data.dart';
import 'package:sba/src/repository/avi/model/book_avi_data.dart';
import 'package:sba/src/repository/avi/model/search_avi_data.dart';
import 'package:sba/src/repository/avi/model/types/avi_status.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/user_repository.dart';

const _tag = 'AVIRepository';

final class AVIRepository {
  AVIRepository({
    required AviApi api,
    required Logger logger,
    required UserRepository userRepository,
    required AuthRepository authRepository,
  }) : _api = api,
       _logger = logger,
       _userRepository = userRepository,
       _authRepository = authRepository {
    _authRepository.loggedInStream
        .doOnData((_) => _cachedResponse.add(null))
        .publish()
        .connect();

    _bookingStream =
        _cachedResponse
            .switchMap(
              (e) => e != null
                  ? Stream.value(e)
                  : _authRepository.loggedInStream
                        .where((e) => e)
                        .asyncMap((e) => _getBookings())
                        .doOnData(_cachedResponse.add),
            )
            .distinct()
            .publishReplay(maxSize: 1)
          ..connect();
  }

  final UserRepository _userRepository;
  final AuthRepository _authRepository;
  final AviApi _api;
  final Logger _logger;

  final _cachedResponse = BehaviorSubject<Result<List<AviBookingData>>?>.seeded(
    null,
  );
  late final Stream<Result<List<AviBookingData>>> _bookingStream;

  Stream<Result<List<AviBookingData>>> get bookings => _bookingStream;

  Future<void> refresh() =>
      Future.sync(() => _cachedResponse.add(null)).withMinimumDuration();

  Future<Result<List<AviBookingData>>> _getBookings() async {
    try {
      final userType = await _userRepository.type.first;

      final result = switch (userType) {
        UserType.normal => await callWithResult(_api.getBookings),
        UserType.guest => Result.success(AVIResponseDto<AVIBookingDto>.empty()),
      };

      return result.map((e) => e.items.map(AviBookingData.fromDto).toList());
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<AviPointData>>> findAVIPoint(SearchAviData data) async {
    try {
      final result = await callWithResult(
        () => _api.findLocation(
          city: data.cityName,
          date: data.date.toIso8601String(),
          vehicleCategory: data.appropriateCategory.code,
          gtp: data.gtp,
          lpg: data.newLpg,
        ),
      );

      return result.map((e) => e.items.map(AviPointData.fromDto).toList());
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> bookAVI(BookAviData data) async {
    try {
      final result = await callWithResult(
        () => _api.book(
          lineId: data.selectedSlotData.lineId,
          date: data.searchAviData.date.toIso8601String(),
          vehicleCategory: data.searchAviData.appropriateCategory.code,
          slot: data.selectedSlotData.slot,
          gtp: data.searchAviData.gtp,
          lpg: data.searchAviData.newLpg,
          plateNumber: data.plateNumber,
        ),
      );

      if (result.isSuccess) {
        final list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list?.add(AviBookingData.fromDto(result.maybeValue!.bookingDetails));
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> cancelBooking(AviBookingData data) async {
    try {
      final result = await callWithResult(
        () => _api.cancelBooking(bookingId: data.id),
      );

      if (result.isSuccess) {
        final cancelled = data.copyWith(state: AviStatus.cancelled);
        final list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list?.replaceWhere((e) => e.id == cancelled.id, cancelled);
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result;
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }
}
