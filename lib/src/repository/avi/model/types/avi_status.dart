import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';

enum AviStatus {
  active('ACTIVE'),
  rejected('CANCELED_BY_SBA'),
  cancelled('CANCELED_BY_CUSTOMER'),
  completed('COMPLETED');

  const AviStatus(this.code);

  static AviStatus? fromCode(String code) =>
      AviStatus.values.firstWhereOrNull((element) => element.code == code.toUpperCase());

  final String code;
}

extension AviStatusExtension on AviStatus {
  String formattedValue(BuildContext context) => switch (this) {
        AviStatus.active => context.l10n.mot_status_active,
        AviStatus.rejected => context.l10n.mot_status_rejected,
        AviStatus.cancelled => context.l10n.mot_status_cancelled,
        AviStatus.completed => context.l10n.mot_status_completed,
      };

  Color get color => switch (this) {
        AviStatus.active => UIColors.primary,
        AviStatus.rejected => UIColors.red,
        AviStatus.cancelled => UIColors.red,
        AviStatus.completed => UIColors.green,
      };
}
