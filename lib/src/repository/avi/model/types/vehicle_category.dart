import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum VehicleCategory {
  car(0),
  carWithLpg(1),
  truckUpTo35(2),
  truckUpTo35WithLpg(3),
  truckOver35(4),
  truckOver35WithLpg(5),
  bus(6);

  const VehicleCategory(this.code);

  static VehicleCategory fromGeneralValueAndLpg(
    VehicleCategory category,
    bool lpg,
  ) =>
      switch (category) {
        VehicleCategory.car => lpg ? VehicleCategory.carWithLpg : VehicleCategory.car,
        VehicleCategory.truckUpTo35 => lpg ? VehicleCategory.truckUpTo35WithLpg : VehicleCategory.truckUpTo35,
        VehicleCategory.truckOver35 => lpg ? VehicleCategory.truckOver35WithLpg : VehicleCategory.truckOver35,
        _ => category,
      };

  static VehicleCategory? fromCode(int code) =>
      VehicleCategory.values.firstWhereOrNull((element) => element.code == code);

  static List<VehicleCategory> get generalValues =>
      [VehicleCategory.car, VehicleCategory.truckUpTo35, VehicleCategory.truckOver35, VehicleCategory.bus];

  final int code;
}

extension VehicleTypeExtension on VehicleCategory {
  String localizedName(BuildContext context) => switch (this) {
        VehicleCategory.car => context.l10n.mot_vehicle_category_car,
        VehicleCategory.carWithLpg => context.l10n.mot_vehicle_category_car,
        VehicleCategory.truckUpTo35 => context.l10n.mot_vehicle_category_truck_under_35,
        VehicleCategory.truckUpTo35WithLpg => context.l10n.mot_vehicle_category_truck_under_35,
        VehicleCategory.truckOver35 => context.l10n.mot_vehicle_category_truck_over_35,
        VehicleCategory.truckOver35WithLpg => context.l10n.mot_vehicle_category_truck_over_35,
        VehicleCategory.bus => context.l10n.mot_vehicle_category_bus,
      };

  bool get hasLpgOption => this != VehicleCategory.bus;
}
