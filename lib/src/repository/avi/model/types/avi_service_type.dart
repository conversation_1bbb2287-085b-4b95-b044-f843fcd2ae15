import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum AVIServiceType {
  gtp(0),
  lpg(1);

  const AVIServiceType(this.code);

  final int code;

  static AVIServiceType? fromCode(int code) =>
      AVIServiceType.values.firstWhereOrNull((element) => element.code == code);
}

extension AviServiceTypeExtension on AVIServiceType {
  String localizedName(BuildContext context) => switch (this) {
        AVIServiceType.gtp => context.l10n.mot_service_type_inspection,
        AVIServiceType.lpg => context.l10n.mot_service_type_lpg,
      };
}
