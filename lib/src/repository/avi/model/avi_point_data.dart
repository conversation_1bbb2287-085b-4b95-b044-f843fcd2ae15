import 'package:flutter/cupertino.dart';
import 'package:latlong2/latlong.dart';
import 'package:sba/src/api/model/avi/avi_point_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class AviPointData {
  const AviPointData({
    required this.id,
    required this.name,
    required this.city,
    required this.address,
    required this.coordinates,
    required this.freeCalendarSlots,
    required this.price,
  });

  factory AviPointData.fromDto(AVIPointDto dto) => AviPointData(
        id: dto.id,
        name: dto.nameBg,
        city: dto.cityBg,
        address: dto.addressBg,
        coordinates: LatLng(dto.lat, dto.lng),
        freeCalendarSlots: dto.freeCalendarSlots?.map(AviPointSlotData.fromDto).toList(),
        price: dto.pricing?.gtpPrice,
      );

  factory AviPointData.fake() => AviPointData(
        id: 1,
        name: BoneMock.name,
        city: BoneMock.city,
        address: BoneMock.address,
        coordinates: const LatLng(0, 0),
        freeCalendarSlots: null,
        price: null,
      );

  final int id;
  final String? name;
  final String? city;
  final String? address;
  final LatLng coordinates;
  final List<AviPointSlotData>? freeCalendarSlots;
  final double? price;
}

@immutable
final class AviPointSlotData {
  const AviPointSlotData({required this.lineId, required this.slot});

  factory AviPointSlotData.fromDto(({int lineId, String slot}) dto) => AviPointSlotData(
        lineId: dto.lineId,
        slot: dto.slot,
      );

  final int lineId;
  final String slot;
}
