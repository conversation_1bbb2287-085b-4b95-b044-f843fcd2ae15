import 'package:sba/src/repository/avi/model/types/vehicle_category.dart';
import 'package:sba/src/repository/general/model/place.dart';

final class SearchAviData {
  SearchAviData({
    required this.city,
    required this.date,
    required this.category,
    required this.hasLpg,
    required this.gtp,
    required this.newLpg,
  });

  final Place city;
  final DateTime date;
  final VehicleCategory category;
  final bool hasLpg;
  final bool gtp;
  final bool newLpg;
}

extension SearchAviDataExtension on SearchAviData {
  VehicleCategory get appropriateCategory =>
      VehicleCategory.fromGeneralValueAndLpg(category, hasLpg);

  String get cityName => city.extractedName;
}
