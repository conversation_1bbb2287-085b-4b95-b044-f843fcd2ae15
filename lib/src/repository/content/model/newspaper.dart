import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/common/newspaper_dto.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:skeletonizer/skeletonizer.dart';

final class Newspaper {
  Newspaper({
    required this.id,
    required this.title,
    required this.year,
    required this.issue,
    required this.uri,
  });

  factory Newspaper.fromDto(NewspaperDto dto) => Newspaper(
        id: dto.id,
        title: dto.title,
        year: dto.year,
        issue: dto.issue,
        uri: dto.url,
      );

  factory Newspaper.fake() =>
      Newspaper(id: 0, title: BoneMock.title, year: 2025, issue: 123, uri: '');

  final int id;
  final String? title;
  final int year;
  final int issue;
  final String uri;
}

extension NewspaperExtension on Newspaper {
  String localizedTitle(BuildContext context) =>
      '${title ?? context.l10n.home_newspaper_tile}\n$issue';
}
