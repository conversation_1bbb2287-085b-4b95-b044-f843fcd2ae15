import 'package:sba/src/api/model/common/info_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

final class Info {
  Info({required this.title, required this.content});

  factory Info.fromDto(InfoDto dto) =>
      Info(title: dto.title, content: dto.content);

  factory Info.fake() =>
      Info(title: BoneMock.title, content: BoneMock.paragraph);

  final String title;
  final String content;
}
