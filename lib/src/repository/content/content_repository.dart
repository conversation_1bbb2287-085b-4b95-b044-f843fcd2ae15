import 'package:logger/logger.dart';
import 'package:sba/src/api/content_api.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/content/model/info.dart';
import 'package:sba/src/repository/content/model/newspaper.dart';

const _tag = 'ContentRepository';

final class ContentRepository {
  ContentRepository({required Logger logger, required ContentApi api})
      : _logger = logger,
        _api = api;

  final Logger _logger;
  final ContentApi _api;

  Future<Result<Newspaper>> getNewspaper() async {
    try {
      final result = await callWithCachedResult(_api.getNewspaper);

      return result.map(Newspaper.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<Info>>> getInfo() async {
    try {
      final result = await callWithCachedResult(_api.getInfo);

      return result.listMap(Info.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
