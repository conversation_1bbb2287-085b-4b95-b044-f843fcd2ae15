import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/toll/toll_product_dto.dart';


@immutable
final class TollProduct {
  const TollProduct({
    required this.id,
    required this.description,
    required this.vehicleType,
    required this.validityType,
    required this.price,
  });

  factory TollProduct.fromDto(TollProductDto dto) => TollProduct(
        id: dto.id,
        description: dto.description,
        vehicleType: dto.vehicleType,
        validityType: dto.validityType,
        price: dto.price,
      );

  final int id;
  final String description;
  final String vehicleType;
  final String validityType;
  final double price;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TollProduct &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
