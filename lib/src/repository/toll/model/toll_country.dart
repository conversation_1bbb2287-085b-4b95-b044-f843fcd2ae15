import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/toll/toll_country_dto.dart';

@immutable
final class TollCountry {
  const TollCountry({
    required this.id,
    required this.code,
    required this.name,
  });

  factory TollCountry.fromDto(TollCountryDto dto) => TollCountry(
        id: dto.id,
        code: dto.code,
        name: dto.name,
      );

  final int id;
  final String code;
  final String name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TollCountry &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
