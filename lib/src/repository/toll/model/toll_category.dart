import 'package:flutter/material.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum TollCategory {
  car('car'),
  trailer('trailer');

  const TollCategory(this.key);

  final String key;
}

extension TollCategoryExtension on TollCategory {
  String localizedName(BuildContext context) => switch (this) {
        TollCategory.car => context.l10n.form_type_option_car,
        TollCategory.trailer => context.l10n.form_type_option_trailer
      };

  Widget get icon => switch (this) {
        TollCategory.car => Assets.icons.directionsCar.svg(),
        TollCategory.trailer => Assets.icons.rvHookup.svg()
      };
}
