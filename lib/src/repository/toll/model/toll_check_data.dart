import 'package:sba/src/api/model/toll/toll_check_dto.dart';

final class TollCheckData {
  TollCheckData({
    required this.id,
    required this.type,
    required this.start,
    required this.end,
    required this.amount,
    required this.currency,
    required this.valid,
  });

  factory TollCheckData.fromDto(
    TollCheckDto? dto,
  ) =>
      TollCheckData(
        id: dto?.code ?? '',
        type: dto?.vehicleClass ?? '',
        start: dto?.validityStartFormatted ?? '',
        end: dto?.validityEndFormatted ?? '',
        amount: double.tryParse(dto?.price ?? '') ?? 0,
        currency: dto?.currency ?? '',
        valid: dto?.valid ?? false,
      );

  final String id;
  final String type;
  final String start;
  final String end;
  final double amount;
  final String currency;
  final bool valid;
}
