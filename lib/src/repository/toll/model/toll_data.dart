import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/toll/toll_check_dto.dart';
import 'package:sba/src/api/model/toll/toll_dto.dart';
import 'package:sba/src/api/model/toll/toll_register_dto.dart';
import 'package:sba/src/api/model/toll/toll_sale_row_dto.dart';
import 'package:sba/src/api/toll_api.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/toll/model/toll_status.dart';
import 'package:sba/src/repository/user/model/user_data.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class TollData {
  const TollData({
    required this.id,
    required this.userId,
    required this.customer,
    required this.eik,
    required this.email,
    required this.phone,
    required this.date,
    required this.countryCode,
    required this.vehicleType,
    required this.plateNumber,
    required this.from,
    required this.to,
    required this.price,
    required this.state,
    required this.vignetteId,
    required this.issued,
    required this.registerJson,
    required this.activateJson,
    required this.checkJson,
  });

  factory TollData.fromDto(TollDto dto) => TollData(
        id: dto.id,
        userId: dto.userId,
        customer: dto.customer,
        eik: dto.eik,
        email: dto.email,
        phone: dto.phone,
        date: dto.date,
        countryCode: dto.countryCode,
        vehicleType: dto.vehicleType,
        plateNumber: dto.plateNumber,
        from: dto.from,
        to: dto.to,
        price: dto.price,
        state: TollStatus.fromCode(dto.state ?? 0),
        vignetteId: dto.vignetteId,
        issued: dto.issued,
        registerJson: dto.registerJson,
        activateJson: dto.activateJson,
        checkJson: dto.checkJson,
      );

  factory TollData.fromCheckData(
    UserData user,
    TollCountry country,
    String plateNumber,
    TollResponseDto<TollCheckDto> data,
  ) {
    final checkData = data.data;

    return TollData(
      id: null,
      userId: user.id,
      customer: user.fullName,
      eik: '',
      email: user.email,
      phone: user.phone,
      date: DateTime.now(),
      countryCode: country.code,
      vehicleType: checkData.productId,
      plateNumber: plateNumber,
      from: checkData.validityStartDate,
      to: checkData.validityEndDate,
      price: double.tryParse(checkData.price ?? '') ?? 0,
      state: checkData.valid ? TollStatus.active : TollStatus.draft,
      vignetteId: checkData.code ?? '',
      issued: null,
      registerJson: null,
      activateJson: null,
      checkJson: data.json,
    );
  }

  factory TollData.fromRegisterData(
    UserData user,
    TollResponseDto<TollRegisterDto> data,
  ) {
    final registerData = data.data;
    final sale = registerData.saleRows.first;

    return TollData(
      id: null,
      userId: user.id,
      customer: user.fullName,
      eik: '',
      email: user.email,
      phone: user.phone,
      date: registerData.createdOn!,
      countryCode: sale.kapschProperties!.vehicle!.countryCode!,
      vehicleType: sale.kapschProperties!.product!.id,
      plateNumber: sale.kapschProperties!.vehicle!.lpn!,
      from: sale.kapschProperties?.validity?.validityStartDateTimeUTC ??
          DateTime.now(),
      to: sale.kapschProperties?.validity?.validityEndDateTimeUTC ??
          DateTime.now(),
      price: registerData.total!,
      state: TollStatus.registered,
      vignetteId: sale.kapschProperties!.id!,
      issued: sale.kapschProperties?.purchase?.purchaseDateTimeUTC,
      registerJson: data.json,
      activateJson: null,
      checkJson: null,
    );
  }

  factory TollData.fromActivateData(
    TollData data,
    TollResponseDto<TollSaleRowDto> dto,
  ) {
    final activateData = dto.data;

    return TollData(
      id: data.id,
      userId: data.userId,
      customer: data.customer,
      eik: data.eik,
      email: data.email,
      phone: data.phone,
      date: data.date,
      countryCode: activateData.kapschProperties!.vehicle!.countryCode!,
      vehicleType: activateData.kapschProperties!.product!.id,
      plateNumber: activateData.kapschProperties!.vehicle!.lpn!,
      from: activateData.kapschProperties?.validity?.validityStartDateTimeUTC ??
          DateTime(0),
      to: activateData.kapschProperties?.validity?.validityEndDateTimeUTC ??
          DateTime(0),
      price: data.price,
      state: TollStatus.active,
      vignetteId: data.vignetteId,
      issued: activateData.kapschProperties?.purchase?.purchaseDateTimeUTC,
      registerJson: data.registerJson,
      activateJson: dto.json,
      checkJson: data.checkJson,
    );
  }

  factory TollData.fake() => TollData(
        id: null,
        userId: 0,
        customer: BoneMock.name,
        eik: null,
        email: BoneMock.email,
        phone: BoneMock.phone,
        date: DateTime.now(),
        countryCode: '',
        vehicleType: 0,
        plateNumber: '',
        from: DateTime.now(),
        to: DateTime.now(),
        price: 0,
        state: TollStatus.draft,
        vignetteId: '',
        issued: null,
        registerJson: null,
        activateJson: null,
        checkJson: null,
      );

  final int? id;
  final int userId;
  final String customer;
  final String? eik;
  final String? email;
  final String? phone;
  final DateTime date;
  final String countryCode;
  final int vehicleType;
  final String plateNumber;
  final DateTime from;
  final DateTime to;
  final double price;
  final TollStatus state;
  final String vignetteId;
  final DateTime? issued;
  final Map<String, dynamic>? registerJson;
  final Map<String, dynamic>? activateJson;
  final Map<String, dynamic>? checkJson;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TollData && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  TollData copyWith({
    int? id,
    int? userId,
    String? eik,
    String? customer,
    String? email,
    String? phone,
    DateTime? date,
    String? countryCode,
    int? vehicleType,
    String? plateNumber,
    DateTime? from,
    DateTime? to,
    double? price,
    TollStatus? state,
    String? vignetteId,
    DateTime? issued,
    Map<String, dynamic>? registerJson,
    Map<String, dynamic>? activateJson,
    Map<String, dynamic>? checkJson,
  }) {
    return TollData(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      customer: customer ?? this.customer,
      eik: eik ?? this.eik,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      date: date ?? this.date,
      countryCode: countryCode ?? this.countryCode,
      vehicleType: vehicleType ?? this.vehicleType,
      plateNumber: plateNumber ?? this.plateNumber,
      from: from ?? this.from,
      to: to ?? this.to,
      price: price ?? this.price,
      state: state ?? this.state,
      vignetteId: vignetteId ?? this.vignetteId,
      issued: issued ?? this.issued,
      registerJson: registerJson ?? this.registerJson,
      activateJson: activateJson ?? this.activateJson,
      checkJson: checkJson ?? this.checkJson,
    );
  }
}

extension TollDataExtension on TollData {
  TollDto toDto() => TollDto(
        id: id,
        userId: userId,
        customer: customer,
        eik: eik,
        email: email,
        phone: phone,
        date: date,
        countryCode: countryCode,
        vehicleType: vehicleType,
        plateNumber: plateNumber,
        from: from,
        to: to,
        price: price,
        state: state.code,
        vignetteId: vignetteId,
        issued: issued,
        registerJson: registerJson,
        activateJson: activateJson,
        checkJson: checkJson,
      );

  bool get isValid {
    final now = DateTime.now();
    return state == TollStatus.active &&
        now.isAfter(from.toLocal()) &&
        now.isBefore(to.toLocal());
  }
}
