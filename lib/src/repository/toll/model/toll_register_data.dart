import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/request/register_toll_request_dto.dart';
import 'package:sba/src/repository/toll/model/toll_category.dart';
import 'package:sba/src/repository/toll/model/toll_country.dart';
import 'package:sba/src/repository/toll/model/toll_product.dart';

@immutable
final class TollRegisterData {
  const TollRegisterData({
    this.selectedCategory,
    this.selectedProduct,
    this.country,
    this.plateNumber,
    this.startDate,
    this.email,
  });

  factory TollRegisterData.empty() => const TollRegisterData();

  final TollCategory? selectedCategory;
  final TollProduct? selectedProduct;
  final TollCountry? country;
  final String? plateNumber;
  final DateTime? startDate;
  final String? email;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TollRegisterData &&
          runtimeType == other.runtimeType &&
          selectedCategory == other.selectedCategory &&
          selectedProduct == other.selectedProduct &&
          country == other.country &&
          plateNumber == other.plateNumber &&
          startDate == other.startDate &&
          email == other.email;

  @override
  int get hashCode =>
      selectedCategory.hashCode ^
      selectedProduct.hashCode ^
      country.hashCode ^
      plateNumber.hashCode ^
      startDate.hashCode ^
      email.hashCode;

  TollRegisterData changeCategory(
    TollCategory? selectedCategory,
  ) {
    return TollRegisterData(
      selectedCategory: selectedCategory,
      country: country,
      plateNumber: plateNumber,
      startDate: startDate,
      email: email,
    );
  }

  TollRegisterData copyWith({
    TollCategory? selectedCategory,
    TollProduct? selectedProduct,
    TollCountry? country,
    String? plateNumber,
    DateTime? startDate,
    String? email,
  }) {
    return TollRegisterData(
      selectedCategory: selectedCategory ?? this.selectedCategory,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      country: country ?? this.country,
      plateNumber: plateNumber ?? this.plateNumber,
      startDate: startDate ?? this.startDate,
      email: email ?? this.email,
    );
  }
}

extension TollRegisterDataExtension on TollRegisterData {
  RegisterTollRequestDto toDto() => RegisterTollRequestDto(
        activationDate: startDate!,
        email: email,
        productId: selectedProduct!.id,
        vehicle: (
          countryCode: country!.code,
          lpn: plateNumber!,
        ),
      );
}
