import 'package:latlong2/latlong.dart';
import 'package:sba/src/api/model/common/training_center_dto.dart';

final class TrainingCenter {
  TrainingCenter({
    required this.id,
    required this.coordinates,
    required this.name,
    required this.city,
    required this.address,
    required this.phone,
    required this.mobile,
    required this.email,
    required this.workTime,
  });

  factory TrainingCenter.fromDto(TrainingCenterDto dto) => TrainingCenter(
        id: dto.id,
        coordinates: LatLng(dto.latitude, dto.longitude),
        name: dto.name,
        city: dto.city,
        address: dto.address,
        phone: dto.phone,
        mobile: dto.mobile,
        email: dto.email,
        workTime: List.unmodifiable([
          dto.wtMonday,
          dto.wtTuesday,
          dto.wtWednesday,
          dto.wtThursday,
          dto.wtFriday,
          dto.wtSaturday,
          dto.wtSunday,
        ]),
      );

  final int id;
  final LatLng coordinates;
  final String? name;
  final String? city;
  final String? address;
  final String? phone;
  final String? mobile;
  final String? email;
  final List<String?> workTime;

  String get fullAddress =>
      [city, address].where((it) => it?.isNotEmpty ?? false).join(', ');
}
