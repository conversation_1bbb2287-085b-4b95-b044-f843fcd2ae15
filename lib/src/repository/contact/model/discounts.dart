import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/common/discounts_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class DiscountCategory {
  const DiscountCategory({
    required this.id,
    required this.name,
    required this.items,
  });

  factory DiscountCategory.fromDto(DiscountCategoryDto dto) => DiscountCategory(
        id: dto.id,
        name: dto.name,
        items: dto.discounts.map(DiscountItem.fromDto).toList(),
      );

  factory DiscountCategory.fake() => DiscountCategory(
        id: 0,
        name: BoneMock.name,
        items: const [],
      );

  final int id;
  final String name;
  final List<DiscountItem> items;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DiscountCategory &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

final class DiscountItem {
  DiscountItem({
    required this.rank,
    required this.name,
    required this.announcement,
    required this.title,
    required this.content,
    required this.image,
  });

  factory DiscountItem.fromDto(DiscountItemDto dto) => DiscountItem(
        rank: dto.rank,
        name: dto.name,
        announcement: dto.announcement,
        title: dto.title,
        content: dto.content,
        image: dto.image,
      );

  factory DiscountItem.fake() => DiscountItem(
        rank: 0,
        name: BoneMock.name,
        announcement: BoneMock.words(3),
        title: BoneMock.title,
        content: BoneMock.words(4),
        image: null,
      );

  final int rank;
  final String name;
  final String? announcement;
  final String? title;
  final String? content;
  final String? image;
}
