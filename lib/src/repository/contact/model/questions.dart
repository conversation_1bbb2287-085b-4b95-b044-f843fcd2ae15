import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/common/questions_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class QuestionGroup {
  const QuestionGroup({
    required this.id,
    required this.name,
    required this.questions,
  });

  factory QuestionGroup.fromDto(QuestionGroupDto dto) =>
      QuestionGroup(
        id: dto.id,
        name: dto.name,
        questions: dto.questions.map(Question.fromDto).toList(),
      );

  factory QuestionGroup.fake() =>
      QuestionGroup(
        id: 0,
        name: BoneMock.name,
        questions: List.filled(2, Question.fake()),
      );

  final int id;
  final String name;
  final List<Question> questions;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is QuestionGroup &&
              runtimeType == other.runtimeType &&
              id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@immutable
final class Question {
  const Question({
    required this.id,
    required this.question,
    required this.answer,
  });

  factory Question.fromDto(QuestionItemDto dto) =>
      Question(
        id: dto.id,
        question: dto.question,
        answer: dto.answer,
      );

  factory Question.fake() =>
      Question(
        id: Random().nextInt(1000),
        question: BoneMock.title,
        answer: BoneMock.words(5),
      );

  final int id;
  final String question;
  final String answer;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is Question && runtimeType == other.runtimeType &&
              id == other.id;

  @override
  int get hashCode => id.hashCode;
}
