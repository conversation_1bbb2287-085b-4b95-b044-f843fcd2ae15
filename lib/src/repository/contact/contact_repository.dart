import 'package:logger/logger.dart';
import 'package:sba/src/api/contacts_api.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/contact/model/discounts.dart';
import 'package:sba/src/repository/contact/model/questions.dart';
import 'package:sba/src/repository/contact/model/training_center.dart';

const _tag = 'ContactRepository';

final class ContactRepository {
  ContactRepository({required Logger logger, required ContactsApi api})
      : _logger = logger,
        _api = api;

  final Logger _logger;
  final ContactsApi _api;

  Future<Result<List<TrainingCenter>>> getTrainingCenters() async {
    try {
      final result = await callWithCachedResult(_api.getTrainingCenters);

      return result.listMap(TrainingCenter.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<QuestionGroup>>> getFAQ() async {
    try {
      final result = await callWithCachedResult(_api.getFAQ);

      return result.listMap(QuestionGroup.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<DiscountCategory>>> getDiscounts() async {
    try {
      final result = await callWithCachedResult(_api.getDiscounts);

      return result.listMap(DiscountCategory.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> sendLegalQuestion(String question) async {
    try {
      final result =
          await callWithResult(() => _api.sendLegalCase(question: question));

      return result;
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
