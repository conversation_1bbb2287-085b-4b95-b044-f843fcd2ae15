import 'package:collection/collection.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/model/vehicle/vehicle_dto.dart';
import 'package:sba/src/api/vehicle_api.dart';
import 'package:sba/src/common/extension/future_extension.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';

const _tag = 'VehicleRepository';

final class VehicleRepository {
  VehicleRepository({
    required GeneralRepository generalRepository,
    required AuthRepository authRepository,
    required UserRepository userRepository,
    required VehicleApi api,
    required Logger logger,
  }) : _generalRepository = generalRepository,
       _authRepository = authRepository,
       _userRepository = userRepository,
       _api = api,
       _logger = logger {
    _authRepository.loggedInStream
        .doOnData((_) => _cachedResponse.add(null))
        .publish()
        .connect();

    _vehiclesStream =
        _cachedResponse
            .switchMap(
              (e) => e != null
                  ? Stream.value(e)
                  : _authRepository.loggedInStream
                        .where((e) => e)
                        .asyncMap((e) => _getVehicles())
                        .doOnData(_cachedResponse.add),
            )
            .distinct()
            .publishReplay(maxSize: 1)
          ..connect();
  }

  final GeneralRepository _generalRepository;
  final AuthRepository _authRepository;
  final UserRepository _userRepository;
  final VehicleApi _api;
  final Logger _logger;
  final _cachedResponse = BehaviorSubject<Result<List<VehicleData>>?>.seeded(
    null,
  );
  late final Stream<Result<List<VehicleData>>> _vehiclesStream;

  Stream<Result<List<VehicleData>>> get vehicles => _vehiclesStream;

  Future<void> refresh() =>
      Future.sync(() => _cachedResponse.add(null)).withMinimumDuration();

  Stream<int> get vehicleCount =>
      vehicles.map((e) => e.maybeValue?.length ?? 0);

  Future<VehicleData?> getVehicle(int id) => _vehiclesStream
      .map((e) => e.maybeValue)
      .map((e) => e?.firstWhereOrNull((i) => i.id == id))
      .first;

  Future<List<VehicleData>?> getVehicles() =>
      _vehiclesStream.map((e) => e.maybeValue).first;

  Future<VehicleData?> getVehicleByPlateNumber(String plateNumber) =>
      _vehiclesStream
          .map((e) => e.maybeValue)
          .map(
            (e) => e?.firstWhereOrNull(
              (i) => i.plateNumber.toUpperCase() == plateNumber,
            ),
          )
          .first;

  Future<Result<List<VehicleData>>> _getVehicles() async {
    try {
      final userType = await _userRepository.type.first;

      final result = switch (userType) {
        UserType.normal => await callWithResult(_api.getVehicles),
        UserType.guest => Result.success(List<VehicleDto>.empty()),
      };

      final brands = result.maybeValue?.isNotEmpty ?? false
          ? await _generalRepository.getCarBrands().then((e) => e.maybeValue)
          : null;
      final places = result.maybeValue?.isNotEmpty ?? false
          ? await _generalRepository.getPlaces().then((e) => e.maybeValue)
          : null;

      final mapped = result.listMap(
        (data) => VehicleData.fromData(
          dto: data,
          brands: brands,
          places: places,
        ),
        growable: true,
      );

      return mapped;
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> createVehicle(VehicleData data) async {
    try {
      final result = await callWithResult(
        () => _api.addVehicle(data: data.toDto()),
      );

      if (result.isSuccess) {
        final newVehicle = data.copyWith(id: result.maybeValue?.id);
        final list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list?.add(newVehicle);
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> updateVehicle(VehicleData data) async {
    try {
      final result = await callWithResult(
        () => _api.updateVehicle(data: data.toDto()),
      );

      if (result.isSuccess) {
        final list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list?.replaceWhere((e) => e.id == data.id, data);
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> deleteVehicle(VehicleData data) async {
    try {
      final result = await callWithResult(
        () => _api.deleteVehicle(id: data.id ?? 0),
      );

      if (result.isSuccess) {
        final list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list?.removeWhere((e) => e.id == data.id);
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
