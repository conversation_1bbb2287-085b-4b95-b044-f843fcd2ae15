import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/vehicle/vehicle_dto.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class VehicleData {
  const VehicleData({
    required this.brand,
    required this.model,
    required this.plateNumber,
    required this.city,
    required this.address,
    this.id,
  });

  factory VehicleData.fromData({
    required VehicleDto dto,
    List<CarBrand>? brands,
    List<Place>? places,
  }) {
    final brand = brands?.firstWhereOrNull((it) => it.id == dto.makerId);
    final model = brand?.models.firstWhereOrNull((it) => it.id == dto.modelId);
    final place = places?.firstWhereOrNull((it) => it.id == dto.city);

    return VehicleData(
      id: dto.id,
      brand: brand,
      model: model,
      plateNumber: dto.plateNumber,
      city: place,
      address: dto.address,
    );
  }

  factory VehicleData.fake() => VehicleData(
        brand: null,
        model: null,
        plateNumber: BoneMock.name,
        city: null,
        address: BoneMock.address,
      );

  factory VehicleData.empty() => const VehicleData(
        brand: null,
        model: null,
        plateNumber: '',
        city: null,
        address: '',
      );

  final int? id;
  final CarBrand? brand;
  final CarModel? model;
  final String plateNumber;
  final Place? city;
  final String address;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VehicleData &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  VehicleData copyWith({
    int? id,
    CarBrand? brand,
    CarModel? model,
    String? plateNumber,
    Place? city,
    String? address,
  }) {
    return VehicleData(
      id: id ?? this.id,
      brand: brand ?? this.brand,
      model: model ?? this.model,
      plateNumber: plateNumber ?? this.plateNumber,
      city: city ?? this.city,
      address: address ?? this.address,
    );
  }
}

extension VehicleDataExtension on VehicleData {
  VehicleDto toDto() => VehicleDto(
        id: id,
        makerId: brand?.id ?? 0,
        modelId: model?.id ?? 0,
        plateNumber: plateNumber,
        city: city?.id ?? 0,
        address: address,
      );

  String? get imageUri => null;

  String get brandName => brand?.name ?? '';

  String get modelName => model?.name ?? '';
}
