import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/notification/notification_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class NotificationData {
  const NotificationData({
    required this.id,
    required this.title,
    required this.text,
    required this.created,
    required this.seen,
  });

  factory NotificationData.fromDto(NotificationDto dto) => NotificationData(
        id: dto.id,
        title: dto.title,
        text: dto.text,
        created: dto.created,
        seen: dto.seen,
      );

  factory NotificationData.fake() => NotificationData(
        id: 0,
        title: BoneMock.title,
        text: BoneMock.paragraph,
        created: DateTime.now(),
        seen: false,
      );

  final int id;
  final String title;
  final String text;
  final DateTime created;
  final bool seen;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationData &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  NotificationData copyWith({
    int? id,
    String? title,
    String? text,
    DateTime? created,
    bool? seen,
  }) {
    return NotificationData(
      id: id ?? this.id,
      title: title ?? this.title,
      text: text ?? this.text,
      created: created ?? this.created,
      seen: seen ?? this.seen,
    );
  }
}
