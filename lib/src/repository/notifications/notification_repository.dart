import 'dart:async';
import 'dart:ui';

import 'package:get_it/get_it.dart';
import 'package:logger/logger.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/model/notification/notification_dto.dart';
import 'package:sba/src/api/notification_api.dart';
import 'package:sba/src/common/extension/future_extension.dart';
import 'package:sba/src/common/lifecycle_observer/lifecycle_observer.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/notifications/model/notification_data.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/user_repository.dart';

const _tag = 'NotificationRepository';

final class NotificationRepository implements Disposable {
  NotificationRepository({
    required Logger logger,
    required UserRepository userRepository,
    required AuthRepository authRepository,
    required NotificationApi notificationApi,
    required LifecycleObserver lifecycleObserver,
  }) : _logger = logger,
       _userRepository = userRepository,
       _authRepository = authRepository,
       _notificationApi = notificationApi,
       _lifecycleObserver = lifecycleObserver {
    _authRepository.loggedInStream
        .doOnData((_) => _cachedResponse.add(null))
        .publish()
        .connect();

    _notificationStream =
        _cachedResponse
            .switchMap(
              (e) => e != null
                  ? Stream.value(e)
                  : _authRepository.loggedInStream
                        .where((e) => e)
                        .asyncMap((e) => _syncNotifications())
                        .doOnData(_cachedResponse.add),
            )
            .distinct()
            .publishReplay(maxSize: 1)
          ..connect();

    _userRepository.user
        .whereNotNull()
        .switchMap(
          (user) =>
              _userRepository.type.map((type) => (user: user, type: type)),
        )
        .map(
          (data) => data.type == UserType.guest
              ? 'guest_${data.user.id}'
              : '${data.user.id}',
        )
        .distinct()
        .asyncMap(OneSignal.login)
        .publish()
        .connect();

    Rx.merge([
          _osNotificationTrigger
              .distinct((a, b) => a.notificationId == b.notificationId)
              .map((_) => true),
          _lifecycleObserver.lifecycleStream
              .where((e) => e == AppLifecycleState.resumed)
              .map((_) => true),
        ])
        .switchMap((e) => _authRepository.loggedInStream.where((e) => e))
        .asyncMap((e) => _syncNotifications())
        .doOnData(_cachedResponse.add)
        .publish()
        .connect();

    OneSignal.Notifications.addForegroundWillDisplayListener(
      _onNotificationEvent,
    );
  }

  final Logger _logger;
  final AuthRepository _authRepository;
  final UserRepository _userRepository;
  final NotificationApi _notificationApi;

  final LifecycleObserver _lifecycleObserver;

  final _cachedResponse =
      BehaviorSubject<Result<List<NotificationData>>?>.seeded(null);
  final _osNotificationTrigger = PublishSubject<OSNotification>();
  late final Stream<Result<List<NotificationData>>> _notificationStream;

  Stream<Result<List<NotificationData>>> get notifications =>
      _notificationStream;

  Stream<int> get unreadCount => notifications
      .map((e) => e.maybeValue ?? List.empty())
      .map((e) => e.where((e) => !e.seen).length);

  Future<void> refresh() =>
      Future.sync(() => _cachedResponse.add(null)).withMinimumDuration();

  Future<void> requestPermissions() async {
    try {
      await OneSignal.Notifications.requestPermission(false);
    } catch (e) {
      _logger.e('$_tag: $e');
    }
  }

  Future<Result<void>> seeAllNotifications() async {
    try {
      final unread = await unreadCount.first;

      if (unread == 0) {
        return Result.success(null);
      }

      final result = await callWithResult(_notificationApi.seeAllNotifications);

      if (result.isSuccess) {
        final list = _cachedResponse.valueOrNull?.maybeValue;
        final update = list?.map((e) => e.copyWith(seen: true)).toList();
        _cachedResponse.add(Result.success(update ?? List.empty()));
      }

      return result;
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<NotificationData>>> _syncNotifications() async {
    try {
      final type = await _userRepository.type.first;

      final result = switch (type) {
        UserType.normal => await callWithResult(
          _notificationApi.getNotifications,
        ),
        UserType.guest => Result.success(List<NotificationDto>.empty()),
      };

      return result.listMap(NotificationData.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  void _onNotificationEvent(OSNotificationWillDisplayEvent event) {
    _osNotificationTrigger.add(event.notification);
  }

  @override
  FutureOr<void> onDispose() {
    OneSignal.Notifications.removeForegroundWillDisplayListener(
      _onNotificationEvent,
    );
  }
}
