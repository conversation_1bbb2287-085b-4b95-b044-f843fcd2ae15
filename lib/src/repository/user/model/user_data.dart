import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/user_dto.dart';
import 'package:sba/src/common/extension/string_extension.dart';
import 'package:sba/src/repository/user/model/type/notification_type.dart';

@immutable
final class UserData {
  const UserData({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.emailNotifications,
    required this.phone,
    required this.phoneNotifications,
    required this.city,
    required this.address,
    required this.category,
    required this.companyName,
    required this.companyEIK,
    required this.member,
  });

  factory UserData.fromDto(UserDto dto) => UserData(
        id: dto.id,
        firstName: dto.firstName,
        lastName: dto.lastName,
        email: dto.email,
        emailNotifications: {
          if (dto.emailInfo) NotificationType.informative,
          if (dto.emailPromo) NotificationType.marketing,
        },
        phone: dto.phone,
        phoneNotifications: {
          if (dto.phoneInfo) NotificationType.informative,
          if (dto.phonePromo) NotificationType.marketing,
        },
        city: dto.city,
        address: dto.address,
        category: dto.category,
        companyName: dto.companyName,
        companyEIK: dto.companyEIK,
        member: dto.member != null ? UserData.fromDto(dto.member!) : null,
      );

  final int id;
  final String? firstName;
  final String? lastName;
  final String? email;
  final Set<NotificationType> emailNotifications;
  final String? phone;
  final Set<NotificationType> phoneNotifications;
  final int? city;
  final String? address;
  final int? category;
  final String? companyName;
  final String? companyEIK;
  final UserData? member;

  UserData copyWith({
    int? id,
    String? firstName,
    String? lastName,
    String? email,
    Set<NotificationType>? emailNotifications,
    String? phone,
    Set<NotificationType>? phoneNotifications,
    int? city,
    String? address,
    int? category,
    String? companyName,
    String? companyEIK,
    UserData? member,
  }) {
    return UserData(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      phone: phone ?? this.phone,
      phoneNotifications: phoneNotifications ?? this.phoneNotifications,
      city: city ?? this.city,
      address: address ?? this.address,
      category: category ?? this.category,
      companyName: companyName ?? this.companyName,
      companyEIK: companyEIK ?? this.companyEIK,
      member: member ?? this.member,
    );
  }
}

extension UserDataExtension on UserData {
  String? get unsafeFullName {
    if (firstName?.isEmpty ?? true) {
      return null;
    }

    if (lastName?.isEmpty ?? true) {
      return firstName;
    }

    return '$firstName $lastName';
  }

  String get fullName {
    var name = safeName;

    if (lastName?.isNotEmpty ?? false) name += ' $lastName';

    return name;
  }

  String get safeName => firstName.isNullOrEmpty ? 'Потребител' : firstName!;

  bool get isMember => member != null;
}
