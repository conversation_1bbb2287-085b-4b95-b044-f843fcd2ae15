import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum NotificationType {
  informative,
  marketing,
}

extension NotificationTypeExtension on NotificationType {
  String localizedName(BuildContext context) => switch (this) {
        NotificationType.informative =>
          context.l10n.form_notification_option_info,
        NotificationType.marketing =>
          context.l10n.form_notification_option_promo,
      };
}
