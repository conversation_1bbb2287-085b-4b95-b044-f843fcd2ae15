import 'package:logger/logger.dart';
import 'package:rx_shared_preferences/rx_shared_preferences.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/model/request/change_address_dto.dart';
import 'package:sba/src/api/model/request/change_consent_dto.dart';
import 'package:sba/src/api/user_api.dart';
import 'package:sba/src/api/users_api.dart';
import 'package:sba/src/common/extension/future_extension.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/user/model/type/notification_type.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/model/user_data.dart';

const _tag = 'UserRepository';
const _typeKey = 'type';

final class UserRepository {
  UserRepository({
    required Logger logger,
    required RxSharedPreferences preferences,
    required AuthRepository authRepository,
    required UserApi userApi,
    required UsersApi usersApi,
  }) : _logger = logger,
       _preferences = preferences,
       _authRepository = authRepository,
       _userApi = userApi,
       _usersApi = usersApi {
    _typeStream =
        _preferences
            .getIntStream(_typeKey)
            .whereNotNull()
            .map(UserType.fromInt)
            .onErrorReturn(UserType.guest)
            .publishReplay(maxSize: 1)
          ..connect();

    _authRepository.loggedInStream
        .doOnData((_) => _cachedResponse.add(null))
        .publish()
        .connect();

    _userStream =
        _cachedResponse
            .switchMap(
              (e) => e != null
                  ? Stream.value(e)
                  : _authRepository.authTokenStream
                        .whereNotNull()
                        .distinct()
                        .asyncMap((e) => _syncUser())
                        .doOnData(_cachedResponse.add),
            )
            .distinct()
            .publishReplay(maxSize: 1)
          ..connect();
  }

  final Logger _logger;

  final RxSharedPreferences _preferences;

  final AuthRepository _authRepository;

  final UserApi _userApi;

  final UsersApi _usersApi;

  final _cachedResponse = BehaviorSubject<Result<UserData?>?>.seeded(null);

  late final Stream<UserType> _typeStream;

  late final Stream<Result<UserData?>> _userStream;

  Stream<UserType> get type => _typeStream;

  Stream<UserData?> get user => _userStream.map((e) => e.maybeValue);

  Future<UserData?> getUser() => user.first;

  Future<void> refresh() =>
      Future.sync(() => _cachedResponse.add(null)).withMinimumDuration();

  Future<Result<UserData?>> _syncUser() async {
    try {
      final result = await callWithResult(_usersApi.getCurrentUser);

      return result.mapNullable(
        (data) => data == null ? null : UserData.fromDto(data),
      );
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<void> updateType(UserType type) async {
    try {
      await _preferences.setInt(_typeKey, type.index);
    } catch (e) {
      _logger.e('$_tag: $e');
    }
  }

  Future<Result<void>> updateAddress({
    required Place place,
    required String address,
  }) async {
    try {
      final data = await user.first;
      final token = await _authRepository.authToken;

      final result = await callWithResult(
        () => _userApi.changeAddress(
          dto: ChangeAddressDto(
            userId: data?.id ?? 0,
            sessionId: token?.sessionId ?? '',
            city: place.id,
            address: address,
          ),
        ),
      );

      if (result.isSuccess) {
        final updated = data?.copyWith(city: place.id, address: address);
        _cachedResponse.add(Result.success(updated));
      }

      return result;
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> updateConsent({
    required Set<NotificationType> emailConsent,
    required Set<NotificationType> mobileConsent,
  }) async {
    try {
      final data = await user.first;
      final token = await _authRepository.authToken;

      final emailResult = await callWithResult(
        () => _userApi.changeEmailConsent(
          dto: ChangeConsentDto(
            userId: data?.id ?? 0,
            sessionId: token?.sessionId ?? '',
            emailInfo: emailConsent.contains(NotificationType.informative),
            emailPromo: emailConsent.contains(NotificationType.marketing),
          ),
        ),
      );

      final mobileResult = await callWithResult(
        () => _userApi.changeMobileConsent(
          dto: ChangeConsentDto(
            userId: data?.id ?? 0,
            sessionId: token?.sessionId ?? '',
            phoneInfo: mobileConsent.contains(NotificationType.informative),
            phonePromo: mobileConsent.contains(NotificationType.marketing),
          ),
        ),
      );

      var updated = data;
      if (emailResult.isSuccess) {
        updated = updated?.copyWith(emailNotifications: emailConsent);
      }

      if (mobileResult.isSuccess) {
        updated = updated?.copyWith(phoneNotifications: mobileConsent);
      }

      _cachedResponse.add(Result.success(updated));

      return emailResult.combine(mobileResult);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
