import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/parking/parking_zone_dto.dart';

final class ParkingZone {
  ParkingZone({required this.id, required this.city, required this.zones});

  factory ParkingZone.fromDto(ParkingZoneDto dto) => ParkingZone(
        id: dto.id,
        city: dto.city,
        zones: dto.zones
            .map(
              (e) => Zone(
                id: e.id,
                name: e.name,
                phone: e.phone,
              ),
            )
            .toList(),
      );

  final int id;
  final String city;
  final List<Zone> zones;
}

@immutable
final class Zone {
  const Zone({required this.id, required this.name, required this.phone});

  final int id;
  final String name;
  final String phone;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Zone && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
