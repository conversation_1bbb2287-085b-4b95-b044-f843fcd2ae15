import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/parking/parking_request_dto.dart';
import 'package:sba/src/repository/parking/model/parking_zone.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class ParkingRequest {
  const ParkingRequest({
    required this.id,
    required this.place,
    required this.zone,
    required this.plateNumber,
    required this.durationHours,
    required this.date,
  });

  factory ParkingRequest.fromData({
    required ParkingRequestDto dto,
    List<ParkingZone>? zones,
  }) {
    final place = zones?.firstWhereOrNull((it) => it.id == dto.placeId);
    final zone = place?.zones.firstWhereOrNull((it) => it.id == dto.zoneId);

    return ParkingRequest(
      id: dto.id,
      place: place,
      zone: zone,
      plateNumber: dto.dkn,
      durationHours: dto.durationHours,
      date: dto.dateTime,
    );
  }

  factory ParkingRequest.empty() => const ParkingRequest(
        id: null,
        place: null,
        zone: null,
        plateNumber: '',
        durationHours: 0,
        date: null,
      );

  factory ParkingRequest.fake() => ParkingRequest(
        id: null,
        place: ParkingZone(id: 0, city: BoneMock.name, zones: []),
        zone: Zone(id: 0, name: BoneMock.name, phone: ''),
        plateNumber: BoneMock.name,
        durationHours: 1,
        date: DateTime.now(),
      );

  final int? id;
  final ParkingZone? place;
  final Zone? zone;
  final String plateNumber;
  final int durationHours;
  final DateTime? date;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ParkingRequest &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  ParkingRequest copyWith({
    int? id,
    ParkingZone? place,
    Zone? zone,
    String? plateNumber,
    int? durationHours,
    DateTime? date,
  }) {
    return ParkingRequest(
      id: id ?? this.id,
      place: place ?? this.place,
      zone: zone ?? this.zone,
      plateNumber: plateNumber ?? this.plateNumber,
      durationHours: durationHours ?? this.durationHours,
      date: date ?? this.date,
    );
  }
}

extension ParkingRequestExtension on ParkingRequest {
  ParkingRequestDto toDto() => ParkingRequestDto(
        id: id,
        placeId: place?.id ?? 0,
        zoneId: zone?.id ?? 0,
        dkn: plateNumber,
        durationHours: durationHours,
        dateTime: date,
      );

  DateTime? get paidUntil => date?.add( Duration(hours: durationHours));

  bool get isValid => paidUntil?.isAfter(DateTime.now()) ?? false;
}
