import 'package:collection/collection.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/parking_api.dart';
import 'package:sba/src/common/extension/future_extension.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/parking/model/parking_request.dart';
import 'package:sba/src/repository/parking/model/parking_zone.dart';

const _tag = 'ParkingRepository';

final class ParkingRepository {
  ParkingRepository({
    required Logger logger,
    required AuthRepository authRepository,
    required ParkingApi api,
  }) : _logger = logger,
       _authRepository = authRepository,
       _api = api {
    _authRepository.loggedInStream
        .doOnData((_) => _cachedResponse.add(null))
        .publish()
        .connect();

    _requestStream =
        _cachedResponse
            .switchMap(
              (e) => e != null
                  ? Stream.value(e)
                  : _authRepository.loggedInStream
                        .where((e) => e)
                        .asyncMap((e) => _getParkingRequest())
                        .doOnData(_cachedResponse.add),
            )
            .distinct()
            .publishReplay(maxSize: 1)
          ..connect();
  }

  final Logger _logger;
  final AuthRepository _authRepository;
  final ParkingApi _api;
  final _cachedResponse = BehaviorSubject<Result<List<ParkingRequest>>?>.seeded(
    null,
  );
  late final Stream<Result<List<ParkingRequest>>> _requestStream;

  Stream<Result<List<ParkingRequest>>> get requestsStream => _requestStream;

  Future<void> refresh() =>
      Future.sync(() => _cachedResponse.add(null)).withMinimumDuration();

  Future<Result<List<ParkingZone>>> getParkingZones() async {
    try {
      final response = await callWithCachedResult(_api.getParkingZones);

      return response.listMap(ParkingZone.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<ParkingRequest>>> _getParkingRequest() async {
    try {
      final result = await callWithResult(_api.getParkingRequests);
      final zones = result.maybeValue?.isNotEmpty ?? false
          ? await getParkingZones().then((e) => e.maybeValue)
          : null;

      return result.listMap(
        (e) => ParkingRequest.fromData(dto: e, zones: zones),
      );
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> createParkingRequest(ParkingRequest data) async {
    try {
      final valid = _cachedResponse.valueOrNull?.maybeValue?.firstWhereOrNull(
        (e) =>
            e.plateNumber == data.plateNumber &&
            e.zone == data.zone &&
            e.isValid,
      );

      final updated = valid?.copyWith(durationHours: valid.durationHours + 1);

      final result = await callWithResult(
        () => updated == null
            ? _api.createParkingRequest(data: data.toDto())
            : _api.updateParkingRequest(
                data: updated.toDto(),
              ),
      );

      if (result.isSuccess) {
        final toAdd =
            updated ??
            data.copyWith(
              id: result.maybeValue?.id,
              date: result.maybeValue?.dateTime,
            );
        var list = _cachedResponse.valueOrNull?.maybeValue?.asCopy();
        list = list?.replaceWhereOrAdd((e) => e == toAdd, toAdd);
        _cachedResponse.add(Result.success(list ?? List.empty()));
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
