import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum SubscriptionDeliveryType {
  none(0),
  address(1),
  office(2),
  wallet(9),
  easybox(10);

  const SubscriptionDeliveryType(this.code);

  static SubscriptionDeliveryType? fromCode(int code) =>
      SubscriptionDeliveryType.values.firstWhereOrNull((e) => e.code == code);

  final int code;
}

extension SubscriptionDeliveryTypeExtension on SubscriptionDeliveryType {
  String localizedName(BuildContext context) => switch (this) {
        SubscriptionDeliveryType.none => 'none',
        SubscriptionDeliveryType.address =>
          context.l10n.form_type_option_delivery_address,
        SubscriptionDeliveryType.office =>
          context.l10n.form_type_option_delivery_speedy,
        SubscriptionDeliveryType.wallet => 'wallet',
        SubscriptionDeliveryType.easybox => 'easybox',
      };
}
