import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_category.dart';
import 'package:sba/src/ui/theme/theme.dart';

enum SubscriptionType {
  silver(2),
  silverPlus(4),
  gold(1),
  platinum(3);

  const SubscriptionType(this.code);

  static SubscriptionType? fromServerCode(int code) =>
      SubscriptionType.values.firstWhereOrNull((e) => e.code == code);

  final int code;
}

extension SubscriptionTypeExtension on SubscriptionType {
  String localizedName(BuildContext context) => switch (this) {
        SubscriptionType.silver => context.l10n.subscription_type_silver,
        SubscriptionType.silverPlus =>
          context.l10n.subscription_type_silver_plus,
        SubscriptionType.gold => context.l10n.subscription_type_gold,
        SubscriptionType.platinum => context.l10n.subscription_type_platinum,
      };

  Color get color => switch (this) {
        SubscriptionType.silver => UIColors.subscriptionSilver,
        SubscriptionType.silverPlus => UIColors.subscriptionSilverPlus,
        SubscriptionType.gold => UIColors.subscriptionGold,
        SubscriptionType.platinum => UIColors.subscriptionPlatinum,
  };

  AssetGenImage image(SubscriptionCategory category) => switch (category) {
        SubscriptionCategory.membership => switch (this) {
            SubscriptionType.silver => Assets.images.memberSilverCard,
            SubscriptionType.silverPlus =>
              Assets.images.memberSilverPlusCard,
            SubscriptionType.gold => Assets.images.memberGoldCard,
            SubscriptionType.platinum =>
              Assets.images.memberPlatinumCard,
          },
        SubscriptionCategory.subscription => switch (this) {
            SubscriptionType.silver =>
              Assets.images.subscriptionSilverCard,
            SubscriptionType.silverPlus =>
              Assets.images.subscriptionSilverPlusCard,
            SubscriptionType.gold => Assets.images.subscriptionGoldCard,
            SubscriptionType.platinum =>
              Assets.images.subscriptionPlatinumCard,
          },
      };


}
