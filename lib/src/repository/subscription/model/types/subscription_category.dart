import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum SubscriptionCategory {
  membership(0),
  subscription(1);

  const SubscriptionCategory(this.code);

  static SubscriptionCategory? fromServerCode(int code) =>
      SubscriptionCategory.values.firstWhereOrNull((e) => e.code == code);

  final int code;
}

extension SubscriptionCategoryExtension on SubscriptionCategory {
  String localizedName(BuildContext context) => switch (this) {
        SubscriptionCategory.membership =>
          context.l10n.subscription_type_member,
        SubscriptionCategory.subscription =>
          context.l10n.subscription_type_subscription,
      };
}
