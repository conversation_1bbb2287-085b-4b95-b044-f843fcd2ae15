import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/theme.dart';

enum SubscriptionOrderStatus {
  requested(1),
  completed(5);

  const SubscriptionOrderStatus(this.code);

  static SubscriptionOrderStatus? fromCode(int code) =>
      SubscriptionOrderStatus.values.firstWhereOrNull((e) => e.code == code);

  final int code;
}

extension SubscriptionOrderStatusExtension on SubscriptionOrderStatus {
  String localizedName(BuildContext context) => switch (this) {
        SubscriptionOrderStatus.requested =>
            context.l10n.subscription_request_status_requested,
        SubscriptionOrderStatus.completed =>
            context.l10n.subscription_request_status_completed,
      };

  Color get color => switch (this) {
        SubscriptionOrderStatus.requested => UIColors.primary,
        SubscriptionOrderStatus.completed => UIColors.green,
      };
}
