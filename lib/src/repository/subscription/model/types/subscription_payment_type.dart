import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum SubscriptionPaymentType {
  card(1),
  delivery(2),
  bank(3);

  const SubscriptionPaymentType(this.code);

  static SubscriptionPaymentType? fromCode(int code) =>
      SubscriptionPaymentType.values.firstWhereOrNull((e) => e.code == code);

  final int code;
}

extension SubscriptionPaymentTypeExtension on SubscriptionPaymentType {
  String localizedName(BuildContext context) => switch (this) {
        SubscriptionPaymentType.delivery =>
          context.l10n.form_type_option_payment_delivery,
        SubscriptionPaymentType.card =>
          context.l10n.form_type_option_payment_card,
        SubscriptionPaymentType.bank =>
          context.l10n.form_type_option_payment_bank,
      };
}
