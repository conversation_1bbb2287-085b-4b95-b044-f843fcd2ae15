import 'package:collection/collection.dart';
import 'package:sba/src/api/model/subscription/subscription_product_dto.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_category.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_type.dart';
import 'package:skeletonizer/skeletonizer.dart';

final class SubscriptionProductData {
  SubscriptionProductData({
    required this.id,
    required this.category,
    required this.type,
    required this.price,
    required this.packages,
    required this.coverage,
  });

  factory SubscriptionProductData.fromDto(SubscriptionProductDto dto) =>
      SubscriptionProductData(
        id: dto.id,
        category: SubscriptionCategory.fromServerCode(dto.category),
        type: SubscriptionType.fromServerCode(dto.type),
        price: dto.total,
        packages: dto.packages
            .map(
              (e) => SubscriptionPackage(
                id: e.id,
                name: e.name,
                total: e.total,
                promo: e.promo,
              ),
            )
            .toList(),
        coverage: dto.coverages
            .map(
              (e) => SubscriptionCoverage(
                id: e.id,
                coverage: e.coverage,
                coverageText: e.coverageText,
              ),
            )
            .toList(),
      );

  factory SubscriptionProductData.fake() => SubscriptionProductData(
        id: 1,
        category: SubscriptionCategory.membership,
        type: SubscriptionType.silver,
        price: 100,
        packages: List.filled(1, SubscriptionPackage.fake()),
        coverage: List.filled(5, SubscriptionCoverage.fake()),
      );

  final int id;
  final SubscriptionCategory? category;
  final SubscriptionType? type;
  final double price;
  final List<SubscriptionPackage> packages;
  final List<SubscriptionCoverage> coverage;
}

final class SubscriptionPackage {
  SubscriptionPackage({
    required this.id,
    required this.name,
    required this.total,
    required this.promo,
  });

  factory SubscriptionPackage.fake() => SubscriptionPackage(
        id: 1,
        name: BoneMock.name,
        total: 100,
        promo: true,
      );

  final int id;
  final String name;
  final double total;
  final bool promo;
}

final class SubscriptionCoverage {
  SubscriptionCoverage({
    required this.id,
    required this.coverage,
    required this.coverageText,
  });

  factory SubscriptionCoverage.fake() => SubscriptionCoverage(
        id: 1,
        coverage: BoneMock.title,
        coverageText: BoneMock.name,
      );

  final int id;
  final String coverage;
  final String coverageText;
}

extension SubscriptionDataExtension on SubscriptionProductData {
  SubscriptionPackage? packageWith(bool promo) =>
      packages.firstWhereOrNull((e) => e.promo == promo);
}
