import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/subscription/subscription_dto.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_category.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_type.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';

@immutable
final class SubscriptionData {
  const SubscriptionData({
    required this.id,
    required this.cardNumber,
    required this.memberId,
    required this.vehicle,
    required this.dateFrom,
    required this.dateTo,
    required this.category,
    required this.type,
  });

  factory SubscriptionData.fromData(
    SubscriptionDto dto,
    List<VehicleData>? vehicles,
  ) =>
      SubscriptionData(
        id: dto.id,
        cardNumber: dto.cardNumber,
        memberId: dto.memberId,
        vehicle: vehicles?.firstWhereOrNull((e) => e.id == dto.carId),
        dateFrom: dto.dateFrom,
        dateTo: dto.dateTo,
        category: SubscriptionCategory.fromServerCode(dto.cardCategory),
        type: SubscriptionType.fromServerCode(dto.cardType),
      );

  factory SubscriptionData.fake() => SubscriptionData(
        id: 0,
        cardNumber: '123123',
        memberId: 0,
        vehicle: null,
        dateFrom: DateTime.now(),
        dateTo: DateTime.now(),
        category: SubscriptionCategory.membership,
        type: SubscriptionType.silver,
      );

  final int id;
  final String cardNumber;
  final int memberId;
  final VehicleData? vehicle;
  final DateTime dateFrom;
  final DateTime dateTo;
  final SubscriptionCategory? category;
  final SubscriptionType? type;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubscriptionData &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
