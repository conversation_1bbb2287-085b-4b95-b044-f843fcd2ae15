import 'package:collection/collection.dart';
import 'package:logger/logger.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/cards_api.dart';
import 'package:sba/src/api/model/subscription/subscription_dto.dart';
import 'package:sba/src/api/model/subscription/subscription_order_request_dto.dart';
import 'package:sba/src/common/extension/future_extension.dart';
import 'package:sba/src/common/extension/list_extension.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/payment/model/payment_status.dart';
import 'package:sba/src/payment/payment_processor.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/repository/general/general_repository.dart';
import 'package:sba/src/repository/subscription/model/subscription_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_product_data.dart';
import 'package:sba/src/repository/subscription/model/subscription_request_data.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_order_status.dart';
import 'package:sba/src/repository/subscription/model/types/subscription_payment_type.dart';
import 'package:sba/src/repository/user/model/type/user_type.dart';
import 'package:sba/src/repository/user/user_repository.dart';
import 'package:sba/src/repository/vehicle/model/vehicle_data.dart';
import 'package:sba/src/repository/vehicle/vehicle_repository.dart';

const _tag = 'SubscriptionRepository';

final class SubscriptionRepository {
  SubscriptionRepository({
    required CardsApi api,
    required Logger logger,
    required UserRepository userRepository,
    required AuthRepository authRepository,
    required VehicleRepository vehicleRepository,
    required GeneralRepository generalRepository,
    required PaymentProcessor paymentProcessor,
  }) : _api = api,
       _logger = logger,
       _userRepository = userRepository,
       _authRepository = authRepository,
       _vehicleRepository = vehicleRepository,
       _generalRepository = generalRepository,
       _paymentProcessor = paymentProcessor {
    _authRepository.loggedInStream
        .doOnData((_) => _cachedResponse.add(null))
        .doOnData((_) => _cachedOrderResponse.add(null))
        .publish()
        .connect();

    _subscriptionsStream =
        _cachedResponse
            .switchMap(
              (e) => e != null
                  ? Stream.value(e)
                  : _authRepository.loggedInStream
                        .where((e) => e)
                        .asyncMap((e) => _getUserSubscriptions())
                        .doOnData(_cachedResponse.add),
            )
            .distinct()
            .publishReplay(maxSize: 1)
          ..connect();

    _orderStream =
        _cachedOrderResponse
            .switchMap(
              (e) => e != null
                  ? Stream.value(e)
                  : _authRepository.loggedInStream
                        .where((e) => e)
                        .asyncMap((e) => _getUserSubscriptionRequests())
                        .doOnData(_cachedOrderResponse.add),
            )
            .distinct()
            .publishReplay(maxSize: 1)
          ..connect();
  }

  final CardsApi _api;
  final Logger _logger;
  final UserRepository _userRepository;
  final AuthRepository _authRepository;
  final VehicleRepository _vehicleRepository;
  final GeneralRepository _generalRepository;
  final PaymentProcessor _paymentProcessor;

  final _cachedResponse =
      BehaviorSubject<Result<List<SubscriptionData>>?>.seeded(null);
  late final Stream<Result<List<SubscriptionData>>> _subscriptionsStream;

  final _cachedOrderResponse =
      BehaviorSubject<Result<List<SubscriptionRequestData>>?>.seeded(null);
  late final Stream<Result<List<SubscriptionRequestData>>> _orderStream;

  Stream<Result<List<SubscriptionData>>> get subscriptions =>
      _subscriptionsStream;

  Stream<Result<List<SubscriptionRequestData>>> get requests => _orderStream;

  Future<void> refresh() => Future.sync(() {
    _cachedResponse.add(null);
    _cachedOrderResponse.add(null);
  }).withMinimumDuration();

  Future<SubscriptionData?> subscriptionForVehicle(VehicleData vehicle) =>
      _subscriptionsStream
          .map((e) => e.maybeValue)
          .map((e) => e?.firstWhereOrNull((i) => i.vehicle == vehicle))
          .first;

  Future<Result<SubscriptionData?>> getSubscription(int id) =>
      _subscriptionsStream
          .map(
            (e) => e.map(
              (e) => e.firstWhereOrNull((i) => i.id == id),
            ),
          )
          .first;

  Future<Result<List<SubscriptionProductData>>>
  getSubscriptionProducts() async {
    try {
      final result = await callWithCachedResult(_api.getSubscriptionProducts);

      return result.listMap(SubscriptionProductData.fromDto);
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<SubscriptionData>>> _getUserSubscriptions() async {
    try {
      final userType = await _userRepository.type.first;

      final result = switch (userType) {
        UserType.normal => await callWithResult(_api.getSubscriptions),
        UserType.guest => Result.success(List<SubscriptionDto>.empty()),
      };

      final vehicles = await _vehicleRepository.getVehicles();

      return result.listMap((d) => SubscriptionData.fromData(d, vehicles));
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<SubscriptionRequestData>>>
  _getUserSubscriptionRequests() async {
    try {
      final userType = await _userRepository.type.first;

      final result = switch (userType) {
        UserType.normal => await callWithResult(_api.getOrders),
        UserType.guest => Result.success(
          List<SubscriptionOrderRequestDto>.empty(),
        ),
      };

      final vehicles = await _vehicleRepository.getVehicles();
      final products = await getSubscriptionProducts().then(
        (e) => e.maybeValue,
      );
      final places = await _generalRepository.getPlaces().then(
        (e) => e.maybeValue,
      );

      return result.listMap(
        (d) => SubscriptionRequestData.fromData(
          dto: d,
          vehicles: vehicles,
          products: products,
          places: places,
        ),
      );
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<SubscriptionRequestData>> createOrderRequest(
    SubscriptionRequestData data,
  ) async {
    try {
      final result = await callWithResult(
        () => _api.createOrderRequest(
          request: data.toDto(),
        ),
      );

      if (result.isFailure) {
        return (result as Failure).transform();
      }

      final newData = data.copyWith(
        id: result.maybeValue?.id,
        status: result.maybeValue?.status == null
            ? null
            : SubscriptionOrderStatus.fromCode(result.maybeValue!.status!),
      );
      final list = _cachedOrderResponse.valueOrNull?.maybeValue?.asCopy();
      list?.add(newData);
      _cachedOrderResponse.add(Result.success(list ?? List.empty()));

      return Result.success(newData);
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }

  Future<Result<PaymentStatus>> paySubscriptionRequest(
    SubscriptionRequestData data,
  ) async {
    try {
      if (data.paymentType != SubscriptionPaymentType.card) {
        return Result.canceled();
      }

      if (data.paymentStatus == PaymentStatus.authorized) {
        return Result.success(data.paymentStatus!);
      }

      final result = await _paymentProcessor.makeSubscriptionPayment(data.id!);

      if (result.isFailure) {
        return (result as Failure).transform();
      }

      final status = result.maybeValue?.paymentStatus;

      final list = _cachedOrderResponse.valueOrNull?.maybeValue?.asCopy();
      list?.replaceWhere(
        (e) => e.id == data.id,
        data.copyWith(
          paymentStatus: status,
        ),
      );
      _cachedOrderResponse.add(Result.success(list ?? List.empty()));

      return result.map((e) => status ?? PaymentStatus.unknown);
    } catch (e) {
      _logger.e('$_tag:$e');
      return Result.otherError(e);
    }
  }
}
