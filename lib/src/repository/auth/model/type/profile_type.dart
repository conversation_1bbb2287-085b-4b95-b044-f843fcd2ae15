import 'package:flutter/material.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum ProfileType { personal, company }

extension ProfileTypeExtension on ProfileType {
  String localizedName(BuildContext context) => switch (this) {
        ProfileType.personal => context.l10n.form_type_option_person,
        ProfileType.company => context.l10n.form_type_option_company,
      };
}
