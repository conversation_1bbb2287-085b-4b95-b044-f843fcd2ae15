import 'dart:core';
import 'package:sba/src/api/model/request/register_dto.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/user/model/type/notification_type.dart';

final class RegisterData {
  RegisterData({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.emailNotifications,
    required this.phone,
    required this.phoneNotifications,
    required this.address,
    required this.password,
    required this.city,
  });

  final String firstName;
  final String lastName;
  final String email;
  final Set<NotificationType> emailNotifications;
  final String phone;
  final Set<NotificationType> phoneNotifications;
  final Place city;
  final String address;
  final String password;
}

extension RegisterDataExtension on RegisterData {
  RegisterDto toDto() => RegisterDto(
        firstName: firstName,
        lastName: lastName,
        email: email,
        emailInfo: emailNotifications.contains(NotificationType.informative),
        emailPromo: emailNotifications.contains(NotificationType.marketing),
        phone: phone,
        phoneInfo: phoneNotifications.contains(NotificationType.informative),
        phonePromo: phoneNotifications.contains(NotificationType.marketing),
        city: city.id,
        address: address,
        password: password,
        confirmPassword: password,
      );
}
