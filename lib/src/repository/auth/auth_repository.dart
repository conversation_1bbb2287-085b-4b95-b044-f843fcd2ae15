import 'dart:convert';

import 'package:logger/logger.dart';
import 'package:rx_shared_preferences/rx_shared_preferences.dart';
import 'package:rxdart/rxdart.dart';
import 'package:sba/src/api/account_api.dart';
import 'package:sba/src/api/model/request/change_password_dto.dart';
import 'package:sba/src/api/model/token_dto.dart';
import 'package:sba/src/api/token_api.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/auth/model/login_data.dart';
import 'package:sba/src/repository/auth/model/register_data.dart';

const _tag = 'AuthRepository';
const _tokenKey = 'auth';
const _guestKey = 'key';

const _rememberMeKey = 'remember_me';

final class AuthRepository {
  AuthRepository(
    this._logger,
    this._accountApi,
    this._tokenApi,
    this._preferences,
  ) {
    _authTokenStream = _preferences
        .observe<TokenDto>(
          _tokenKey,
          (dynamic data) => data == null
              ? null
              : TokenDto.fromJson(
                  jsonDecode(data as String) as Map<String, dynamic>,
                ),
        )
        .mergeWith([_memoryToken])
        .distinct()
        .publishReplay(maxSize: 1)
      ..connect();
  }

  final Logger _logger;
  final AccountApi _accountApi;
  final TokenApi _tokenApi;
  final RxSharedPreferences _preferences;
  final PublishSubject<TokenDto?> _memoryToken = PublishSubject();

  late final Stream<TokenDto?> _authTokenStream;

  Stream<TokenDto?> get authTokenStream => _authTokenStream;

  Stream<bool> get loggedInStream => authTokenStream
      .map((e) => e != null)
      .distinct()
      .delay(const Duration(milliseconds: 50));

  Future<TokenDto?> get authToken => authTokenStream.first;

  Future<Result<void>> login({required LoginData data}) async {
    try {
      await logout();

      final result = await callWithResult(
        () => _accountApi.login(email: data.email, password: data.password),
      );

      if (result.isSuccess) {
        await _preferences.setBool(_rememberMeKey, data.rememberMe);
      }

      await _updateTokens(result.maybeValue);

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> guestLogin() async {
    try {
      await logout();

      final prev = await _preferences.getInt(_guestKey);

      final result = await callWithResult(
        () => _accountApi.guestLogin(guestUserId: prev),
      );

      await _updateTokens(result.maybeValue?.token);

      if (result.isSuccess) {
        await _preferences.setInt(_guestKey, result.maybeValue?.userId);
      }

      return result.toVoid();
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> logout() async {
    try {
      await _preferences.remove(_rememberMeKey);
      await _updateTokens(null);

      return Result.success(null);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<TokenDto>> refreshTokens() async {
    final savedToken = await authToken;

    if (savedToken == null) {
      return Result.canceled();
    }

    final result = await callWithResult(
      () => _tokenApi.refresh(
        accessToken: savedToken.accessToken,
        refreshToken: savedToken.refreshToken,
      ),
    );

    await _updateTokens(result.maybeValue);
    return result;
  }

  Future<Result<void>> forgottenPassword({required String email}) async {
    try {
      return callWithResult(() => _accountApi.lostPassword(email: email));
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<bool> checkEmail({required String email}) async {
    try {
      final result =
          await callWithResult(() => _accountApi.checkUsername(email: email));
      return result.isFailure;
    } catch (e) {
      _logger.e('$_tag: $e');
      return true;
    }
  }

  Future<Result<void>> register({required RegisterData data}) async {
    try {
      return callWithResult(() => _accountApi.register(data: data.toDto()));
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> resendActivation({required String email}) async {
    try {
      return callWithResult(() => _accountApi.sendActivationMail(email: email));
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<void>> updatePassword({
    required String email,
    required String oldPassword,
    required String newPassword,
  }) async {
    try {
      final result = await callWithResult(
        () => _accountApi.changePassword(
          dto: ChangePasswordDto(
            username: email,
            currentPassword: oldPassword,
            newPassword: newPassword,
            passwordAgain: newPassword,
          ),
        ),
      );

      return result;
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<void> _updateTokens(TokenDto? token) async {
    final remember = await _preferences.getBool(_rememberMeKey) ?? false;

    _memoryToken.add(token);

    if (remember || token == null) {
      await _preferences.write(
        _tokenKey,
        token,
        (d) => d == null ? null : jsonEncode(d.toJson()),
      );
    }
  }
}
