import 'package:logger/logger.dart';
import 'package:sba/src/api/general_api.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/general/model/car_brand.dart';
import 'package:sba/src/repository/general/model/fuel_trader.dart';
import 'package:sba/src/repository/general/model/fuel_type.dart';
import 'package:sba/src/repository/general/model/place.dart';
import 'package:sba/src/repository/general/model/road_assistance_reason.dart';
import 'package:sba/src/repository/general/model/types/tyre_size_type.dart';
import 'package:sba/src/repository/general/model/tyre_brand.dart';
import 'package:sba/src/repository/general/model/tyre_size.dart';

const _tag = 'ContactRepository';

final class GeneralRepository {
  GeneralRepository({required Logger logger, required GeneralApi api})
      : _logger = logger,
        _api = api;

  final Logger _logger;
  final GeneralApi _api;

  Future<Result<List<CarBrand>>> getCarBrands() async {
    try {
      final result = await callWithCachedResult(_api.getCarBrands);

      return result.listMap(CarBrand.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<Place>>> getPlaces() async {
    try {
      final result = await callWithCachedResult(_api.getPlaces);

      return result.listMap(Place.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<FuelType>>> getFuelTypes() async {
    try {
      final result = await callWithCachedResult(_api.getFuelTypes);

      return result.listMap(FuelType.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<TyreBrand>>> getTyres() async {
    try {
      final result = await callWithCachedResult(_api.getTyreBrands);

      return result.listMap(TyreBrand.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<TyreSize>>> getTyreWidths() async {
    try {
      final result = await callWithCachedResult(
        () => _api.getTyreSizes(type: TyreSizeType.width.code),
        cacheKey: 'tyreWidths',
      );

      return result.listMap(TyreSize.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<TyreSize>>> getTyreHeights() async {
    try {
      final result = await callWithCachedResult(
        () => _api.getTyreSizes(type: TyreSizeType.height.code),
        cacheKey: 'tyreHeights',
      );

      return result.listMap(TyreSize.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<TyreSize>>> getTyreDiameters() async {
    try {
      final result = await callWithCachedResult(
        () => _api.getTyreSizes(type: TyreSizeType.diameter.code),
        cacheKey: 'tyreDiameters',
      );

      return result.listMap(TyreSize.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<FuelTrader>>> getFuels() async {
    try {
      final result = await callWithCachedResult(_api.getFuelTraders);

      return result.listMap(FuelTrader.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }

  Future<Result<List<RoadAssistanceReason>>> getRoadAssistanceReasons() async {
    try {
      final result = await callWithCachedResult(_api.getRoadAssistanceReasons);

      return result.listMap(RoadAssistanceReason.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
