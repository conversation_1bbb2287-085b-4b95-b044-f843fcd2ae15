import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/road_assistance/road_assistance_reason_dto.dart';

@immutable
final class RoadAssistanceReason {
  const RoadAssistanceReason({
    required this.id,
    required this.reason,
  });

  factory RoadAssistanceReason.fromDto(RoadAssistanceReasonDto dto) =>
      RoadAssistanceReason(
        id: dto.id,
        reason: dto.reasonBg,
      );

  final int id;
  final String reason;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RoadAssistanceReason &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
