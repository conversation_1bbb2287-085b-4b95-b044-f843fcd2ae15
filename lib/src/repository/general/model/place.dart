import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/common/place_dto.dart';

@immutable
final class Place {
  const Place({
    required this.id,
    required this.name,
    required this.district,
    required this.postCode,
  });

  factory Place.fromDto(PlaceDto dto) => Place(
        id: dto.id,
        name: dto.name,
        district: dto.district,
        postCode: dto.postCode,
      );

  final int id;
  final String name;
  final String district;
  final int postCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Place && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

extension PlaceExtension on Place {
  String get formattedText => '$name, $district';

  String get extractedName {
    final generalRegex = RegExp(r'^[^\s]+\s+');
    final result = name.replaceFirst(generalRegex, '');
    // If the regex didn't match (e.g., no space found, meaning no abbreviation), return original.
    // This happens if fullLocation.replaceFirst returns the same string.
    return result == name && name.contains(' ') ? name : result;
  }
}
