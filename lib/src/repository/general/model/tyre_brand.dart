import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/service_book/tyre_brand_dto.dart';

@immutable
final class TyreBrand {
  const TyreBrand({required this.id, required this.name});

  factory TyreBrand.fromDto(TyreBrandDto dto) =>
      TyreBrand(id: dto.id, name: dto.brand);

  final int id;
  final String name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TyreBrand && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
