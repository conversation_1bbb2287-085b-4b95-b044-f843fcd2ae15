import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/service_book/tire_size_dto.dart';
import 'package:sba/src/repository/general/model/types/tyre_size_type.dart';

@immutable
final class TyreSize {
 const TyreSize({required this.size, required this.type});

  factory TyreSize.fromDto(TireSizeDto dto) => TyreSize(
        size: dto.size,
        type: TyreSizeType.fromServerCode(dto.type),
      );

  final double size;
  final TyreSizeType? type;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TyreSize &&
          runtimeType == other.runtimeType &&
          size == other.size &&
          type == other.type;

  @override
  int get hashCode => size.hashCode ^ type.hashCode;
}
