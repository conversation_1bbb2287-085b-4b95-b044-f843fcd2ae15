import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/service_book/fuel_type_dto.dart';

@immutable
final class FuelType {
  const FuelType({required this.id, required this.name});

  factory FuelType.fromDto(FuelTypeDto dto) =>
      FuelType(id: dto.id, name: dto.fuel);

  final int id;
  final String name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FuelType && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
