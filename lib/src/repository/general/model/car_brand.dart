import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/vehicle/car_brand_dto.dart';

@immutable
final class CarBrand {
  const CarBrand({required this.id, required this.name, required this.models});

  factory CarBrand.fromDto(CarBrandDto dto) => CarBrand(
        id: dto.id,
        name: dto.brand,
        models: dto.models.map(CarModel.fromDto).toList(growable: false),
      );

  final int id;
  final String name;
  final List<CarModel> models;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CarBrand && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@immutable
final class CarModel {
  const CarModel({required this.id, required this.name});

  factory CarModel.fromDto(CarModelDto dto) =>
      CarModel(id: dto.id, name: dto.model);

  final int id;
  final String name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CarModel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
