import 'package:flutter/cupertino.dart';
import 'package:sba/src/api/model/service_book/fuel_traders_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

@immutable
final class FuelTrader {
  const FuelTrader({required this.id, required this.name, required this.fuels});

  factory FuelTrader.fromDto(FuelTradersDto dto) => FuelTrader(
        id: dto.id,
        name: dto.name,
        fuels: dto.fuels.map(Fuel.fromDto).toList(growable: false),
      );

  factory FuelTrader.fake() =>
      FuelTrader(id: 0, name: BoneMock.name, fuels: List.empty());

  final int id;
  final String name;
  final List<Fuel> fuels;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FuelTrader && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

@immutable
final class Fuel {
  const Fuel({
    required this.id,
    required this.name,
    required this.typeId,
    required this.typeName,
  });

  factory Fuel.fromDto(FuelDto dto) =>
      Fuel(id: dto.id, name: dto.name, typeId: dto.typeId, typeName: dto.name);

  factory Fuel.fake() =>
      Fuel(id: 0, name: BoneMock.name, typeId: 0, typeName: BoneMock.name);

  final int id;
  final String name;

  final int typeId;

  final String typeName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Fuel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
