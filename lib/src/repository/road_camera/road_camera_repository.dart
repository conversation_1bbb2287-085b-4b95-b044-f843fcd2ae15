import 'dart:async';

import 'package:logger/logger.dart';
import 'package:sba/src/api/road_camera_api.dart';
import 'package:sba/src/common/result/call_with_result.dart';
import 'package:sba/src/common/result/result.dart';
import 'package:sba/src/common/result/result_extension.dart';
import 'package:sba/src/repository/road_camera/model/road_camera.dart';

const _tag = 'RoadCameraRepository';

final class RoadCameraRepository {
  RoadCameraRepository({required RoadCameraApi api, required Logger logger})
      : _api = api,
        _logger = logger;

  final RoadCameraApi _api;
  final Logger _logger;

  Future<Result<List<RoadCamera>>> getRoadCameras() async {
    try {
      final result = await callWithCachedResult(_api.getCCTV);

      return result.listMap(RoadCamera.fromDto);
    } catch (e) {
      _logger.e('$_tag: $e');
      return Result.otherError(e);
    }
  }
}
