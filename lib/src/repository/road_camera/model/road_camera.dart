import 'package:latlong2/latlong.dart';
import 'package:sba/src/api/model/common/cctv_dto.dart';
import 'package:skeletonizer/skeletonizer.dart';

final class RoadCamera {
  RoadCamera({
    required this.id,
    required this.coordinates,
    required this.name,
    required this.imageUri,
  });

  factory RoadCamera.fromDto(CCTVDto dto) => RoadCamera(
        id: dto.id,
        coordinates: LatLng(dto.latitude, dto.longitude),
        name: dto.description,
        imageUri: dto.url,
      );

  factory RoadCamera.fake() => RoadCamera(
        id: 1,
        coordinates: const LatLng(0, 0),
        name: BoneMock.name,
        imageUri: '',
      );

  final int id;
  final LatLng coordinates;
  final String? name;
  final String? imageUri;
}
