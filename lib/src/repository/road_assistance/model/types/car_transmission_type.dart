import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum CarTransmissionType {
  unknown(0),
  manual(1),
  automatic(2),
  semiAutomatic(3),
  cvt(4);

  const CarTransmissionType(this.code);

  static CarTransmissionType? fromCode(int code) =>
      CarTransmissionType.values.firstWhereOrNull((e) => e.code == code);

  static List<CarTransmissionType> get valid => CarTransmissionType.values
      .where((e) => e != CarTransmissionType.unknown)
      .toList(growable: false);

  final int code;
}

extension CarTransmissionTypeExtension on CarTransmissionType {
  String localizedName(BuildContext context) => switch (this) {
        CarTransmissionType.unknown => '',
        CarTransmissionType.manual =>
          context.l10n.form_type_option_transmission_type_manual,
        CarTransmissionType.automatic =>
          context.l10n.form_type_option_transmission_type_automatic,
        CarTransmissionType.semiAutomatic =>
          context.l10n.form_type_option_transmission_type_semiautomatic,
        CarTransmissionType.cvt =>
          context.l10n.form_type_option_transmission_type_cvt,
      };
}
