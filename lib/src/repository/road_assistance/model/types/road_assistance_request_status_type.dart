import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/ui_colors.dart';

enum RoadAssistanceRequestStatusType {
  requested(0),
  inProgress(1),
  completed(2),
  declinedByUser(3),
  declinedByOperator(4);

  const RoadAssistanceRequestStatusType(this.code);

  static RoadAssistanceRequestStatusType? fromCode(int code) =>
      RoadAssistanceRequestStatusType.values
          .firstWhereOrNull((e) => e.code == code);

  final int code;
}

extension RoadAssistanceRequestStatusTypeExtension
    on RoadAssistanceRequestStatusType {
  bool get isActive => {
        RoadAssistanceRequestStatusType.requested,
        RoadAssistanceRequestStatusType.inProgress,
      }.contains(this);

  String localizedName(BuildContext context) => switch (this) {
        RoadAssistanceRequestStatusType.requested =>
          context.l10n.road_assistance_status_requested,
        RoadAssistanceRequestStatusType.inProgress =>
          context.l10n.road_assistance_status_progress,
        RoadAssistanceRequestStatusType.completed =>
          context.l10n.road_assistance_status_completed,
        RoadAssistanceRequestStatusType.declinedByUser =>
          context.l10n.road_assistance_status_cancelled,
        RoadAssistanceRequestStatusType.declinedByOperator =>
          context.l10n.road_assistance_status_cancelled,
      };

  Color get color => switch (this) {
        RoadAssistanceRequestStatusType.requested => UIColors.primary,
        RoadAssistanceRequestStatusType.inProgress => UIColors.primary,
        RoadAssistanceRequestStatusType.completed => UIColors.green,
        RoadAssistanceRequestStatusType.declinedByUser => UIColors.red,
        RoadAssistanceRequestStatusType.declinedByOperator => UIColors.red,
      };
}
