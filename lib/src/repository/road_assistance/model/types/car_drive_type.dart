import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:sba/src/localization/localization_extension.dart';

enum CarDriveType {
  unknown(0),
  fwd(1),
  rwd(2),
  awd(3),
  fourWD(4);

  const CarDriveType(this.code);

  static CarDriveType? fromCode(int code) =>
      CarDriveType.values.firstWhereOrNull((e) => e.code == code);

  static List<CarDriveType> get valid => CarDriveType.values
      .where((e) => e != CarDriveType.unknown)
      .toList(growable: false);

  final int code;
}

extension CarDriveTypeExtension on CarDriveType {
  String localizedName(BuildContext context) => switch (this) {
        CarDriveType.unknown => '',
        CarDriveType.fwd => context.l10n.form_type_option_drive_type_fwd,
        CarDriveType.rwd => context.l10n.form_type_option_drive_type_rwd,
        CarDriveType.awd => context.l10n.form_type_option_drive_type_awd,
        CarDriveType.fourWD => context.l10n.form_type_option_drive_type_4wd,
      };
}
