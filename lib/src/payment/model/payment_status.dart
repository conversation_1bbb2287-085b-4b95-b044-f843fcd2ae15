import 'package:flutter/material.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/ui/theme/ui_colors.dart';

enum PaymentStatus {
  unknown(-1),
  registered(0),
  onHold(1),
  authorized(2),
  canceled(3),
  refunded(4),
  authorizing(5),
  declined(6);

  const PaymentStatus(this.code);

  factory PaymentStatus.fromServerValue(int serverValue) =>
      PaymentStatus.values.firstWhere(
        (e) => e.code == serverValue,
        orElse: () => PaymentStatus.unknown,
      );

  final int code;
}

extension PaymentStatusExtension on PaymentStatus {
  bool get isPaid => {
        PaymentStatus.authorized,
        PaymentStatus.onHold,
        PaymentStatus.authorizing,
      }.contains(this);

  bool get shouldBePaid => {
        PaymentStatus.registered,
        PaymentStatus.canceled,
      }.contains(this);

  String localizedName(BuildContext context) => switch (this) {
        PaymentStatus.registered ||
        PaymentStatus.onHold ||
        PaymentStatus.authorizing =>
          context.l10n.payment_status_pending,
        PaymentStatus.authorized => context.l10n.payment_status_completed,
        PaymentStatus.refunded ||
        PaymentStatus.unknown =>
          context.l10n.payment_status_failed,
        PaymentStatus.declined ||
        PaymentStatus.canceled =>
          context.l10n.payment_status_declined,
      };

  Color get color => switch (this) {
        PaymentStatus.registered ||
        PaymentStatus.onHold ||
        PaymentStatus.authorizing =>
          UIColors.primary,
        PaymentStatus.authorized => UIColors.green,
        PaymentStatus.refunded ||
        PaymentStatus.declined ||
        PaymentStatus.canceled ||
        PaymentStatus.unknown =>
          UIColors.red,
      };
}
