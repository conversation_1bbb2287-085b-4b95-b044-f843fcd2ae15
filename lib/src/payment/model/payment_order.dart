import 'package:flutter/foundation.dart';
import 'package:sba/src/api/model/payment/payment_order_dto.dart';
import 'package:sba/src/payment/model/payment_status.dart';

@immutable
final class PaymentOrder {
  const PaymentOrder({
    required this.orderId,
    required this.orderNumber,
    required this.formUrl,
    this.paymentStatus = PaymentStatus.unknown,
  });

  factory PaymentOrder.fromDto(PaymentOrderDto dto) => PaymentOrder(
        orderId: dto.orderId ?? '',
        orderNumber: dto.orderNumber ?? '',
        formUrl: dto.formUrl ?? '',
      );

  final String orderId;
  final String orderNumber;
  final String formUrl;
  final PaymentStatus paymentStatus;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentOrder &&
          runtimeType == other.runtimeType &&
          orderId == other.orderId;

  @override
  int get hashCode => orderId.hashCode;

  PaymentOrder copyWith({
    String? orderId,
    String? orderNumber,
    String? formUrl,
    PaymentStatus? paymentStatus,
  }) {
    return PaymentOrder(
      orderId: orderId ?? this.orderId,
      orderNumber: orderNumber ?? this.orderNumber,
      formUrl: formUrl ?? this.formUrl,
      paymentStatus: paymentStatus ?? this.paymentStatus,
    );
  }
}
