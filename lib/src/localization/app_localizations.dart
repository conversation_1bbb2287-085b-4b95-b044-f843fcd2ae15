import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_bg.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'localization/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('bg')];

  /// No description provided for @app_title.
  ///
  /// In bg, this message translates to:
  /// **'СБА'**
  String get app_title;

  /// No description provided for @form_email.
  ///
  /// In bg, this message translates to:
  /// **'Имейл адрес'**
  String get form_email;

  /// No description provided for @form_email_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете вашият имейл...'**
  String get form_email_hint;

  /// No description provided for @form_password.
  ///
  /// In bg, this message translates to:
  /// **'Парола'**
  String get form_password;

  /// No description provided for @form_old_password.
  ///
  /// In bg, this message translates to:
  /// **'Предишна парола'**
  String get form_old_password;

  /// No description provided for @form_new_password.
  ///
  /// In bg, this message translates to:
  /// **'Нова парола'**
  String get form_new_password;

  /// No description provided for @form_password_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете вашата парола...'**
  String get form_password_hint;

  /// No description provided for @form_confirm_password.
  ///
  /// In bg, this message translates to:
  /// **'Повтори парола'**
  String get form_confirm_password;

  /// No description provided for @form_confirm_password_hint.
  ///
  /// In bg, this message translates to:
  /// **'Повторете вашата парола...'**
  String get form_confirm_password_hint;

  /// No description provided for @form_company_name.
  ///
  /// In bg, this message translates to:
  /// **'Име на фирма'**
  String get form_company_name;

  /// No description provided for @form_company_name_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете името на фирмата...'**
  String get form_company_name_hint;

  /// No description provided for @form_eik.
  ///
  /// In bg, this message translates to:
  /// **'ЕИК'**
  String get form_eik;

  /// No description provided for @form_eik_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете единен идентификационен код...'**
  String get form_eik_hint;

  /// No description provided for @form_name.
  ///
  /// In bg, this message translates to:
  /// **'Име'**
  String get form_name;

  /// No description provided for @form_name_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете вашето име...'**
  String get form_name_hint;

  /// No description provided for @form_driver_name.
  ///
  /// In bg, this message translates to:
  /// **'Име на водача'**
  String get form_driver_name;

  /// No description provided for @form_driver_name_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете име на водача...'**
  String get form_driver_name_hint;

  /// No description provided for @form_last_name.
  ///
  /// In bg, this message translates to:
  /// **'Фамилия'**
  String get form_last_name;

  /// No description provided for @form_last_name_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете вашата фамилия...'**
  String get form_last_name_hint;

  /// No description provided for @form_city.
  ///
  /// In bg, this message translates to:
  /// **'Град'**
  String get form_city;

  /// No description provided for @form_city_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете града, в който живеете...'**
  String get form_city_hint;

  /// No description provided for @form_city_delivery_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете град за доставка...'**
  String get form_city_delivery_hint;

  /// No description provided for @form_city_choose_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете град...'**
  String get form_city_choose_hint;

  /// No description provided for @form_office.
  ///
  /// In bg, this message translates to:
  /// **'Офис'**
  String get form_office;

  /// No description provided for @form_office_speedy.
  ///
  /// In bg, this message translates to:
  /// **'Офис на Спиди'**
  String get form_office_speedy;

  /// No description provided for @form_office_hint.
  ///
  /// In bg, this message translates to:
  /// **'Започнете да въвеждате ...'**
  String get form_office_hint;

  /// No description provided for @form_zip.
  ///
  /// In bg, this message translates to:
  /// **'Пощенски код'**
  String get form_zip;

  /// No description provided for @form_zip_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете пощенски код...'**
  String get form_zip_hint;

  /// No description provided for @form_address.
  ///
  /// In bg, this message translates to:
  /// **'Адрес'**
  String get form_address;

  /// No description provided for @form_address_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете вашия адрес...'**
  String get form_address_hint;

  /// No description provided for @form_complex.
  ///
  /// In bg, this message translates to:
  /// **'Кв. / ж.к.'**
  String get form_complex;

  /// No description provided for @form_complex_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете квартал / жилищен комплекс...'**
  String get form_complex_hint;

  /// No description provided for @form_street.
  ///
  /// In bg, this message translates to:
  /// **'Улица'**
  String get form_street;

  /// No description provided for @form_street_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете улица...'**
  String get form_street_hint;

  /// No description provided for @form_street_number.
  ///
  /// In bg, this message translates to:
  /// **'Номер'**
  String get form_street_number;

  /// No description provided for @form_street_number_hint.
  ///
  /// In bg, this message translates to:
  /// **'№'**
  String get form_street_number_hint;

  /// No description provided for @form_block.
  ///
  /// In bg, this message translates to:
  /// **'Блок'**
  String get form_block;

  /// No description provided for @form_block_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете блок...'**
  String get form_block_hint;

  /// No description provided for @form_entry.
  ///
  /// In bg, this message translates to:
  /// **'Вход'**
  String get form_entry;

  /// No description provided for @form_entry_hint.
  ///
  /// In bg, this message translates to:
  /// **'Вход'**
  String get form_entry_hint;

  /// No description provided for @form_floor.
  ///
  /// In bg, this message translates to:
  /// **'Етаж'**
  String get form_floor;

  /// No description provided for @form_floor_hint.
  ///
  /// In bg, this message translates to:
  /// **'Етаж'**
  String get form_floor_hint;

  /// No description provided for @form_apartment.
  ///
  /// In bg, this message translates to:
  /// **'Ап.'**
  String get form_apartment;

  /// No description provided for @form_apartment_hint.
  ///
  /// In bg, this message translates to:
  /// **'Ап.'**
  String get form_apartment_hint;

  /// No description provided for @form_phone.
  ///
  /// In bg, this message translates to:
  /// **'Телефон'**
  String get form_phone;

  /// No description provided for @form_phone_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете телефонен номер...'**
  String get form_phone_hint;

  /// No description provided for @form_driver_phone.
  ///
  /// In bg, this message translates to:
  /// **'Телефон на водача'**
  String get form_driver_phone;

  /// No description provided for @form_driver_phone_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете телефон на водача...'**
  String get form_driver_phone_hint;

  /// No description provided for @form_brand.
  ///
  /// In bg, this message translates to:
  /// **'Марка'**
  String get form_brand;

  /// No description provided for @form_brand_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете марка...'**
  String get form_brand_hint;

  /// No description provided for @form_vehicle.
  ///
  /// In bg, this message translates to:
  /// **'Автомобил'**
  String get form_vehicle;

  /// No description provided for @form_vehicle_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете автомобил...'**
  String get form_vehicle_hint;

  /// No description provided for @form_model.
  ///
  /// In bg, this message translates to:
  /// **'Модел'**
  String get form_model;

  /// No description provided for @form_model_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете модел...'**
  String get form_model_hint;

  /// No description provided for @form_plate_number.
  ///
  /// In bg, this message translates to:
  /// **'Регистрационен номер'**
  String get form_plate_number;

  /// No description provided for @form_plate_number_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете Регистрационен номер...'**
  String get form_plate_number_hint;

  /// No description provided for @form_notification_hint.
  ///
  /// In bg, this message translates to:
  /// **'Съгласен съм да получавам следните съобщения по имейл'**
  String get form_notification_hint;

  /// No description provided for @form_sms_hint.
  ///
  /// In bg, this message translates to:
  /// **'Съгласен съм да получавам следните СМС-и'**
  String get form_sms_hint;

  /// No description provided for @form_type_hint.
  ///
  /// In bg, this message translates to:
  /// **'Този профил ще бъде използван от'**
  String get form_type_hint;

  /// No description provided for @form_image_hint.
  ///
  /// In bg, this message translates to:
  /// **'Добавяне на изображение'**
  String get form_image_hint;

  /// No description provided for @form_image_hint_gallery.
  ///
  /// In bg, this message translates to:
  /// **'Галерия'**
  String get form_image_hint_gallery;

  /// No description provided for @form_image_hint_camera.
  ///
  /// In bg, this message translates to:
  /// **'Камера'**
  String get form_image_hint_camera;

  /// No description provided for @form_question.
  ///
  /// In bg, this message translates to:
  /// **'Задайте своя въпрос'**
  String get form_question;

  /// No description provided for @form_question_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете вашия въпрос...'**
  String get form_question_hint;

  /// No description provided for @form_zone.
  ///
  /// In bg, this message translates to:
  /// **'Зона'**
  String get form_zone;

  /// No description provided for @form_zone_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете зона...'**
  String get form_zone_hint;

  /// No description provided for @form_transmission.
  ///
  /// In bg, this message translates to:
  /// **'Скоростна кутия'**
  String get form_transmission;

  /// No description provided for @form_transmission_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете вид скоростна кутия...'**
  String get form_transmission_hint;

  /// No description provided for @form_drive.
  ///
  /// In bg, this message translates to:
  /// **'Задвижване'**
  String get form_drive;

  /// No description provided for @form_drive_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете вид задвижване...'**
  String get form_drive_hint;

  /// No description provided for @form_failure_type.
  ///
  /// In bg, this message translates to:
  /// **'Причина за обездвижване'**
  String get form_failure_type;

  /// No description provided for @form_failure_type_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете причина за обездвижване...'**
  String get form_failure_type_hint;

  /// No description provided for @form_info.
  ///
  /// In bg, this message translates to:
  /// **'Допълнителна информация'**
  String get form_info;

  /// No description provided for @form_info_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете допълнителна информация...'**
  String get form_info_hint;

  /// No description provided for @form_card_number.
  ///
  /// In bg, this message translates to:
  /// **'Номер на карта'**
  String get form_card_number;

  /// No description provided for @form_card_number_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете номер на карта...'**
  String get form_card_number_hint;

  /// No description provided for @form_barcode.
  ///
  /// In bg, this message translates to:
  /// **'Баркод'**
  String get form_barcode;

  /// No description provided for @form_barcode_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете баркод...'**
  String get form_barcode_hint;

  /// No description provided for @form_date.
  ///
  /// In bg, this message translates to:
  /// **'Дата'**
  String get form_date;

  /// No description provided for @form_date_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете дата...'**
  String get form_date_hint;

  /// No description provided for @form_start_date.
  ///
  /// In bg, this message translates to:
  /// **'Начална дата'**
  String get form_start_date;

  /// No description provided for @form_start_date_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете начална дата...'**
  String get form_start_date_hint;

  /// No description provided for @form_end_date.
  ///
  /// In bg, this message translates to:
  /// **'Крайна дата'**
  String get form_end_date;

  /// No description provided for @form_end_date_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете крайна дата...'**
  String get form_end_date_hint;

  /// No description provided for @form_valid_duration.
  ///
  /// In bg, this message translates to:
  /// **'Валидност в месеци'**
  String get form_valid_duration;

  /// No description provided for @form_valid_duration_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете валидност...'**
  String get form_valid_duration_hint;

  /// No description provided for @form_service.
  ///
  /// In bg, this message translates to:
  /// **'Сервиз'**
  String get form_service;

  /// No description provided for @form_service_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете сервиз...'**
  String get form_service_hint;

  /// No description provided for @form_odometer.
  ///
  /// In bg, this message translates to:
  /// **'Километраж'**
  String get form_odometer;

  /// No description provided for @form_odometer_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете километраж...'**
  String get form_odometer_hint;

  /// No description provided for @form_oil.
  ///
  /// In bg, this message translates to:
  /// **'Масло'**
  String get form_oil;

  /// No description provided for @form_oil_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете име на масло...'**
  String get form_oil_hint;

  /// No description provided for @form_quantity.
  ///
  /// In bg, this message translates to:
  /// **'Количество'**
  String get form_quantity;

  /// No description provided for @form_quantity_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете количество...'**
  String get form_quantity_hint;

  /// No description provided for @form_price.
  ///
  /// In bg, this message translates to:
  /// **'Цена'**
  String get form_price;

  /// No description provided for @form_price_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете цена...'**
  String get form_price_hint;

  /// No description provided for @form_note.
  ///
  /// In bg, this message translates to:
  /// **'Бележка'**
  String get form_note;

  /// No description provided for @form_note_hint.
  ///
  /// In bg, this message translates to:
  /// **'Тук можете да запишете бележка...'**
  String get form_note_hint;

  /// No description provided for @form_vehicle_service.
  ///
  /// In bg, this message translates to:
  /// **'Сервизно обслужване'**
  String get form_vehicle_service;

  /// No description provided for @form_vehicle_service_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете вид на сервизно обсужване...'**
  String get form_vehicle_service_hint;

  /// No description provided for @form_vehicle_filter_hint.
  ///
  /// In bg, this message translates to:
  /// **'Сменени филтри'**
  String get form_vehicle_filter_hint;

  /// No description provided for @form_dealer.
  ///
  /// In bg, this message translates to:
  /// **'Търговец'**
  String get form_dealer;

  /// No description provided for @form_dealer_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете търговец...'**
  String get form_dealer_hint;

  /// No description provided for @form_station.
  ///
  /// In bg, this message translates to:
  /// **'Център'**
  String get form_station;

  /// No description provided for @form_station_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете център...'**
  String get form_station_hint;

  /// No description provided for @form_dealer_choose_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете търговец...'**
  String get form_dealer_choose_hint;

  /// No description provided for @form_fuel.
  ///
  /// In bg, this message translates to:
  /// **'Гориво'**
  String get form_fuel;

  /// No description provided for @form_fuel_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете гориво...'**
  String get form_fuel_hint;

  /// No description provided for @form_price_per_liter.
  ///
  /// In bg, this message translates to:
  /// **'Цена за литър'**
  String get form_price_per_liter;

  /// No description provided for @form_price_per_liter_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете цената за литър...'**
  String get form_price_per_liter_hint;

  /// No description provided for @form_tyre_type_hint.
  ///
  /// In bg, this message translates to:
  /// **'Вид гуми'**
  String get form_tyre_type_hint;

  /// No description provided for @form_dot.
  ///
  /// In bg, this message translates to:
  /// **'DOT'**
  String get form_dot;

  /// No description provided for @form_dot_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете DOT...'**
  String get form_dot_hint;

  /// No description provided for @form_tyre.
  ///
  /// In bg, this message translates to:
  /// **'Гуми'**
  String get form_tyre;

  /// No description provided for @form_tyre_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете гуми...'**
  String get form_tyre_hint;

  /// No description provided for @form_country_vehicle.
  ///
  /// In bg, this message translates to:
  /// **'Държава на регистрация на превозното средство'**
  String get form_country_vehicle;

  /// No description provided for @form_country_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете държава...'**
  String get form_country_hint;

  /// No description provided for @form_lpg_hint.
  ///
  /// In bg, this message translates to:
  /// **'Автомобилът има ли газова уредба?'**
  String get form_lpg_hint;

  /// No description provided for @form_mot_lpg_hint.
  ///
  /// In bg, this message translates to:
  /// **'Узаконяване на газова уредба - АГУ?'**
  String get form_mot_lpg_hint;

  /// No description provided for @form_time_diapason.
  ///
  /// In bg, this message translates to:
  /// **'Часови диапазон'**
  String get form_time_diapason;

  /// No description provided for @form_time_diapason_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете часови диапазон...'**
  String get form_time_diapason_hint;

  /// No description provided for @form_time.
  ///
  /// In bg, this message translates to:
  /// **'Час'**
  String get form_time;

  /// No description provided for @form_time_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете час...'**
  String get form_time_hint;

  /// No description provided for @form_delivery_type.
  ///
  /// In bg, this message translates to:
  /// **'Начин на доставка'**
  String get form_delivery_type;

  /// No description provided for @form_payment_type.
  ///
  /// In bg, this message translates to:
  /// **'Начин на плащане'**
  String get form_payment_type;

  /// No description provided for @form_additional_package.
  ///
  /// In bg, this message translates to:
  /// **'Допълнителни пакети'**
  String get form_additional_package;

  /// No description provided for @form_tyre_width.
  ///
  /// In bg, this message translates to:
  /// **'Ширина'**
  String get form_tyre_width;

  /// No description provided for @form_tyre_width_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете ширина...'**
  String get form_tyre_width_hint;

  /// No description provided for @form_tyre_height.
  ///
  /// In bg, this message translates to:
  /// **'Височина'**
  String get form_tyre_height;

  /// No description provided for @form_tyre_height_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете височина...'**
  String get form_tyre_height_hint;

  /// No description provided for @form_tyre_diameter.
  ///
  /// In bg, this message translates to:
  /// **'Диаметър'**
  String get form_tyre_diameter;

  /// No description provided for @form_tyre_diameter_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете диаметър...'**
  String get form_tyre_diameter_hint;

  /// No description provided for @form_category.
  ///
  /// In bg, this message translates to:
  /// **'Категория'**
  String get form_category;

  /// No description provided for @form_category_hint.
  ///
  /// In bg, this message translates to:
  /// **'Изберете категория'**
  String get form_category_hint;

  /// No description provided for @form_notification_option_info.
  ///
  /// In bg, this message translates to:
  /// **'информационни'**
  String get form_notification_option_info;

  /// No description provided for @form_notification_option_promo.
  ///
  /// In bg, this message translates to:
  /// **'промоционални'**
  String get form_notification_option_promo;

  /// No description provided for @form_type_option_person.
  ///
  /// In bg, this message translates to:
  /// **'Физическо лице'**
  String get form_type_option_person;

  /// No description provided for @form_type_option_company.
  ///
  /// In bg, this message translates to:
  /// **'Юридическо лице'**
  String get form_type_option_company;

  /// No description provided for @form_type_option_vehicle_other.
  ///
  /// In bg, this message translates to:
  /// **'Друго МПС'**
  String get form_type_option_vehicle_other;

  /// No description provided for @form_type_option_oil_filter.
  ///
  /// In bg, this message translates to:
  /// **'Маслен филтър'**
  String get form_type_option_oil_filter;

  /// No description provided for @form_type_option_air_filter.
  ///
  /// In bg, this message translates to:
  /// **'Въздушен филтър'**
  String get form_type_option_air_filter;

  /// No description provided for @form_type_option_fuel_filter.
  ///
  /// In bg, this message translates to:
  /// **'Горивен филтър'**
  String get form_type_option_fuel_filter;

  /// No description provided for @form_type_option_cabin_filter.
  ///
  /// In bg, this message translates to:
  /// **'Поленов филтър'**
  String get form_type_option_cabin_filter;

  /// No description provided for @form_type_option_winter_tire.
  ///
  /// In bg, this message translates to:
  /// **'Зимни гуми'**
  String get form_type_option_winter_tire;

  /// No description provided for @form_type_option_summer_tire.
  ///
  /// In bg, this message translates to:
  /// **'Летни гуми'**
  String get form_type_option_summer_tire;

  /// No description provided for @form_type_option_seasonal_tire.
  ///
  /// In bg, this message translates to:
  /// **'Всесезонни гуми'**
  String get form_type_option_seasonal_tire;

  /// No description provided for @form_type_option_yes.
  ///
  /// In bg, this message translates to:
  /// **'Да'**
  String get form_type_option_yes;

  /// No description provided for @form_type_option_no.
  ///
  /// In bg, this message translates to:
  /// **'Не'**
  String get form_type_option_no;

  /// No description provided for @form_type_option_car.
  ///
  /// In bg, this message translates to:
  /// **'Лек автомобил'**
  String get form_type_option_car;

  /// No description provided for @form_type_option_trailer.
  ///
  /// In bg, this message translates to:
  /// **'Ремарке / каравана'**
  String get form_type_option_trailer;

  /// No description provided for @form_type_option_delivery_address.
  ///
  /// In bg, this message translates to:
  /// **'Доставка до адрес'**
  String get form_type_option_delivery_address;

  /// No description provided for @form_type_option_delivery_speedy.
  ///
  /// In bg, this message translates to:
  /// **'Доставка до офис на Спиди'**
  String get form_type_option_delivery_speedy;

  /// No description provided for @form_type_option_payment_delivery.
  ///
  /// In bg, this message translates to:
  /// **'Плащане при доставка'**
  String get form_type_option_payment_delivery;

  /// No description provided for @form_type_option_payment_card.
  ///
  /// In bg, this message translates to:
  /// **'Плащане с карта'**
  String get form_type_option_payment_card;

  /// No description provided for @form_type_option_payment_bank.
  ///
  /// In bg, this message translates to:
  /// **'Банков превод'**
  String get form_type_option_payment_bank;

  /// No description provided for @form_type_option_drive_type_fwd.
  ///
  /// In bg, this message translates to:
  /// **'Предно'**
  String get form_type_option_drive_type_fwd;

  /// No description provided for @form_type_option_drive_type_rwd.
  ///
  /// In bg, this message translates to:
  /// **'Задно'**
  String get form_type_option_drive_type_rwd;

  /// No description provided for @form_type_option_drive_type_awd.
  ///
  /// In bg, this message translates to:
  /// **'Пълно'**
  String get form_type_option_drive_type_awd;

  /// No description provided for @form_type_option_drive_type_4wd.
  ///
  /// In bg, this message translates to:
  /// **'4x4'**
  String get form_type_option_drive_type_4wd;

  /// No description provided for @form_type_option_transmission_type_manual.
  ///
  /// In bg, this message translates to:
  /// **'Ръчна'**
  String get form_type_option_transmission_type_manual;

  /// No description provided for @form_type_option_transmission_type_automatic.
  ///
  /// In bg, this message translates to:
  /// **'Автоматична'**
  String get form_type_option_transmission_type_automatic;

  /// No description provided for @form_type_option_transmission_type_semiautomatic.
  ///
  /// In bg, this message translates to:
  /// **'Полуавтоматична'**
  String get form_type_option_transmission_type_semiautomatic;

  /// No description provided for @form_type_option_transmission_type_cvt.
  ///
  /// In bg, this message translates to:
  /// **'Вариаторна'**
  String get form_type_option_transmission_type_cvt;

  /// No description provided for @form_validation_email.
  ///
  /// In bg, this message translates to:
  /// **'Невалиден имейл'**
  String get form_validation_email;

  /// No description provided for @form_validation_min_length.
  ///
  /// In bg, this message translates to:
  /// **'Минимална дължина {length} символа'**
  String form_validation_min_length(Object length);

  /// No description provided for @form_validation_zip.
  ///
  /// In bg, this message translates to:
  /// **'Невалиден код'**
  String get form_validation_zip;

  /// No description provided for @form_validation_phone.
  ///
  /// In bg, this message translates to:
  /// **'Невалиден номер'**
  String get form_validation_phone;

  /// No description provided for @form_validation_required.
  ///
  /// In bg, this message translates to:
  /// **'Задължително поле'**
  String get form_validation_required;

  /// No description provided for @form_validation_password_match.
  ///
  /// In bg, this message translates to:
  /// **'Паролите не съвпадат'**
  String get form_validation_password_match;

  /// No description provided for @form_validation_email_taken.
  ///
  /// In bg, this message translates to:
  /// **'Имейла е взет'**
  String get form_validation_email_taken;

  /// No description provided for @form_validation_numeric.
  ///
  /// In bg, this message translates to:
  /// **'Невалидно число'**
  String get form_validation_numeric;

  /// No description provided for @form_validation_date.
  ///
  /// In bg, this message translates to:
  /// **'Невалидна дата'**
  String get form_validation_date;

  /// No description provided for @form_validation_license_plate.
  ///
  /// In bg, this message translates to:
  /// **'Невалиден регистрационен номер'**
  String get form_validation_license_plate;

  /// No description provided for @navigation_home.
  ///
  /// In bg, this message translates to:
  /// **'Начало'**
  String get navigation_home;

  /// No description provided for @navigation_road_assistance.
  ///
  /// In bg, this message translates to:
  /// **'Пътна помощ'**
  String get navigation_road_assistance;

  /// No description provided for @navigation_road_assistance_request.
  ///
  /// In bg, this message translates to:
  /// **'Пътна помощ за мен'**
  String get navigation_road_assistance_request;

  /// No description provided for @navigation_road_assistance_request_other.
  ///
  /// In bg, this message translates to:
  /// **'Пътна помощ за друг водач'**
  String get navigation_road_assistance_request_other;

  /// No description provided for @navigation_mot.
  ///
  /// In bg, this message translates to:
  /// **'ГТП'**
  String get navigation_mot;

  /// No description provided for @navigation_mot_request.
  ///
  /// In bg, this message translates to:
  /// **'Заявка за ГТП'**
  String get navigation_mot_request;

  /// No description provided for @navigation_parking.
  ///
  /// In bg, this message translates to:
  /// **'Паркиране'**
  String get navigation_parking;

  /// No description provided for @navigation_parking_request.
  ///
  /// In bg, this message translates to:
  /// **'Паркиране в зона'**
  String get navigation_parking_request;

  /// No description provided for @navigation_insurance.
  ///
  /// In bg, this message translates to:
  /// **'Застраховка'**
  String get navigation_insurance;

  /// No description provided for @navigation_other.
  ///
  /// In bg, this message translates to:
  /// **'Други'**
  String get navigation_other;

  /// No description provided for @navigation_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Клубна/членска карта'**
  String get navigation_subscription;

  /// No description provided for @navigation_activate_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Активиране на карта'**
  String get navigation_activate_subscription;

  /// No description provided for @navigation_buy_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Закупуване на карта'**
  String get navigation_buy_subscription;

  /// No description provided for @navigation_renew_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Подновяване на карта'**
  String get navigation_renew_subscription;

  /// No description provided for @navigation_subscription_details.
  ///
  /// In bg, this message translates to:
  /// **'Детайли по карта'**
  String get navigation_subscription_details;

  /// No description provided for @navigation_legal_help.
  ///
  /// In bg, this message translates to:
  /// **'Правна помощ'**
  String get navigation_legal_help;

  /// No description provided for @navigation_training.
  ///
  /// In bg, this message translates to:
  /// **'Учебна дейност'**
  String get navigation_training;

  /// No description provided for @navigation_toll.
  ///
  /// In bg, this message translates to:
  /// **'Винетен стикер'**
  String get navigation_toll;

  /// No description provided for @navigation_toll_check.
  ///
  /// In bg, this message translates to:
  /// **'Провери винетка'**
  String get navigation_toll_check;

  /// No description provided for @navigation_toll_buy.
  ///
  /// In bg, this message translates to:
  /// **'Купи винетка'**
  String get navigation_toll_buy;

  /// No description provided for @navigation_service_book.
  ///
  /// In bg, this message translates to:
  /// **'Сервизна книжка'**
  String get navigation_service_book;

  /// No description provided for @navigation_road_cameras.
  ///
  /// In bg, this message translates to:
  /// **'Камери СБА'**
  String get navigation_road_cameras;

  /// No description provided for @navigation_notification.
  ///
  /// In bg, this message translates to:
  /// **'Известия'**
  String get navigation_notification;

  /// No description provided for @navigation_partners.
  ///
  /// In bg, this message translates to:
  /// **'Партньори'**
  String get navigation_partners;

  /// No description provided for @navigation_information.
  ///
  /// In bg, this message translates to:
  /// **'Информация'**
  String get navigation_information;

  /// No description provided for @navigation_profile_edit.
  ///
  /// In bg, this message translates to:
  /// **'Редакция на профил'**
  String get navigation_profile_edit;

  /// No description provided for @navigation_profile_password.
  ///
  /// In bg, this message translates to:
  /// **'Промяна на парола'**
  String get navigation_profile_password;

  /// No description provided for @navigation_vehicles.
  ///
  /// In bg, this message translates to:
  /// **'Моите автомобили'**
  String get navigation_vehicles;

  /// No description provided for @navigation_vehicles_new.
  ///
  /// In bg, this message translates to:
  /// **'Добавяне на автомобил'**
  String get navigation_vehicles_new;

  /// No description provided for @navigation_vehicles_edit.
  ///
  /// In bg, this message translates to:
  /// **'Промяна на автомобил'**
  String get navigation_vehicles_edit;

  /// No description provided for @navigation_book_service.
  ///
  /// In bg, this message translates to:
  /// **'Сервизно обслужване'**
  String get navigation_book_service;

  /// No description provided for @navigation_book_fuel.
  ///
  /// In bg, this message translates to:
  /// **'Гориво'**
  String get navigation_book_fuel;

  /// No description provided for @navigation_book_engine_oil.
  ///
  /// In bg, this message translates to:
  /// **'Масло - двигател'**
  String get navigation_book_engine_oil;

  /// No description provided for @navigation_book_transmission_oil.
  ///
  /// In bg, this message translates to:
  /// **'Масло - скоростна кутия'**
  String get navigation_book_transmission_oil;

  /// No description provided for @navigation_book_tyres.
  ///
  /// In bg, this message translates to:
  /// **'Гуми'**
  String get navigation_book_tyres;

  /// No description provided for @navigation_book_other_service.
  ///
  /// In bg, this message translates to:
  /// **'Друго сервизно обслужване'**
  String get navigation_book_other_service;

  /// No description provided for @navigation_book_new_tyres.
  ///
  /// In bg, this message translates to:
  /// **'Закупени гуми'**
  String get navigation_book_new_tyres;

  /// No description provided for @navigation_book_swap_tyres.
  ///
  /// In bg, this message translates to:
  /// **'Сезонна смяна на гуми'**
  String get navigation_book_swap_tyres;

  /// No description provided for @navigation_book_mot.
  ///
  /// In bg, this message translates to:
  /// **'Годишни технически прегледи'**
  String get navigation_book_mot;

  /// No description provided for @empty_notification.
  ///
  /// In bg, this message translates to:
  /// **'Нямате известия за момента'**
  String get empty_notification;

  /// No description provided for @empty_vehicles.
  ///
  /// In bg, this message translates to:
  /// **'Нямате автомобили в момента'**
  String get empty_vehicles;

  /// No description provided for @empty_common.
  ///
  /// In bg, this message translates to:
  /// **'Нямате данни за момента'**
  String get empty_common;

  /// No description provided for @empty_search.
  ///
  /// In bg, this message translates to:
  /// **'Няма намерени резултати'**
  String get empty_search;

  /// No description provided for @empty_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Нямате валидни абонаментни/членски карти за автомобил {vehicle}'**
  String empty_subscription(Object vehicle);

  /// No description provided for @empty_insurance.
  ///
  /// In bg, this message translates to:
  /// **'Нямате валидни застраховки за автомобил {vehicle}'**
  String empty_insurance(Object vehicle);

  /// No description provided for @empty_toll.
  ///
  /// In bg, this message translates to:
  /// **'Нямате валидна винетка за автомобил {vehicle}'**
  String empty_toll(Object vehicle);

  /// No description provided for @empty_toll_without_vehicle.
  ///
  /// In bg, this message translates to:
  /// **'Няма валидна винетка'**
  String get empty_toll_without_vehicle;

  /// No description provided for @empty_mot.
  ///
  /// In bg, this message translates to:
  /// **'Нямате валиден годишен технически преглед за автомобил {vehicle}'**
  String empty_mot(Object vehicle);

  /// No description provided for @empty_request.
  ///
  /// In bg, this message translates to:
  /// **'Няма заявки за момента'**
  String get empty_request;

  /// No description provided for @empty_newspaper.
  ///
  /// In bg, this message translates to:
  /// **'Няма ново издание в момента'**
  String get empty_newspaper;

  /// No description provided for @empty_cards.
  ///
  /// In bg, this message translates to:
  /// **'Нямате карти за момента'**
  String get empty_cards;

  /// No description provided for @action_add_vehicle.
  ///
  /// In bg, this message translates to:
  /// **'Добави автомобил'**
  String get action_add_vehicle;

  /// No description provided for @action_request_parking.
  ///
  /// In bg, this message translates to:
  /// **'Заявка за паркиране'**
  String get action_request_parking;

  /// No description provided for @action_send_request.
  ///
  /// In bg, this message translates to:
  /// **'Изпрати заяка'**
  String get action_send_request;

  /// No description provided for @action_request.
  ///
  /// In bg, this message translates to:
  /// **'Заяви'**
  String get action_request;

  /// No description provided for @action_prolong_parking.
  ///
  /// In bg, this message translates to:
  /// **'Удължаване на паркирането'**
  String get action_prolong_parking;

  /// No description provided for @action_choose_coordinates.
  ///
  /// In bg, this message translates to:
  /// **'Избери други координати'**
  String get action_choose_coordinates;

  /// No description provided for @action_choose_position.
  ///
  /// In bg, this message translates to:
  /// **'Избери местоположение'**
  String get action_choose_position;

  /// No description provided for @action_buy_toll.
  ///
  /// In bg, this message translates to:
  /// **'Купи винетка'**
  String get action_buy_toll;

  /// No description provided for @action_reserve_mot.
  ///
  /// In bg, this message translates to:
  /// **'Запиши час за ГТП'**
  String get action_reserve_mot;

  /// No description provided for @action_search.
  ///
  /// In bg, this message translates to:
  /// **'Търси'**
  String get action_search;

  /// No description provided for @action_add.
  ///
  /// In bg, this message translates to:
  /// **'Добавяне'**
  String get action_add;

  /// No description provided for @action_activate.
  ///
  /// In bg, this message translates to:
  /// **'Активиране'**
  String get action_activate;

  /// No description provided for @action_edit.
  ///
  /// In bg, this message translates to:
  /// **'Редактиране'**
  String get action_edit;

  /// No description provided for @action_delete.
  ///
  /// In bg, this message translates to:
  /// **'Изтриване'**
  String get action_delete;

  /// No description provided for @action_save.
  ///
  /// In bg, this message translates to:
  /// **'Запази'**
  String get action_save;

  /// No description provided for @action_register.
  ///
  /// In bg, this message translates to:
  /// **'Регистрация'**
  String get action_register;

  /// No description provided for @action_login.
  ///
  /// In bg, this message translates to:
  /// **'Вход'**
  String get action_login;

  /// No description provided for @action_recover.
  ///
  /// In bg, this message translates to:
  /// **'Възстанови'**
  String get action_recover;

  /// No description provided for @action_exit.
  ///
  /// In bg, this message translates to:
  /// **'Изход'**
  String get action_exit;

  /// No description provided for @action_send.
  ///
  /// In bg, this message translates to:
  /// **'Изпрати'**
  String get action_send;

  /// No description provided for @action_send_sms.
  ///
  /// In bg, this message translates to:
  /// **'Изпрати SMS'**
  String get action_send_sms;

  /// No description provided for @action_call.
  ///
  /// In bg, this message translates to:
  /// **'Обади се'**
  String get action_call;

  /// No description provided for @action_navigate.
  ///
  /// In bg, this message translates to:
  /// **'Навигация'**
  String get action_navigate;

  /// No description provided for @action_ok.
  ///
  /// In bg, this message translates to:
  /// **'Ok'**
  String get action_ok;

  /// No description provided for @action_cancel.
  ///
  /// In bg, this message translates to:
  /// **'Отказ'**
  String get action_cancel;

  /// No description provided for @action_yes.
  ///
  /// In bg, this message translates to:
  /// **'Да'**
  String get action_yes;

  /// No description provided for @action_no.
  ///
  /// In bg, this message translates to:
  /// **'Не'**
  String get action_no;

  /// No description provided for @action_check.
  ///
  /// In bg, this message translates to:
  /// **'Проверка'**
  String get action_check;

  /// No description provided for @action_continue.
  ///
  /// In bg, this message translates to:
  /// **'Продължи'**
  String get action_continue;

  /// No description provided for @action_back.
  ///
  /// In bg, this message translates to:
  /// **'Назад'**
  String get action_back;

  /// No description provided for @action_close.
  ///
  /// In bg, this message translates to:
  /// **'Затвори'**
  String get action_close;

  /// No description provided for @action_confirm.
  ///
  /// In bg, this message translates to:
  /// **'Потвърди'**
  String get action_confirm;

  /// No description provided for @action_pay.
  ///
  /// In bg, this message translates to:
  /// **'Плати'**
  String get action_pay;

  /// No description provided for @action_make_order.
  ///
  /// In bg, this message translates to:
  /// **'Направи поръчка'**
  String get action_make_order;

  /// No description provided for @action_track.
  ///
  /// In bg, this message translates to:
  /// **'Проследи'**
  String get action_track;

  /// No description provided for @action_renew_card.
  ///
  /// In bg, this message translates to:
  /// **'Поднови карта'**
  String get action_renew_card;

  /// No description provided for @message_profile_not_finished.
  ///
  /// In bg, this message translates to:
  /// **'Незавършен профил'**
  String get message_profile_not_finished;

  /// No description provided for @message_success_create.
  ///
  /// In bg, this message translates to:
  /// **'Добавянето е успешно'**
  String get message_success_create;

  /// No description provided for @message_success_delete.
  ///
  /// In bg, this message translates to:
  /// **'Изтриването е успешно'**
  String get message_success_delete;

  /// No description provided for @message_success_edit.
  ///
  /// In bg, this message translates to:
  /// **'Промените бяха записани успешно'**
  String get message_success_edit;

  /// No description provided for @message_success_request.
  ///
  /// In bg, this message translates to:
  /// **'Заявката е изпратена успешно'**
  String get message_success_request;

  /// No description provided for @message_success_paid.
  ///
  /// In bg, this message translates to:
  /// **'Плащането е успешно'**
  String get message_success_paid;

  /// No description provided for @message_success_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Заявката е изпратена успешно! Ще получите имейл инструкции за следващи стъпки.'**
  String get message_success_subscription;

  /// No description provided for @message_success_paid_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Заявката е платена успешно! Ще получите имейл инструкции за следващи стъпки.'**
  String get message_success_paid_subscription;

  /// No description provided for @message_feature_locked_title.
  ///
  /// In bg, this message translates to:
  /// **'Повечето функции са достъпни само за регистрирани потребители'**
  String get message_feature_locked_title;

  /// No description provided for @message_feature_locked_text.
  ///
  /// In bg, this message translates to:
  /// **'За да използвате всички функции на приложението трябва да се впишете.'**
  String get message_feature_locked_text;

  /// No description provided for @message_mot_expiring_title.
  ///
  /// In bg, this message translates to:
  /// **'Годишният ви технически преглед изтича'**
  String get message_mot_expiring_title;

  /// No description provided for @message_mot_expiring_text.
  ///
  /// In bg, this message translates to:
  /// **'ГТП за автомобил {plate} изтича скоро.'**
  String message_mot_expiring_text(Object plate);

  /// No description provided for @message_delete_title.
  ///
  /// In bg, this message translates to:
  /// **'Изтриване'**
  String get message_delete_title;

  /// No description provided for @message_cancel_title.
  ///
  /// In bg, this message translates to:
  /// **'Отказ'**
  String get message_cancel_title;

  /// No description provided for @message_delete_text.
  ///
  /// In bg, this message translates to:
  /// **'Сигурни ли сте, че искате да изтриете записа'**
  String get message_delete_text;

  /// No description provided for @message_delete_request_text.
  ///
  /// In bg, this message translates to:
  /// **'Сигурни ли сте, че искате да откажете заявката'**
  String get message_delete_request_text;

  /// No description provided for @message_delete_vehicle_title.
  ///
  /// In bg, this message translates to:
  /// **'Изтриване на автомобил'**
  String get message_delete_vehicle_title;

  /// No description provided for @message_delete_vehicle_text.
  ///
  /// In bg, this message translates to:
  /// **'Сигурни ли сте, че искате да изтриете от профила си атомобил с регистрационен номер {plate}?'**
  String message_delete_vehicle_text(Object plate);

  /// No description provided for @error_title.
  ///
  /// In bg, this message translates to:
  /// **'Грешка'**
  String get error_title;

  /// No description provided for @error_general_text.
  ///
  /// In bg, this message translates to:
  /// **'Възникна неочаквана грешка. Опитайте пак.'**
  String get error_general_text;

  /// No description provided for @error_location_text.
  ///
  /// In bg, this message translates to:
  /// **'Неуспешно определяне на местоположение. Опитайте пак.'**
  String get error_location_text;

  /// No description provided for @error_payment_text.
  ///
  /// In bg, this message translates to:
  /// **'Възникна грешка при плащането. Опитайте пак.'**
  String get error_payment_text;

  /// No description provided for @error_sms.
  ///
  /// In bg, this message translates to:
  /// **'Изпращането на СМС не беше успешно. Опитайте пак'**
  String get error_sms;

  /// No description provided for @error_call.
  ///
  /// In bg, this message translates to:
  /// **'Осъществяването на разговор не беше успешно. Опитайте пак'**
  String get error_call;

  /// No description provided for @error_network_text.
  ///
  /// In bg, this message translates to:
  /// **'Няма връзка с сървъра. Опитайте по късно.'**
  String get error_network_text;

  /// No description provided for @error_email_not_found.
  ///
  /// In bg, this message translates to:
  /// **'Невалиден имейл адрес или профила ви не е активиран! Изпращане на активационен имейл?'**
  String get error_email_not_found;

  /// No description provided for @error_wrong_username.
  ///
  /// In bg, this message translates to:
  /// **'Грешно потребителско име или парола'**
  String get error_wrong_username;

  /// No description provided for @error_activation.
  ///
  /// In bg, this message translates to:
  /// **'Профила не е активиран! Изпращане на активационен имейл?'**
  String get error_activation;

  /// No description provided for @error_toll_start_date.
  ///
  /// In bg, this message translates to:
  /// **'Невалидна начална дата! Променете датата и опитайте пак.'**
  String get error_toll_start_date;

  /// No description provided for @error_toll_email.
  ///
  /// In bg, this message translates to:
  /// **'Невалиден имейл! проверете имейл адреса и опитайте пак.'**
  String get error_toll_email;

  /// No description provided for @error_toll_vehicle.
  ///
  /// In bg, this message translates to:
  /// **'Невалиден регистрационен номер! Проверете въведения регистрационен номер и опитайте пак.'**
  String get error_toll_vehicle;

  /// No description provided for @error_toll_activated.
  ///
  /// In bg, this message translates to:
  /// **'Винетката еече активирана.'**
  String get error_toll_activated;

  /// No description provided for @error_toll_has_prucase.
  ///
  /// In bg, this message translates to:
  /// **'Превозното средство име действаща винетка за този период.'**
  String get error_toll_has_prucase;

  /// No description provided for @login_title.
  ///
  /// In bg, this message translates to:
  /// **'Добре дошли отново!'**
  String get login_title;

  /// No description provided for @login_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'ГТП, пътна помощ, застраховки и още, всичко на едно място!'**
  String get login_subtitle;

  /// No description provided for @login_remember_me.
  ///
  /// In bg, this message translates to:
  /// **'Запомни ме'**
  String get login_remember_me;

  /// No description provided for @login_forgotten_password.
  ///
  /// In bg, this message translates to:
  /// **'Забравена парола?'**
  String get login_forgotten_password;

  /// No description provided for @login_guest_sign_in.
  ///
  /// In bg, this message translates to:
  /// **'Вход без регистрация'**
  String get login_guest_sign_in;

  /// No description provided for @login_register_hint.
  ///
  /// In bg, this message translates to:
  /// **'Нямате профил?'**
  String get login_register_hint;

  /// No description provided for @login_send_message_success_title.
  ///
  /// In bg, this message translates to:
  /// **'Активационен имейл'**
  String get login_send_message_success_title;

  /// No description provided for @login_send_message_success_text.
  ///
  /// In bg, this message translates to:
  /// **'Изпратихме инструкции на имейл адреса.'**
  String get login_send_message_success_text;

  /// No description provided for @register_title.
  ///
  /// In bg, this message translates to:
  /// **'Добре дошли!'**
  String get register_title;

  /// No description provided for @register_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'ГТП, пътна помощ, застраховки и още, всичко на едно място!'**
  String get register_subtitle;

  /// No description provided for @register_gdpr.
  ///
  /// In bg, this message translates to:
  /// **'Давам съгласие за обработка на лични данни'**
  String get register_gdpr;

  /// No description provided for @register_sba.
  ///
  /// In bg, this message translates to:
  /// **'Съгласен съм да свържа профила си с базата данни на СБА и да получавам известия'**
  String get register_sba;

  /// No description provided for @register_sign_in_hint.
  ///
  /// In bg, this message translates to:
  /// **'Вече имате регистрация?'**
  String get register_sign_in_hint;

  /// No description provided for @register_message_success_title.
  ///
  /// In bg, this message translates to:
  /// **'Успешна регистрация'**
  String get register_message_success_title;

  /// No description provided for @register_message_success_text.
  ///
  /// In bg, this message translates to:
  /// **'Изпратихме инструкции на имейл адреса с който се регистрирахте'**
  String get register_message_success_text;

  /// No description provided for @recovery_title.
  ///
  /// In bg, this message translates to:
  /// **'Възстановяване на парола'**
  String get recovery_title;

  /// No description provided for @recovery_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Ще изпратим инструкции на имейл-а с който сте регистриран.'**
  String get recovery_subtitle;

  /// No description provided for @recovery_message_success_text.
  ///
  /// In bg, this message translates to:
  /// **'Изпратихме инструкции на имейл адреса с който сте регистриран'**
  String get recovery_message_success_text;

  /// No description provided for @home_title.
  ///
  /// In bg, this message translates to:
  /// **'Здравейте, {name}!'**
  String home_title(Object name);

  /// No description provided for @home_title_guest.
  ///
  /// In bg, this message translates to:
  /// **'Гост'**
  String get home_title_guest;

  /// No description provided for @home_section_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Абонаментни/членски карти'**
  String get home_section_subscription;

  /// No description provided for @home_section_insurance.
  ///
  /// In bg, this message translates to:
  /// **'Валидни застраховки'**
  String get home_section_insurance;

  /// No description provided for @home_section_toll.
  ///
  /// In bg, this message translates to:
  /// **'Валидна винетка'**
  String get home_section_toll;

  /// No description provided for @home_section_mot.
  ///
  /// In bg, this message translates to:
  /// **'Валиден годишен технически преглед'**
  String get home_section_mot;

  /// No description provided for @home_section_other.
  ///
  /// In bg, this message translates to:
  /// **'Други'**
  String get home_section_other;

  /// No description provided for @home_section_newspaper.
  ///
  /// In bg, this message translates to:
  /// **'Вестник'**
  String get home_section_newspaper;

  /// No description provided for @home_vehicle_tile_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Член на СБА:'**
  String get home_vehicle_tile_subscription;

  /// No description provided for @home_vehicle_tile_mot.
  ///
  /// In bg, this message translates to:
  /// **'ГТП:'**
  String get home_vehicle_tile_mot;

  /// No description provided for @home_vehicle_tile_road_insurance.
  ///
  /// In bg, this message translates to:
  /// **'Активна застраховка ГО:'**
  String get home_vehicle_tile_road_insurance;

  /// No description provided for @home_vehicle_tile_insurance.
  ///
  /// In bg, this message translates to:
  /// **'Активна застраховка Каско:'**
  String get home_vehicle_tile_insurance;

  /// No description provided for @home_insurance_tile_company.
  ///
  /// In bg, this message translates to:
  /// **'Застраховател: {company}'**
  String home_insurance_tile_company(Object company);

  /// No description provided for @home_insurance_tile_valid.
  ///
  /// In bg, this message translates to:
  /// **'Период на покритие: {date}'**
  String home_insurance_tile_valid(Object date);

  /// No description provided for @home_toll_tile_title.
  ///
  /// In bg, this message translates to:
  /// **'Винетка от {data}'**
  String home_toll_tile_title(Object data);

  /// No description provided for @home_toll_tile_valid.
  ///
  /// In bg, this message translates to:
  /// **'Срок на валидност: до {date}'**
  String home_toll_tile_valid(Object date);

  /// No description provided for @home_mot_tile_title.
  ///
  /// In bg, this message translates to:
  /// **'ГТП от {date}'**
  String home_mot_tile_title(Object date);

  /// No description provided for @home_mot_tile_valid.
  ///
  /// In bg, this message translates to:
  /// **'Срок на валидност: {date}'**
  String home_mot_tile_valid(Object date);

  /// No description provided for @home_newspaper_tile.
  ///
  /// In bg, this message translates to:
  /// **'Вестник'**
  String get home_newspaper_tile;

  /// No description provided for @home_newspaper_tile_year_label.
  ///
  /// In bg, this message translates to:
  /// **'година'**
  String get home_newspaper_tile_year_label;

  /// No description provided for @home_newspaper_tile_issue_label.
  ///
  /// In bg, this message translates to:
  /// **'Брой:'**
  String get home_newspaper_tile_issue_label;

  /// No description provided for @profile_navigation_edit_title.
  ///
  /// In bg, this message translates to:
  /// **'Моят профил'**
  String get profile_navigation_edit_title;

  /// No description provided for @profile_navigation_edit_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Редактиране на лични данни'**
  String get profile_navigation_edit_subtitle;

  /// No description provided for @profile_navigation_password_title.
  ///
  /// In bg, this message translates to:
  /// **'Сигурност'**
  String get profile_navigation_password_title;

  /// No description provided for @profile_navigation_password_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Промяна на парола'**
  String get profile_navigation_password_subtitle;

  /// No description provided for @profile_navigation_cars_title.
  ///
  /// In bg, this message translates to:
  /// **'Моите автомобили'**
  String get profile_navigation_cars_title;

  /// No description provided for @profile_navigation_cars_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Редактиране на моите автомобили'**
  String get profile_navigation_cars_subtitle;

  /// No description provided for @profile_navigation_notifications_title.
  ///
  /// In bg, this message translates to:
  /// **'Известия'**
  String get profile_navigation_notifications_title;

  /// No description provided for @profile_navigation_notifications_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Разрешения за известия'**
  String get profile_navigation_notifications_subtitle;

  /// No description provided for @profile_edit_personal_data_section.
  ///
  /// In bg, this message translates to:
  /// **'Лични данни'**
  String get profile_edit_personal_data_section;

  /// No description provided for @profile_edit_password_section.
  ///
  /// In bg, this message translates to:
  /// **'Парола'**
  String get profile_edit_password_section;

  /// No description provided for @profile_edit_address_section.
  ///
  /// In bg, this message translates to:
  /// **'Адрес'**
  String get profile_edit_address_section;

  /// No description provided for @profile_notification_email_section.
  ///
  /// In bg, this message translates to:
  /// **'Имейл съобщения'**
  String get profile_notification_email_section;

  /// No description provided for @profile_notification_sms_section.
  ///
  /// In bg, this message translates to:
  /// **'СМС-и'**
  String get profile_notification_sms_section;

  /// No description provided for @vehicle_edit_information_section.
  ///
  /// In bg, this message translates to:
  /// **'Информация за автомобила'**
  String get vehicle_edit_information_section;

  /// No description provided for @vehicle_edit_place_section.
  ///
  /// In bg, this message translates to:
  /// **'Местодомуване на автомобила'**
  String get vehicle_edit_place_section;

  /// No description provided for @training_map_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Учебни центрове'**
  String get training_map_subtitle;

  /// No description provided for @training_description_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Повече за дейността'**
  String get training_description_subtitle;

  /// No description provided for @training_description_text.
  ///
  /// In bg, this message translates to:
  /// **'Съюзът на българските автомобилисти обучава водачи на моторни превозни средства от 1960 г. СБА съхрани традициите и доброто си име в организирането и провеждането на дейността по обучение на водачи от категория “В”. Сега организацията разполага с над 23 учебни центъра с учебни автомобили в цялата страна.\nКурсовете в СБА се водят съобразно изискванията на Наредба №37 и учебната документация за съответната категория.'**
  String get training_description_text;

  /// No description provided for @training_item_1_title.
  ///
  /// In bg, this message translates to:
  /// **'КАТЕГОРИЯ „B“ – ВОДАЧИ НА МПС ДО 8 МЕСТА И 3.5 ТОНА'**
  String get training_item_1_title;

  /// No description provided for @training_item_1_text.
  ///
  /// In bg, this message translates to:
  /// **'Изисквания:\n- изпитът по Теория може да се проведе в рамките на месеца преди навършване на 18 години;\n\n- лична карта;\n- декларация за данните от дипломата си;\n- снимка паспортен формат – 1 бр.;\n- диплома за завършено образование;\n\nОБУЧЕНИЕ ПО ТЕОРИЯ:\n\nОбучението по теория се извършва съгласно Наредба №37 за Обучение на водачи. Обучението се провежда в удобно за курсиста време. След завършване на теоретичното обучение се полага вътрешен изпит по теория, след което следва изпит пред ИААА:\n\n– решава се 1 бр. листовка (тест) общо 45 въпроса. В случай, че резултатът е отрицателен следва ново явяване на изпит.\n\nОБУЧЕНИЕ ПО ПРАКТИКА:\n\nЧасовете по кормуване са общо 31 на брой с времетраене от 50 минути всеки.\n\nСлед завършване на практическото обучение се провежда вътрешен изпит по кормуване и следва изпит пред ИААА.\n\nПрактическият изпит се провежда при успешно положен изпит по теория.\n\nЦЕНА НА КУРСА: ТЯ Е РАЗЛИЧНА В ЗАВИСИМОСТ ОТ ВИДА НА УЧЕБНИЯ АВТОМОБИЛ, УЧЕБНИЯ КАБИНЕТ И ДР.\n\nПосочената цена включва теоретичното и практическото обучение, както и всички вътрешни изпити до получаване на свидетелство за завършен курс.\nДопълнително се заплаща явяването на практически и теоретичен изпит пред ДАИ.\n\nИнформация за допълнителното заплащане за изпитите пред ДАИ може да получите от съответния офис на СБА при записване за курс.\n\nНЕ СЕ СЪБИРАТ ДОПЪЛНИТЕЛНИ СРЕДСТВА ЗА ТЕОРЕТИЧНОТО И ПРАКТИЧЕСКО ОБУЧЕНИЕ В РАМКИТЕ НА КУРСА И ВЪТРЕШНИТЕ ИЗПИТИ.'**
  String get training_item_1_text;

  /// No description provided for @training_item_2_title.
  ///
  /// In bg, this message translates to:
  /// **'ВЪЗСТАНОВЯВАНЕ НА КОНТРОЛНИ ТОЧКИ:'**
  String get training_item_2_title;

  /// No description provided for @training_item_2_text.
  ///
  /// In bg, this message translates to:
  /// **'Водачите на моторни превозно средства притежават 39 контролни точки.\n\nПри определени нарушения на Закона за движение по пътищата контролните органи отнемат от точките на водачите. Ежегодно водачите могат след допълнително обучение в лицензирани кабинети на СБА да възстановяват 13 точки. Обучението е теоретично в един ден и завършва с решаването на листовка. За да участвате в това обучение, трябва да не сте изгубили всички контролни точки, както и да сте заплатили всички дължими глоби към КАТ – Пътна полиция.\nЗа курса на обучение се изисква да представите лична карта, свидетелство за правоуправление, синия контролен талон към свидетелството, или ако е отнет, акта, с който е отнет талона.'**
  String get training_item_2_text;

  /// No description provided for @legal_help_tab_cases.
  ///
  /// In bg, this message translates to:
  /// **'Казуси'**
  String get legal_help_tab_cases;

  /// No description provided for @legal_help_tab_questions.
  ///
  /// In bg, this message translates to:
  /// **'Казуси извън посочените'**
  String get legal_help_tab_questions;

  /// No description provided for @legal_help_send_question_section.
  ///
  /// In bg, this message translates to:
  /// **'Изпращане на въпрос'**
  String get legal_help_send_question_section;

  /// No description provided for @legal_help_call_for_help_section.
  ///
  /// In bg, this message translates to:
  /// **'Свържете се с кол център'**
  String get legal_help_call_for_help_section;

  /// No description provided for @legal_help_send_message_success_title.
  ///
  /// In bg, this message translates to:
  /// **'Успех'**
  String get legal_help_send_message_success_title;

  /// No description provided for @legal_help_send_message_success_text.
  ///
  /// In bg, this message translates to:
  /// **'Успешно изпратихте въпроса си! Очаквайте отговор по имейл.'**
  String get legal_help_send_message_success_text;

  /// No description provided for @parking_tile_city.
  ///
  /// In bg, this message translates to:
  /// **'Град:'**
  String get parking_tile_city;

  /// No description provided for @parking_tile_vehicle.
  ///
  /// In bg, this message translates to:
  /// **'Автомобил:'**
  String get parking_tile_vehicle;

  /// No description provided for @parking_tile_zone.
  ///
  /// In bg, this message translates to:
  /// **'Зона:'**
  String get parking_tile_zone;

  /// No description provided for @parking_tile_until.
  ///
  /// In bg, this message translates to:
  /// **'Платен до:'**
  String get parking_tile_until;

  /// No description provided for @parking_sms_message_title.
  ///
  /// In bg, this message translates to:
  /// **'Потвърждение'**
  String get parking_sms_message_title;

  /// No description provided for @parking_sms_message_test.
  ///
  /// In bg, this message translates to:
  /// **'Изпратихте ли СМС за паркиране?'**
  String get parking_sms_message_test;

  /// No description provided for @road_assistance_services_section.
  ///
  /// In bg, this message translates to:
  /// **'Услуги'**
  String get road_assistance_services_section;

  /// No description provided for @road_assistance_requests_section.
  ///
  /// In bg, this message translates to:
  /// **'Вашите заявки'**
  String get road_assistance_requests_section;

  /// No description provided for @road_assistance_vehicle_section.
  ///
  /// In bg, this message translates to:
  /// **'Данни за автомобила'**
  String get road_assistance_vehicle_section;

  /// No description provided for @road_assistance_driver_section.
  ///
  /// In bg, this message translates to:
  /// **'Лични данни'**
  String get road_assistance_driver_section;

  /// No description provided for @road_assistance_tile_request_number.
  ///
  /// In bg, this message translates to:
  /// **'Заявка № {request}'**
  String road_assistance_tile_request_number(Object request);

  /// No description provided for @road_assistance_tile_reason.
  ///
  /// In bg, this message translates to:
  /// **'Причина:'**
  String get road_assistance_tile_reason;

  /// No description provided for @road_assistance_tile_driver.
  ///
  /// In bg, this message translates to:
  /// **'Водач:'**
  String get road_assistance_tile_driver;

  /// No description provided for @road_assistance_tile_vehicle.
  ///
  /// In bg, this message translates to:
  /// **'Автомобил:'**
  String get road_assistance_tile_vehicle;

  /// No description provided for @road_assistance_tile_submitDate.
  ///
  /// In bg, this message translates to:
  /// **'Заявка от:'**
  String get road_assistance_tile_submitDate;

  /// No description provided for @road_assistance_tile_arrivalDate.
  ///
  /// In bg, this message translates to:
  /// **'Очакван час:'**
  String get road_assistance_tile_arrivalDate;

  /// No description provided for @road_assistance_tile_status.
  ///
  /// In bg, this message translates to:
  /// **'Статус:'**
  String get road_assistance_tile_status;

  /// No description provided for @road_assistance_status_requested.
  ///
  /// In bg, this message translates to:
  /// **'В обработка'**
  String get road_assistance_status_requested;

  /// No description provided for @road_assistance_status_progress.
  ///
  /// In bg, this message translates to:
  /// **'В изпълнение'**
  String get road_assistance_status_progress;

  /// No description provided for @road_assistance_status_completed.
  ///
  /// In bg, this message translates to:
  /// **'Завършена'**
  String get road_assistance_status_completed;

  /// No description provided for @road_assistance_status_cancelled.
  ///
  /// In bg, this message translates to:
  /// **'Отказана'**
  String get road_assistance_status_cancelled;

  /// No description provided for @road_assistance_request_other_caption.
  ///
  /// In bg, this message translates to:
  /// **'* Служител от нашия кол център ще се свърже с водача за установяване на неговата локация'**
  String get road_assistance_request_other_caption;

  /// No description provided for @road_assistance_cancel_message_title.
  ///
  /// In bg, this message translates to:
  /// **'Отмяна на заявка за пътна помощ'**
  String get road_assistance_cancel_message_title;

  /// No description provided for @road_assistance_cancel_message_text.
  ///
  /// In bg, this message translates to:
  /// **'Сигурни ли сте, че искате да отмените пристигането на пътна помощ за автомобил {plate}?'**
  String road_assistance_cancel_message_text(Object plate);

  /// No description provided for @subscription_details_category_label.
  ///
  /// In bg, this message translates to:
  /// **'Тип на картата:'**
  String get subscription_details_category_label;

  /// No description provided for @subscription_details_type_label.
  ///
  /// In bg, this message translates to:
  /// **'Вид на картата:'**
  String get subscription_details_type_label;

  /// No description provided for @subscription_details_vehicle_label.
  ///
  /// In bg, this message translates to:
  /// **'Автомобил:'**
  String get subscription_details_vehicle_label;

  /// No description provided for @subscription_details_card_number_label.
  ///
  /// In bg, this message translates to:
  /// **'Номер на картата:'**
  String get subscription_details_card_number_label;

  /// No description provided for @subscription_details_valid_from_label.
  ///
  /// In bg, this message translates to:
  /// **'Валидна от:'**
  String get subscription_details_valid_from_label;

  /// No description provided for @subscription_details_valid_until_label.
  ///
  /// In bg, this message translates to:
  /// **'Валидна до:'**
  String get subscription_details_valid_until_label;

  /// No description provided for @subscription_activate_activate_section.
  ///
  /// In bg, this message translates to:
  /// **'Тук може да активирате членска карта на СБА, закупена от обектите на нашите партньори от ОМВ.'**
  String get subscription_activate_activate_section;

  /// No description provided for @subscription_activate_information_section.
  ///
  /// In bg, this message translates to:
  /// **'Данни за автомобила'**
  String get subscription_activate_information_section;

  /// No description provided for @subscription_activate_place_section.
  ///
  /// In bg, this message translates to:
  /// **'Местодомуване на автомобила'**
  String get subscription_activate_place_section;

  /// No description provided for @subscription_activate_gdpr_hint.
  ///
  /// In bg, this message translates to:
  /// **'Запознах се с политиката за защита на личните данни и уведомлението за поверително третиране на СБА'**
  String get subscription_activate_gdpr_hint;

  /// No description provided for @subscription_activate_omv_hint.
  ///
  /// In bg, this message translates to:
  /// **'Запознах се с условията, при които мога да използвам отстъпките в OMV'**
  String get subscription_activate_omv_hint;

  /// No description provided for @subscription_activate_terms_hint.
  ///
  /// In bg, this message translates to:
  /// **'Запознах се и приемам общите условията по членство в СБА'**
  String get subscription_activate_terms_hint;

  /// No description provided for @subscription_activate_conditions_hint.
  ///
  /// In bg, this message translates to:
  /// **'Запознах се и приемам устава на СБА'**
  String get subscription_activate_conditions_hint;

  /// No description provided for @subscription_tab_member_section.
  ///
  /// In bg, this message translates to:
  /// **'Членските карти са предназначени за физически лица. Ако сте фирма, вижте абонаментните ни карти.'**
  String get subscription_tab_member_section;

  /// No description provided for @subscription_tab_subscription_section.
  ///
  /// In bg, this message translates to:
  /// **'Абонаментните карти са предназначени както за физически, така и за юридически лица.'**
  String get subscription_tab_subscription_section;

  /// No description provided for @subscription_tab_cards.
  ///
  /// In bg, this message translates to:
  /// **'Карти'**
  String get subscription_tab_cards;

  /// No description provided for @subscription_tab_requests.
  ///
  /// In bg, this message translates to:
  /// **'Заявки'**
  String get subscription_tab_requests;

  /// No description provided for @subscription_type_member.
  ///
  /// In bg, this message translates to:
  /// **'Членство'**
  String get subscription_type_member;

  /// No description provided for @subscription_type_subscription.
  ///
  /// In bg, this message translates to:
  /// **'Абонамент'**
  String get subscription_type_subscription;

  /// No description provided for @subscription_type_silver.
  ///
  /// In bg, this message translates to:
  /// **'Сребърна'**
  String get subscription_type_silver;

  /// No description provided for @subscription_type_silver_plus.
  ///
  /// In bg, this message translates to:
  /// **'Сребърна +'**
  String get subscription_type_silver_plus;

  /// No description provided for @subscription_type_gold.
  ///
  /// In bg, this message translates to:
  /// **'Златна'**
  String get subscription_type_gold;

  /// No description provided for @subscription_type_platinum.
  ///
  /// In bg, this message translates to:
  /// **'Платинена'**
  String get subscription_type_platinum;

  /// No description provided for @subscription_order_title.
  ///
  /// In bg, this message translates to:
  /// **'Заявка за {type}'**
  String subscription_order_title(Object type);

  /// No description provided for @subscription_renew_title.
  ///
  /// In bg, this message translates to:
  /// **'Подновяване на {type}'**
  String subscription_renew_title(Object type);

  /// No description provided for @subscription_request_category_label.
  ///
  /// In bg, this message translates to:
  /// **'Тип на картата:'**
  String get subscription_request_category_label;

  /// No description provided for @subscription_request_type_label.
  ///
  /// In bg, this message translates to:
  /// **'Вид на картата:'**
  String get subscription_request_type_label;

  /// No description provided for @subscription_request_vehicle_label.
  ///
  /// In bg, this message translates to:
  /// **'Автомобил:'**
  String get subscription_request_vehicle_label;

  /// No description provided for @subscription_request_status_label.
  ///
  /// In bg, this message translates to:
  /// **'Статус:'**
  String get subscription_request_status_label;

  /// No description provided for @subscription_request_payment_label.
  ///
  /// In bg, this message translates to:
  /// **'Начин на плащане:'**
  String get subscription_request_payment_label;

  /// No description provided for @subscription_request_payment_status_label.
  ///
  /// In bg, this message translates to:
  /// **'Статус на плащане:'**
  String get subscription_request_payment_status_label;

  /// No description provided for @subscription_request_delivery_label.
  ///
  /// In bg, this message translates to:
  /// **'Доставка:'**
  String get subscription_request_delivery_label;

  /// No description provided for @subscription_request_status_requested.
  ///
  /// In bg, this message translates to:
  /// **'В обработка'**
  String get subscription_request_status_requested;

  /// No description provided for @subscription_request_status_completed.
  ///
  /// In bg, this message translates to:
  /// **'Завършена'**
  String get subscription_request_status_completed;

  /// No description provided for @service_book_oil_label.
  ///
  /// In bg, this message translates to:
  /// **'Масло:'**
  String get service_book_oil_label;

  /// No description provided for @service_book_autoservice_label.
  ///
  /// In bg, this message translates to:
  /// **'Сервиз:'**
  String get service_book_autoservice_label;

  /// No description provided for @service_book_odometer_label.
  ///
  /// In bg, this message translates to:
  /// **'Километраж:'**
  String get service_book_odometer_label;

  /// No description provided for @service_book_quantity_label.
  ///
  /// In bg, this message translates to:
  /// **'Количество:'**
  String get service_book_quantity_label;

  /// No description provided for @service_book_price_label.
  ///
  /// In bg, this message translates to:
  /// **'Цена:'**
  String get service_book_price_label;

  /// No description provided for @service_book_changed_label.
  ///
  /// In bg, this message translates to:
  /// **'Сменени:'**
  String get service_book_changed_label;

  /// No description provided for @service_book_dealer_label.
  ///
  /// In bg, this message translates to:
  /// **'Търговец:'**
  String get service_book_dealer_label;

  /// No description provided for @service_book_fuel_label.
  ///
  /// In bg, this message translates to:
  /// **'Гориво: '**
  String get service_book_fuel_label;

  /// No description provided for @service_book_value_label.
  ///
  /// In bg, this message translates to:
  /// **'Стойност:'**
  String get service_book_value_label;

  /// No description provided for @service_book_brand_label.
  ///
  /// In bg, this message translates to:
  /// **'Марка:'**
  String get service_book_brand_label;

  /// No description provided for @service_book_model_label.
  ///
  /// In bg, this message translates to:
  /// **'Модел:'**
  String get service_book_model_label;

  /// No description provided for @service_book_dot_label.
  ///
  /// In bg, this message translates to:
  /// **'DOT:'**
  String get service_book_dot_label;

  /// No description provided for @service_book_buy_date_label.
  ///
  /// In bg, this message translates to:
  /// **'Дата на закупуване:'**
  String get service_book_buy_date_label;

  /// No description provided for @service_book_date_label.
  ///
  /// In bg, this message translates to:
  /// **'Дата:'**
  String get service_book_date_label;

  /// No description provided for @service_book_changed_by_label.
  ///
  /// In bg, this message translates to:
  /// **'Монтирани от:'**
  String get service_book_changed_by_label;

  /// No description provided for @service_book_station_label.
  ///
  /// In bg, this message translates to:
  /// **'ГТП пункт:'**
  String get service_book_station_label;

  /// No description provided for @service_book_valid_label.
  ///
  /// In bg, this message translates to:
  /// **'Важи до:'**
  String get service_book_valid_label;

  /// No description provided for @service_book_size_label.
  ///
  /// In bg, this message translates to:
  /// **'Размер:'**
  String get service_book_size_label;

  /// No description provided for @service_book_months.
  ///
  /// In bg, this message translates to:
  /// **'{duration} месеца'**
  String service_book_months(Object duration);

  /// No description provided for @toll_id_label.
  ///
  /// In bg, this message translates to:
  /// **'Идентификационен номер на винетка'**
  String get toll_id_label;

  /// No description provided for @toll_vehicle_type_label.
  ///
  /// In bg, this message translates to:
  /// **'Клас на превозното средство'**
  String get toll_vehicle_type_label;

  /// No description provided for @toll_start_date_label.
  ///
  /// In bg, this message translates to:
  /// **'Начална дата и час на валидност'**
  String get toll_start_date_label;

  /// No description provided for @toll_valid_date_label.
  ///
  /// In bg, this message translates to:
  /// **'Крайна дата и час на валидност'**
  String get toll_valid_date_label;

  /// No description provided for @toll_amount_label.
  ///
  /// In bg, this message translates to:
  /// **'Сума'**
  String get toll_amount_label;

  /// No description provided for @toll_status_label.
  ///
  /// In bg, this message translates to:
  /// **'Статус'**
  String get toll_status_label;

  /// No description provided for @toll_status_active.
  ///
  /// In bg, this message translates to:
  /// **'Активна'**
  String get toll_status_active;

  /// No description provided for @toll_status_inactive.
  ///
  /// In bg, this message translates to:
  /// **'Не активна'**
  String get toll_status_inactive;

  /// No description provided for @toll_type_label.
  ///
  /// In bg, this message translates to:
  /// **'Вид винетка'**
  String get toll_type_label;

  /// No description provided for @toll_payment_type_label.
  ///
  /// In bg, this message translates to:
  /// **'Начин на плащане'**
  String get toll_payment_type_label;

  /// No description provided for @toll_period_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Период на валидност'**
  String get toll_period_subtitle;

  /// No description provided for @toll_period_hint.
  ///
  /// In bg, this message translates to:
  /// **'Електронната винетка е валидна за Уикенд от избраната дата на валидност. Можете да закупите винетка 30 дни преди началната дата на валидност.'**
  String get toll_period_hint;

  /// No description provided for @toll_vehicle_data_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Данни за превозното средство'**
  String get toll_vehicle_data_subtitle;

  /// No description provided for @toll_email_subtitle.
  ///
  /// In bg, this message translates to:
  /// **'Имейл'**
  String get toll_email_subtitle;

  /// No description provided for @toll_email_hint.
  ///
  /// In bg, this message translates to:
  /// **'Въведете имейл адрес, на който да получите електронната си винетка.'**
  String get toll_email_hint;

  /// No description provided for @toll_confirm_message_title.
  ///
  /// In bg, this message translates to:
  /// **'Потвърждение на данните'**
  String get toll_confirm_message_title;

  /// No description provided for @toll_success_message_title.
  ///
  /// In bg, this message translates to:
  /// **'Вие успешно закупихте винeтка'**
  String get toll_success_message_title;

  /// No description provided for @toll_success_message_text.
  ///
  /// In bg, this message translates to:
  /// **'На имейл адрес {email} ще получите потвърждение за успешна покупка и разписка за активна е-винетка.'**
  String toll_success_message_text(Object email);

  /// No description provided for @toll_info_message_title.
  ///
  /// In bg, this message translates to:
  /// **'Вие успешно заявихте винетка за автомобил {plate}'**
  String toll_info_message_title(Object plate);

  /// No description provided for @toll_info_message_text.
  ///
  /// In bg, this message translates to:
  /// **'За да завършите закупуването на винетка, моля изберете бутона по-долу и преминете към плащане.'**
  String get toll_info_message_text;

  /// No description provided for @toll_message_plate_number_label.
  ///
  /// In bg, this message translates to:
  /// **'Рег номер:'**
  String get toll_message_plate_number_label;

  /// No description provided for @toll_message_country_label.
  ///
  /// In bg, this message translates to:
  /// **'Държава:'**
  String get toll_message_country_label;

  /// No description provided for @toll_message_type_label.
  ///
  /// In bg, this message translates to:
  /// **'Тип:'**
  String get toll_message_type_label;

  /// No description provided for @toll_message_valid_label.
  ///
  /// In bg, this message translates to:
  /// **'Валидна от:'**
  String get toll_message_valid_label;

  /// No description provided for @toll_message_valid_to_label.
  ///
  /// In bg, this message translates to:
  /// **'Валидна до:'**
  String get toll_message_valid_to_label;

  /// No description provided for @toll_message_issued_label.
  ///
  /// In bg, this message translates to:
  /// **'Издадена на:'**
  String get toll_message_issued_label;

  /// No description provided for @toll_message_validity_label.
  ///
  /// In bg, this message translates to:
  /// **'Продължителност:'**
  String get toll_message_validity_label;

  /// No description provided for @toll_message_price_label.
  ///
  /// In bg, this message translates to:
  /// **'Сума за плащане:'**
  String get toll_message_price_label;

  /// No description provided for @toll_message_paid_label.
  ///
  /// In bg, this message translates to:
  /// **'Заплатена сума:'**
  String get toll_message_paid_label;

  /// No description provided for @toll_message_email_label.
  ///
  /// In bg, this message translates to:
  /// **'Имейл:'**
  String get toll_message_email_label;

  /// No description provided for @mot_status_active.
  ///
  /// In bg, this message translates to:
  /// **'Активна'**
  String get mot_status_active;

  /// No description provided for @mot_status_rejected.
  ///
  /// In bg, this message translates to:
  /// **'Отхвърлена'**
  String get mot_status_rejected;

  /// No description provided for @mot_status_cancelled.
  ///
  /// In bg, this message translates to:
  /// **'Отказана'**
  String get mot_status_cancelled;

  /// No description provided for @mot_status_completed.
  ///
  /// In bg, this message translates to:
  /// **'Завършена'**
  String get mot_status_completed;

  /// No description provided for @mot_status_active_many.
  ///
  /// In bg, this message translates to:
  /// **'Активни'**
  String get mot_status_active_many;

  /// No description provided for @mot_status_rejected_many.
  ///
  /// In bg, this message translates to:
  /// **'Отхвърлени'**
  String get mot_status_rejected_many;

  /// No description provided for @mot_status_cancelled_many.
  ///
  /// In bg, this message translates to:
  /// **'Отказани'**
  String get mot_status_cancelled_many;

  /// No description provided for @mot_status_completed_many.
  ///
  /// In bg, this message translates to:
  /// **'Завършени'**
  String get mot_status_completed_many;

  /// No description provided for @mot_tile_id_label.
  ///
  /// In bg, this message translates to:
  /// **'Заявка № {id}'**
  String mot_tile_id_label(Object id);

  /// No description provided for @mot_tile_vehicle_label.
  ///
  /// In bg, this message translates to:
  /// **'Автомобил:'**
  String get mot_tile_vehicle_label;

  /// No description provided for @mot_tile_place_label.
  ///
  /// In bg, this message translates to:
  /// **'Пункт:'**
  String get mot_tile_place_label;

  /// No description provided for @mot_tile_date_label.
  ///
  /// In bg, this message translates to:
  /// **'Дата:'**
  String get mot_tile_date_label;

  /// No description provided for @mot_tile_time_label.
  ///
  /// In bg, this message translates to:
  /// **'Час:'**
  String get mot_tile_time_label;

  /// No description provided for @mot_tile_services_label.
  ///
  /// In bg, this message translates to:
  /// **'Услуги:'**
  String get mot_tile_services_label;

  /// No description provided for @mot_tile_status.
  ///
  /// In bg, this message translates to:
  /// **'Статус:'**
  String get mot_tile_status;

  /// No description provided for @mot_request_valid_caption.
  ///
  /// In bg, this message translates to:
  /// **'Моля, имайте предвид, че трябва да пристигнете в техническия център 10 минути по-рано от запазения от Вас час за годишен преглед! При записани няколко часа за ГТП за един и същи автомобил, клиентът губи всички часове!'**
  String get mot_request_valid_caption;

  /// No description provided for @mot_documents_button.
  ///
  /// In bg, this message translates to:
  /// **'Необходими документи за ГТП'**
  String get mot_documents_button;

  /// No description provided for @mot_vehicle_category_car.
  ///
  /// In bg, this message translates to:
  /// **'Лек автомобил'**
  String get mot_vehicle_category_car;

  /// No description provided for @mot_vehicle_category_truck_under_35.
  ///
  /// In bg, this message translates to:
  /// **'Камион до 3.5т'**
  String get mot_vehicle_category_truck_under_35;

  /// No description provided for @mot_vehicle_category_truck_over_35.
  ///
  /// In bg, this message translates to:
  /// **'Камион над 3.5т'**
  String get mot_vehicle_category_truck_over_35;

  /// No description provided for @mot_vehicle_category_bus.
  ///
  /// In bg, this message translates to:
  /// **'Автобус'**
  String get mot_vehicle_category_bus;

  /// No description provided for @mot_service_type_inspection.
  ///
  /// In bg, this message translates to:
  /// **'ГТП'**
  String get mot_service_type_inspection;

  /// No description provided for @mot_service_type_lpg.
  ///
  /// In bg, this message translates to:
  /// **'Узаконяване на АГУ'**
  String get mot_service_type_lpg;

  /// No description provided for @partners_filter_all.
  ///
  /// In bg, this message translates to:
  /// **'Всички'**
  String get partners_filter_all;

  /// No description provided for @partners_details_info_section.
  ///
  /// In bg, this message translates to:
  /// **'Информация:'**
  String get partners_details_info_section;

  /// No description provided for @payment_status_pending.
  ///
  /// In bg, this message translates to:
  /// **'В очакване'**
  String get payment_status_pending;

  /// No description provided for @payment_status_completed.
  ///
  /// In bg, this message translates to:
  /// **'Успешно'**
  String get payment_status_completed;

  /// No description provided for @payment_status_declined.
  ///
  /// In bg, this message translates to:
  /// **'Отказано'**
  String get payment_status_declined;

  /// No description provided for @payment_status_failed.
  ///
  /// In bg, this message translates to:
  /// **'Неуспешно'**
  String get payment_status_failed;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['bg'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'bg':
      return AppLocalizationsBg();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
