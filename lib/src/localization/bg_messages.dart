import 'package:timeago/timeago.dart';

class BgMessages implements LookupMessages {
  @override
  String prefixAgo() => 'преди';

  @override
  String prefixFromNow() => '';

  @override
  String suffixAgo() => '';

  @override
  String suffixFromNow() => 'от сега';

  @override
  String lessThanOneMinute(int seconds) => 'момент';

  @override
  String aboutAMinute(int minutes) => '1 минута';

  @override
  String minutes(int minutes) => '$minutes минути';

  @override
  String aboutAnHour(int minutes) => '1 час';

  @override
  String hours(int hours) => '$hours часа';

  @override
  String aDay(int hours) => '1 ден';

  @override
  String days(int days) => '$days дни';

  @override
  String aboutAMonth(int days) => '1 месец';

  @override
  String months(int months) => '$months месеци';

  @override
  String aboutAYear(int year) => '1 година';

  @override
  String years(int years) => '$years години';

  @override
  String wordSeparator() => ' ';
}
