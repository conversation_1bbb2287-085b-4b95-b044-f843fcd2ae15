import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sba/src/generated/assets.gen.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/theme/theme_extension.dart';

class ErrorScreen extends StatelessWidget {
  const ErrorScreen({this.error, super.key});

  final Exception? error;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: UISpacing.defaultScreenPadding,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Assets.images.appLogo.image(width: 150, height: 150),
            const Gap(UISpacing.l),
            Text(
              error.toString(),
              style: context.textTheme.headlineMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
