import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sba/src/app/app_router.dart';
import 'package:sba/src/app/di/di.dart';
import 'package:sba/src/common/utils/splash_screen.dart';
import 'package:sba/src/common/utils/stream_listenable.dart';
import 'package:sba/src/localization/app_localizations.dart';
import 'package:sba/src/localization/localization_extension.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';
import 'package:sba/src/ui/theme/theme.dart';
import 'package:sba/src/ui/widget/stream_listener.dart';

class MyApp extends StatelessWidget {
  MyApp({
    super.key,
  });

  final AuthRepository auth = get();
  late final GoRouter router = AppRouter.generateRouter(
    StreamListenable(auth.loggedInStream),
  );

  @override
  Widget build(BuildContext context) {
    final AuthRepository auth = get();

    return StreamListener(
      stream: auth.loggedInStream,
      onChange: (_) => SplashScreen.remove(),
      child: MaterialApp.router(
        restorationScopeId: 'app',
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        onGenerateTitle: (BuildContext context) => context.l10n.app_title,
        theme: UITheme.data,
        routerConfig: router,
      ),
    );
  }
}
