import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/common/info_dto.dart';
import 'package:sba/src/api/model/common/newspaper_dto.dart';

part 'content_api.g.dart';

@RestApi(baseUrl: 'api/Content')
abstract class ContentApi {
  factory ContentApi(Dio dio) = _ContentApi;

  @GET('/get-all-content')
  Future<List<InfoDto>> getInfo();

  @GET('/newspaper')
  Future<NewspaperDto> getNewspaper();
}
