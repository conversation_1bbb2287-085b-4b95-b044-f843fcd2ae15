import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/vehicle/vehicle_dto.dart';

part 'vehicle_api.g.dart';

@RestApi(baseUrl: 'api/Cars')
abstract class VehicleApi {
  factory VehicleApi(Dio dio) = _VehicleApi;

  @GET('/user-cars')
  Future<List<VehicleDto>> getVehicles();

  @POST('/add-user-car')
  Future<VehicleDto> addVehicle({
    @Body() required VehicleDto data,
  });

  @PUT('/update-user-car')
  Future<void> updateVehicle({
    @Body() required VehicleDto data,
  });

  @DELETE('/delete-user-car')
  Future<void> deleteVehicle({
    @Query('id') required int id,
  });
}
