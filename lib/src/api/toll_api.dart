import 'package:dio/dio.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/request/register_toll_request_dto.dart';
import 'package:sba/src/api/model/toll/toll_check_dto.dart';
import 'package:sba/src/api/model/toll/toll_country_dto.dart';
import 'package:sba/src/api/model/toll/toll_dto.dart';
import 'package:sba/src/api/model/toll/toll_product_dto.dart';
import 'package:sba/src/api/model/toll/toll_register_dto.dart';
import 'package:sba/src/api/model/toll/toll_sale_row_dto.dart';

part 'toll_api.g.dart';

Object? _topLevelDecoder(Map<dynamic, dynamic> json, String key) => json;

@RestApi(baseUrl: 'api/Vignette')
abstract class TollApi {
  factory TollApi(Dio dio) = _TollApi;

  @GET('/countries')
  Future<List<TollCountryDto>> getCountries();

  @GET('/products')
  Future<List<TollProductDto>> getProducts();

  @GET('/check-validity')
  Future<TollResponseDto<TollCheckDto>> tollCheck({
    @Query('countryCode') required String countryCode,
    @Query('lpn') required String plateNumber,
  });

  @POST('/register')
  Future<TollResponseDto<TollRegisterDto>> registerToll({
    @Field('saleRows') required List<RegisterTollRequestDto> request,
  });

  @POST('/activate')
  Future<TollResponseDto<TollSaleRowDto>> activateToll({
    @Query('vignetteId') required String id,
  });

  @GET('/user-vignettes')
  Future<List<TollDto>> getUserToll();

  @POST('/add')
  Future<TollDto> addToll({@Body() required TollDto dto});

  @POST('/update')
  Future<void> updateToll({@Body() required TollDto dto});

  @POST('/set-vehicle-vignette')
  Future<TollDto> updateTollCheck({@Body() required TollDto dto});
}

@JsonSerializable(genericArgumentFactories: true)
final class TollResponseDto<T> {
  TollResponseDto({required this.data, required this.json});

  factory TollResponseDto.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$TollResponseDtoFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T) toJsonT) =>
      _$TollResponseDtoToJson(this, toJsonT);

  @JsonKey(readValue: _topLevelDecoder)
  final T data;
  @JsonKey(readValue: _topLevelDecoder)
  final Map<String, dynamic> json;
}
