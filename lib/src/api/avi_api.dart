import 'package:dio/dio.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/avi/avi_booking_dto.dart';
import 'package:sba/src/api/model/avi/avi_point_dto.dart';

part 'avi_api.g.dart';

@RestApi(baseUrl: 'api/AVI')
abstract class AviApi {
  factory AviApi(Dio dio) = _AviApi;

  @GET('/user-bookings')
  Future<AVIResponseDto<AVIBookingDto>> getBookings();

  @POST('/find-location')
  Future<AVIResponseDto<AVIPointDto>> findLocation({
    @Field('city') required String city,
    @Field('date') required String date,
    @Field('vehicleCategory') required int vehicleCategory,
    @Field('gtp') bool gtp = true,
    @Field('licenceNewLpg') bool lpg = false,
  });

  @POST('/book')
  Future<AVIBookingResponse> book({
    @Field('lineId') required int lineId,
    @Field('date') required String date,
    @Field('slot') required String slot,
    @Field('vehicleCategory') required int vehicleCategory,
    @Field('plateNumber') required String plateNumber,
    @Field('gtp') bool gtp = true,
    @Field('licenceNewLpg') bool lpg = false,
  });

  @DELETE('/cancel-booking')
  Future<void> cancelBooking({@Query('bookingId') required String bookingId});
}

@JsonSerializable(genericArgumentFactories: true)
class AVIResponseDto<T> {
  AVIResponseDto({required this.items});

  factory AVIResponseDto.empty() => AVIResponseDto(items: []);

  factory AVIResponseDto.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$AVIResponseDtoFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T) toJsonT) => _$AVIResponseDtoToJson(this, toJsonT);

  @JsonKey(name: 'items')
  final List<T> items;
}

@JsonSerializable()
class AVIBookingResponse {
  AVIBookingResponse({
    required this.status,
    required this.message,
    required this.bookingDetails,
  });

  factory AVIBookingResponse.fromJson(Map<String, dynamic> json) => _$AVIBookingResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AVIBookingResponseToJson(this);

  @JsonKey(name: 'status')
  final String status;

  @JsonKey(name: 'message')
  final String message;

  @JsonKey(name: 'bookingDetails')
  final AVIBookingDto bookingDetails;
}
