import 'package:dio/dio.dart';
import 'package:sba/src/repository/auth/auth_repository.dart';

final class TokenInterceptor extends Interceptor {
  TokenInterceptor({
    required AuthRepository repository,
    required Dio client,
  })  : _repository = repository,
        _client = client;

  final AuthRepository _repository;
  final Dio _client;

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final token = await _repository.authToken;
    if (token != null) {
      options.headers['Authorization'] = 'Bearer ${token.accessToken}';
    }

    return handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // If a 401 response is received, refresh the access token
      final result = await _repository.refreshTokens();

      if (result.isFailure) {
        return handler.next(err);
      }

      // Update the request header with the new access token
      err.requestOptions.headers['Authorization'] =
          'Bearer ${result.maybeValue?.accessToken}';

      // Repeat the request with the updated header
      return handler.resolve(
        await _client.fetch(err.requestOptions),
      );
    }
    return handler.next(err);
  }
}
