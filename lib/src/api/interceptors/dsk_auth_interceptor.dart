import 'package:dio/dio.dart';

final class DskAuthInterceptor extends Interceptor {
  DskAuthInterceptor({
    required String username,
    required String password,
  })  : _password = password,
        _username = username;

  final String _username;
  final String _password;

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final data = options.data;
    if (data is Map<String, dynamic>) {
      data
        ..putIfAbsent('userName', () => _username)
        ..putIfAbsent('password', () => _password);
    }

    options.data = data;

    return handler.next(options);
  }
}
