import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

final class LoggerInterceptor extends Interceptor {
  LoggerInterceptor({required Logger logger, required String tag})
      : _logger = logger,
        _tag = tag;

  final Logger _logger;
  final String _tag;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    _logger.d('''
    $_tag: *** Request ***
    method: ${options.method}
    uri: ${options.uri}
    data: ${options.data}''');
    super.onRequest(options, handler);
  }

  @override
  void onResponse(
    Response<dynamic> response,
    ResponseInterceptorHandler handler,
  ) {
    _logger.d('''
    $_tag: *** Response ***
    uri: ${response.requestOptions.uri}
    statusCode: ${response.statusCode}
    statusMessage: ${response.statusMessage}
    data: ${response.data}''');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    _logger.e('''
    $_tag: *** Error ***
    uri: ${err.requestOptions.uri}
    type: ${err.type}
    statusCode: ${err.response?.statusCode}
    statusMessage: ${err.response?.statusMessage}
    data: ${err.response?.data}''');
    super.onError(err, handler);
  }
}
