import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/common/place_dto.dart';
import 'package:sba/src/api/model/road_assistance/road_assistance_reason_dto.dart';
import 'package:sba/src/api/model/service_book/fuel_traders_dto.dart';
import 'package:sba/src/api/model/service_book/fuel_type_dto.dart';
import 'package:sba/src/api/model/service_book/tire_size_dto.dart';
import 'package:sba/src/api/model/service_book/tyre_brand_dto.dart';
import 'package:sba/src/api/model/vehicle/car_brand_dto.dart';

part 'general_api.g.dart';

@RestApi(baseUrl: 'api/Nomenclatures')
abstract class GeneralApi {
  factory GeneralApi(Dio dio) = _GeneralApi;

  @GET('/places')
  Future<List<PlaceDto>> getPlaces();

  @GET('/fuel-types')
  Future<List<FuelTypeDto>> getFuelTypes();

  @GET('/car-makes')
  Future<List<CarBrandDto>> getCarBrands();

  @GET('/tyre-brands')
  Future<List<TyreBrandDto>> getTyreBrands();

  @GET('/tyre-sizes')
  Future<List<TireSizeDto>> getTyreSizes({
    @Query('type') required int type,
  });

  @GET('/fuel-traders')
  Future<List<FuelTradersDto>> getFuelTraders();

  @GET('/road-assistance-reasons')
  Future<List<RoadAssistanceReasonDto>> getRoadAssistanceReasons();
}
