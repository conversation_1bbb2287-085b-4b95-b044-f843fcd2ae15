import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/parking/parking_request_dto.dart';
import 'package:sba/src/api/model/parking/parking_zone_dto.dart';

part 'parking_api.g.dart';

@RestApi(baseUrl: 'api/SMSParking')
abstract class ParkingApi {
  factory ParkingApi(Dio dio) = _ParkingApi;

  @GET('/all')
  Future<List<ParkingZoneDto>> getParkingZones();

  @GET('/user-parking')
  Future<List<ParkingRequestDto>> getParkingRequests();

  @POST('/add-user-parking')
  Future<ParkingRequestDto> createParkingRequest({
    @Body() required ParkingRequestDto data,
  });
  @PUT('/update-user-parking')
  Future<ParkingRequestDto> updateParkingRequest({
    @Body() required ParkingRequestDto data,
  });
}
