import 'package:json_annotation/json_annotation.dart';

part 'parking_zone_dto.g.dart';

@JsonSerializable()
final class ParkingZoneDto {
  ParkingZoneDto({required this.id, required this.city, required this.zones});

  factory ParkingZoneDto.fromJson(Map<String, dynamic> json) =>
      _$ParkingZoneDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ParkingZoneDtoToJson(this);

  @<PERSON>sonKey(name: 'id')
  final int id;

  @JsonKey(name: 'place')
  final String city;

  @<PERSON>sonKey(name: 'zones')
  final List<({int id, String name, String phone})> zones;
}
