import 'package:json_annotation/json_annotation.dart';

part 'parking_request_dto.g.dart';
@JsonSerializable(includeIfNull: false)
final class ParkingRequestDto {
  ParkingRequestDto({
    required this.id,
    required this.placeId,
    required this.zoneId,
    required this.dkn,
    required this.durationHours,
    required this.dateTime,
  });

  factory ParkingRequestDto.fromJson(Map<String, dynamic> json) =>
      _$ParkingRequestDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ParkingRequestDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  final int? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'placeId')
  final int placeId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'phoneId')
  final int zoneId;
  @<PERSON>sonKey(name: 'dkn')
  final String dkn;
  @<PERSON>son<PERSON>ey(name: 'durationHours')
  final int durationHours;
  @JsonKey(name: 'created')
  final DateTime? dateTime;
}
