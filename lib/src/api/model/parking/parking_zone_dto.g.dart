// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parking_zone_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ParkingZoneDto _$ParkingZoneDtoFromJson(Map<String, dynamic> json) =>
    ParkingZoneDto(
      id: (json['id'] as num).toInt(),
      city: json['place'] as String,
      zones: (json['zones'] as List<dynamic>)
          .map(
            (e) => _$recordConvert(
              e,
              ($jsonValue) => (
                id: ($jsonValue['id'] as num).toInt(),
                name: $jsonValue['name'] as String,
                phone: $jsonValue['phone'] as String,
              ),
            ),
          )
          .toList(),
    );

Map<String, dynamic> _$ParkingZoneDtoToJson(
  ParkingZoneDto instance,
) => <String, dynamic>{
  'id': instance.id,
  'place': instance.city,
  'zones': instance.zones
      .map(
        (e) => <String, dynamic>{'id': e.id, 'name': e.name, 'phone': e.phone},
      )
      .toList(),
};

$Rec _$recordConvert<$Rec>(Object? value, $Rec Function(Map) convert) =>
    convert(value as Map<String, dynamic>);
