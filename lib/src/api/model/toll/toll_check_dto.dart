import 'package:json_annotation/json_annotation.dart';

part 'toll_check_dto.g.dart';

@JsonSerializable()
final class TollCheckDto {
  TollCheckDto({
    required this.code,
    required this.vehicleClass,
    required this.validityStartDate,
    required this.validityEndDate,
    required this.validityStartFormatted,
    required this.validityEndFormatted,
    required this.price,
    required this.currency,
    required this.status,
    required this.statusCode,
    required this.productId,
    required this.valid,
  });

  factory TollCheckDto.fromJson(Map<String, dynamic> json) =>
      _$TollCheckDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TollCheckDtoToJson(this);

  @Json<PERSON>ey(name: 'vignetteCode')
  final String? code;
  @JsonKey(name: 'vehicleClass')
  final String? vehicleClass;
  @JsonKey(name: 'validityStartDate')
  final DateTime validityStartDate;
  @Json<PERSON><PERSON>(name: 'validityEndDate')
  final DateTime validityEndDate;
  @Json<PERSON><PERSON>(name: 'validityStartFormatted')
  final String? validityStartFormatted;
  @JsonKey(name: 'validityEndFormatted')
  final String? validityEndFormatted;
  @JsonKey(name: 'price')
  final String? price;
  @JsonKey(name: 'currency')
  final String? currency;
  @JsonKey(name: 'status')
  final String? status;
  @JsonKey(name: 'statusCode')
  final int statusCode;
  @JsonKey(name: 'productId')
  final int productId;
  @JsonKey(name: 'valid')
  final bool valid;
}
