// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'toll_sale_row_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TollSaleRowDto _$TollSaleRowDtoFromJson(Map<String, dynamic> json) =>
    TollSaleRowDto(
      active: json['active'] as bool,
      createdOn: DateTime.parse(json['createdOn'] as String),
      id: json['id'] as String?,
      kapschProperties: json['kapschProperties'] == null
          ? null
          : TollKapschPropertiesDto.fromJson(
              json['kapschProperties'] as Map<String, dynamic>,
            ),
      remoteClientId: json['remoteClientId'] as String?,
      uniqueFiscalSaleId: json['uniqueFiscalSaleId'] as String?,
    );

Map<String, dynamic> _$TollSaleRowDtoToJson(TollSaleRowDto instance) =>
    <String, dynamic>{
      'active': instance.active,
      'createdOn': instance.createdOn.toIso8601String(),
      'id': instance.id,
      'kapschProperties': instance.kapschProperties,
      'remoteClientId': instance.remoteClientId,
      'uniqueFiscalSaleId': instance.uniqueFiscalSaleId,
    };
