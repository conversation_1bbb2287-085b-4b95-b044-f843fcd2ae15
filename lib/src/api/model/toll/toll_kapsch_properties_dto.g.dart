// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'toll_kapsch_properties_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TollKapschPropertiesDto _$TollKapschPropertiesDtoFromJson(
  Map<String, dynamic> json,
) => TollKapschPropertiesDto(
  id: json['id'] as String?,
  product: _$recordConvertNullable(
    json['product'],
    ($jsonValue) => (
      emissionClass: $jsonValue['emissionClass'] as String?,
      id: ($jsonValue['id'] as num).toInt(),
      validityType: $jsonValue['validityType'] as String?,
      vehicleType: $jsonValue['vehicleType'] as String?,
    ),
  ),
  status: (json['status'] as num).toInt(),
  vehicle: _$recordConvertNullable(
    json['vehicle'],
    ($jsonValue) => (
      countryCode: $jsonValue['countryCode'] as String?,
      lpn: $jsonValue['lpn'] as String?,
    ),
  ),
  validity: _$recordConvertNullable(
    json['validity'],
    ($jsonValue) => (
      requestedValidityStartDate:
          $jsonValue['requestedValidityStartDate'] == null
          ? null
          : DateTime.parse($jsonValue['requestedValidityStartDate'] as String),
      validityEndDateTimeUTC: $jsonValue['validityEndDateTimeUTC'] == null
          ? null
          : DateTime.parse($jsonValue['validityEndDateTimeUTC'] as String),
      validityStartDateTimeUTC: $jsonValue['validityStartDateTimeUTC'] == null
          ? null
          : DateTime.parse($jsonValue['validityStartDateTimeUTC'] as String),
    ),
  ),
  price: _$recordConvertNullable(
    json['price'],
    ($jsonValue) => (
      amount: ($jsonValue['amount'] as num).toDouble(),
      currency: $jsonValue['currency'] as String?,
    ),
  ),
  purchase: _$recordConvertNullable(
    json['purchase'],
    ($jsonValue) => (
      purchaseDateTimeUTC: DateTime.parse(
        $jsonValue['purchaseDateTimeUTC'] as String,
      ),
    ),
  ),
);

Map<String, dynamic> _$TollKapschPropertiesDtoToJson(
  TollKapschPropertiesDto instance,
) => <String, dynamic>{
  'id': instance.id,
  'product': instance.product == null
      ? null
      : <String, dynamic>{
          'emissionClass': instance.product!.emissionClass,
          'id': instance.product!.id,
          'validityType': instance.product!.validityType,
          'vehicleType': instance.product!.vehicleType,
        },
  'status': instance.status,
  'vehicle': instance.vehicle == null
      ? null
      : <String, dynamic>{
          'countryCode': instance.vehicle!.countryCode,
          'lpn': instance.vehicle!.lpn,
        },
  'validity': instance.validity == null
      ? null
      : <String, dynamic>{
          'requestedValidityStartDate': instance
              .validity!
              .requestedValidityStartDate
              ?.toIso8601String(),
          'validityEndDateTimeUTC': instance.validity!.validityEndDateTimeUTC
              ?.toIso8601String(),
          'validityStartDateTimeUTC': instance
              .validity!
              .validityStartDateTimeUTC
              ?.toIso8601String(),
        },
  'price': instance.price == null
      ? null
      : <String, dynamic>{
          'amount': instance.price!.amount,
          'currency': instance.price!.currency,
        },
  'purchase': instance.purchase == null
      ? null
      : <String, dynamic>{
          'purchaseDateTimeUTC': instance.purchase!.purchaseDateTimeUTC
              .toIso8601String(),
        },
};

$Rec? _$recordConvertNullable<$Rec>(
  Object? value,
  $Rec Function(Map) convert,
) => value == null ? null : convert(value as Map<String, dynamic>);
