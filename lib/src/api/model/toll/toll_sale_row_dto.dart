import 'package:json_annotation/json_annotation.dart';
import 'package:sba/src/api/model/toll/toll_kapsch_properties_dto.dart';

part 'toll_sale_row_dto.g.dart';

@JsonSerializable()
final class TollSaleRowDto {
  TollSaleRowDto({
    required this.active,
    required this.createdOn,
    required this.id,
    required this.kapschProperties,
    required this.remoteClientId,
    required this.uniqueFiscalSaleId,
  });

  factory TollSaleRowDto.fromJson(Map<String, dynamic> json) =>
      _$TollSaleRowDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TollSaleRowDtoToJson(this);

  @Json<PERSON><PERSON>(name: 'active')
  final bool active;
  @Json<PERSON>ey(name: 'createdOn')
  final DateTime createdOn;
  @Json<PERSON>ey(name: 'id')
  final String? id;
  @JsonKey(name: 'kapschProperties')
  final TollKapschPropertiesDto? kapschProperties;
  @Json<PERSON><PERSON>(name: 'remoteClientId')
  final String? remoteClientId;
  @JsonKey(name: 'uniqueFiscalSaleId')
  final String? uniqueFiscalSaleId;
}
