// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'toll_check_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TollCheckDto _$TollCheckDtoFromJson(Map<String, dynamic> json) => TollCheckDto(
  code: json['vignetteCode'] as String?,
  vehicleClass: json['vehicleClass'] as String?,
  validityStartDate: DateTime.parse(json['validityStartDate'] as String),
  validityEndDate: DateTime.parse(json['validityEndDate'] as String),
  validityStartFormatted: json['validityStartFormatted'] as String?,
  validityEndFormatted: json['validityEndFormatted'] as String?,
  price: json['price'] as String?,
  currency: json['currency'] as String?,
  status: json['status'] as String?,
  statusCode: (json['statusCode'] as num).toInt(),
  productId: (json['productId'] as num).toInt(),
  valid: json['valid'] as bool,
);

Map<String, dynamic> _$TollCheckDtoToJson(TollCheckDto instance) =>
    <String, dynamic>{
      'vignetteCode': instance.code,
      'vehicleClass': instance.vehicleClass,
      'validityStartDate': instance.validityStartDate.toIso8601String(),
      'validityEndDate': instance.validityEndDate.toIso8601String(),
      'validityStartFormatted': instance.validityStartFormatted,
      'validityEndFormatted': instance.validityEndFormatted,
      'price': instance.price,
      'currency': instance.currency,
      'status': instance.status,
      'statusCode': instance.statusCode,
      'productId': instance.productId,
      'valid': instance.valid,
    };
