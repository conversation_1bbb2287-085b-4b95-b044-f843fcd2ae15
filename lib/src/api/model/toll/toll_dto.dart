import 'package:json_annotation/json_annotation.dart';
import 'package:sba/src/api/converters/json_string_converter.dart';

part 'toll_dto.g.dart';

@JsonSerializable()
final class TollDto {
  TollDto({
    required this.id,
    required this.userId,
    required this.customer,
    required this.eik,
    required this.email,
    required this.phone,
    required this.date,
    required this.countryCode,
    required this.vehicleType,
    required this.plateNumber,
    required this.from,
    required this.to,
    required this.price,
    required this.state,
    required this.vignetteId,
    required this.issued,
    required this.registerJson,
    required this.activateJson,
    required this.checkJson,
  });

  factory TollDto.fromJson(Map<String, dynamic> json) =>
      _$TollDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TollDtoToJson(this);

  @JsonKey(name: 'id')
  final int? id;
  @JsonKey(name: 'mobileUserId')
  final int userId;
  @<PERSON>son<PERSON>ey(name: 'customer')
  final String customer;
  @JsonKey(name: 'eik')
  final String? eik;
  @JsonKey(name: 'email')
  final String? email;
  @JsonKey(name: 'phone')
  final String? phone;
  @JsonKey(name: 'date')
  final DateTime date;
  @JsonKey(name: 'countryCode')
  final String countryCode;
  @JsonKey(name: 'vehicleType')
  final int vehicleType;
  @JsonKey(name: 'dkn')
  final String plateNumber;
  @JsonKey(name: 'from')
  final DateTime from;
  @JsonKey(name: 'to')
  final DateTime to;
  @JsonKey(name: 'price')
  final double price;
  @JsonKey(name: 'state')
  final int? state;
  @JsonKey(name: 'vignetteId')
  final String vignetteId;
  @JsonKey(name: 'issued')
  final DateTime? issued;
  @JsonKey(name: 'registerJsonResponse')
  @JsonStringConverter()
  final Map<String, dynamic>? registerJson;
  @JsonKey(name: 'activateJsonResponse')
  @JsonStringConverter()
  final Map<String, dynamic>? activateJson;
  @JsonKey(name: 'checkJsonResponse')
  @JsonStringConverter()
  final Map<String, dynamic>? checkJson;
}
