// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'toll_register_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TollRegisterDto _$TollRegisterDtoFromJson(Map<String, dynamic> json) =>
    TollRegisterDto(
      id: json['id'] as String?,
      total: (json['total'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      createdOn: json['createdOn'] == null
          ? null
          : DateTime.parse(json['createdOn'] as String),
      active: json['active'] as bool,
      saleRows: (json['saleRows'] as List<dynamic>)
          .map((e) => TollSaleRowDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TollRegisterDtoToJson(TollRegisterDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'total': instance.total,
      'currency': instance.currency,
      'createdOn': instance.createdOn?.toIso8601String(),
      'active': instance.active,
      'saleRows': instance.saleRows,
    };
