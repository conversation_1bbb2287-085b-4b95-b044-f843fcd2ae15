import 'package:json_annotation/json_annotation.dart';

part 'toll_product_dto.g.dart';

@JsonSerializable()
final class TollProductDto {
  TollProductDto({
    required this.id,
    required this.description,
    required this.vehicleType,
    required this.validityType,
    required this.emissionClass,
    required this.price,
  });

  factory TollProductDto.fromJson(Map<String, dynamic> json) =>
      _$TollProductDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TollProductDtoToJson(this);

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'vehicleTypeName')
  final String vehicleType;
  @Json<PERSON>ey(name: 'validityType')
  final String validityType;
  @JsonKey(name: 'emissionClass')
  final String emissionClass;
  @Json<PERSON>ey(name: 'price')
  final double price;
}
