import 'package:json_annotation/json_annotation.dart';

part 'toll_error_dto.g.dart';

@JsonSerializable()
final class TollErrorDto {
  TollErrorDto({
    required this.code,
    required this.message,
    required this.status,
  });

  factory TollErrorDto.fromJson(Map<String, dynamic> json) =>
      _$TollErrorDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TollErrorDtoToJson(this);
  @Json<PERSON>ey(name: 'code')
  final int code;

  @Json<PERSON>ey(name: 'message')
  final String? message;

  @Json<PERSON>ey(name: 'status')
  final String? status;
}
