import 'package:json_annotation/json_annotation.dart';
import 'package:sba/src/api/model/toll/toll_sale_row_dto.dart';

part 'toll_register_dto.g.dart';

@JsonSerializable()
final class TollRegisterDto {
  TollRegisterDto({
    required this.id,
    required this.total,
    required this.currency,
    required this.createdOn,
    required this.active,
    required this.saleRows,
  });

  factory TollRegisterDto.fromJson(Map<String, dynamic> json) =>
      _$TollRegisterDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TollRegisterDtoToJson(this);

  @J<PERSON><PERSON><PERSON>(name: 'id')
  final String? id;
  @Json<PERSON>ey(name: 'total')
  final double? total;
  @Json<PERSON>ey(name: 'currency')
  final String? currency;
  @JsonKey(name: 'createdOn')
  final DateTime? createdOn;
  @JsonKey(name: 'active')
  final bool active;
  @JsonKey(name: 'saleRows')
  final List<TollSaleRowDto> saleRows;
}
