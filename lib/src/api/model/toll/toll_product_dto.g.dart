// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'toll_product_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TollProductDto _$TollProductDtoFromJson(Map<String, dynamic> json) =>
    TollProductDto(
      id: (json['id'] as num).toInt(),
      description: json['description'] as String,
      vehicleType: json['vehicleTypeName'] as String,
      validityType: json['validityType'] as String,
      emissionClass: json['emissionClass'] as String,
      price: (json['price'] as num).toDouble(),
    );

Map<String, dynamic> _$TollProductDtoToJson(TollProductDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'description': instance.description,
      'vehicleTypeName': instance.vehicleType,
      'validityType': instance.validityType,
      'emissionClass': instance.emissionClass,
      'price': instance.price,
    };
