import 'package:json_annotation/json_annotation.dart';

part 'toll_kapsch_properties_dto.g.dart';

@JsonSerializable()
final class TollKapschPropertiesDto {
  TollKapschPropertiesDto({
    required this.id,
    required this.product,
    required this.status,
    required this.vehicle,
    required this.validity,
    required this.price,
    required this.purchase,
  });

  factory TollKapschPropertiesDto.fromJson(Map<String, dynamic> json) =>
      _$TollKapschPropertiesDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TollKapschPropertiesDtoToJson(this);

  @JsonKey(name: 'id')
  final String? id;

  @<PERSON>son<PERSON>ey(name: 'product')
  final ({
    int id,
    String? vehicleType,
    String? emissionClass,
    String? validityType
  })? product;

  @JsonKey(name: 'status')
  final int status;

  @<PERSON>son<PERSON>ey(name: 'vehicle')
  final ({String? lpn, String? countryCode})? vehicle;

  @<PERSON>son<PERSON><PERSON>(name: 'validity')
  final ({
    DateTime? requestedValidityStartDate,
    DateTime? validityStartDateTimeUTC,
    DateTime? validityEndDateTimeUTC,
  })? validity;

  @JsonKey(name: 'price')
  final ({double amount, String? currency})? price;

  @JsonKey(name: 'purchase')
  final ({DateTime purchaseDateTimeUTC})? purchase;
}
