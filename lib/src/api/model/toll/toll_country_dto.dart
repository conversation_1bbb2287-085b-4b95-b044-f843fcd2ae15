
import 'package:json_annotation/json_annotation.dart';

part 'toll_country_dto.g.dart';
@JsonSerializable()
final class TollCountryDto {
  TollCountryDto({
    required this.id,
    required this.code,
    required this.name,
  });

  factory TollCountryDto.fromJson(Map<String, dynamic> json) =>
      _$TollCountryDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TollCountryDtoToJson(this);

  @JsonKey(name: 'id')
  final int id;
  @<PERSON>son<PERSON>ey(name: 'code')
  final String code;
  @Json<PERSON>ey(name: 'name')
  final String name;
}
