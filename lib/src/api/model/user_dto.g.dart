// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserDto _$UserDtoFromJson(Map<String, dynamic> json) => UserDto(
  id: (json['id'] as num).toInt(),
  firstName: json['firstName'] as String?,
  lastName: json['lastName'] as String?,
  email: json['email'] as String?,
  emailInfo: json['emailInfo'] as bool? ?? false,
  emailPromo: json['emailPromo'] as bool? ?? false,
  phone: json['mobile'] as String?,
  phoneInfo: json['mobileInfo'] as bool? ?? false,
  phonePromo: json['mobilePromo'] as bool? ?? false,
  city: (json['cityId'] as num?)?.toInt(),
  address: json['address'] as String?,
  category: (json['category'] as num?)?.toInt(),
  companyName: json['companyName'] as String?,
  companyEIK: json['companyEIK'] as String?,
  member: json['member'] == null
      ? null
      : UserDto.fromJson(json['member'] as Map<String, dynamic>),
);

Map<String, dynamic> _$UserDtoToJson(UserDto instance) => <String, dynamic>{
  'id': instance.id,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'email': instance.email,
  'emailInfo': instance.emailInfo,
  'emailPromo': instance.emailPromo,
  'mobile': instance.phone,
  'mobileInfo': instance.phoneInfo,
  'mobilePromo': instance.phonePromo,
  'cityId': instance.city,
  'address': instance.address,
  'category': instance.category,
  'companyName': instance.companyName,
  'companyEIK': instance.companyEIK,
  'member': instance.member,
};
