import 'package:json_annotation/json_annotation.dart';

part 'user_dto.g.dart';

@JsonSerializable()
final class UserDto {
  UserDto({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.emailInfo,
    required this.emailPromo,
    required this.phone,
    required this.phoneInfo,
    required this.phonePromo,
    required this.city,
    required this.address,
    required this.category,
    required this.companyName,
    required this.companyEIK,
    required this.member,
  });

  factory UserDto.fromJson(Map<String, dynamic> json) =>
      _$UserDtoFromJson(json);

  Map<String, dynamic> toJson() => _$UserDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  final int id;
  @Json<PERSON><PERSON>(name: 'firstName')
  final String? firstName;
  @JsonKey(name: 'lastName')
  final String? lastName;
  @JsonKey(name: 'email')
  final String? email;
  @<PERSON>son<PERSON>ey(name: 'emailInfo', defaultValue: false)
  final bool emailInfo;
  @Json<PERSON>ey(name: 'emailPromo', defaultValue: false)
  final bool emailPromo;
  @JsonKey(name: 'mobile')
  final String? phone;
  @JsonKey(name: 'mobileInfo', defaultValue: false)
  final bool phoneInfo;
  @JsonKey(name: 'mobilePromo', defaultValue: false)
  final bool phonePromo;
  @JsonKey(name: 'cityId')
  final int? city;
  @JsonKey(name: 'address')
  final String? address;
  @JsonKey(name: 'category')
  final int? category;
  @JsonKey(name: 'companyName')
  final String? companyName;
  @JsonKey(name: 'companyEIK')
  final String? companyEIK;
  @JsonKey(name: 'member')
  final UserDto? member;
}
