import 'package:json_annotation/json_annotation.dart';

part 'road_assistance_reason_dto.g.dart';

@JsonSerializable()
final class RoadAssistanceReasonDto {
  RoadAssistanceReasonDto({
    required this.id,
    required this.reasonBg,
    this.reasonEn,
  });

  factory RoadAssistanceReasonDto.fromJson(Map<String, dynamic> json) =>
      _$RoadAssistanceReasonDtoFromJson(json);

  Map<String, dynamic> toJson() => _$RoadAssistanceReasonDtoToJson(this);

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'reasonBg')
  final String reasonBg;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'reasonEn')
  final String? reasonEn;
}
