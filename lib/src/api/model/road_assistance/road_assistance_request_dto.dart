import 'package:json_annotation/json_annotation.dart';

part 'road_assistance_request_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class RoadAssistanceRequestDto {
  RoadAssistanceRequestDto({
    required this.id,
    required this.latitude,
    required this.longitude,
    required this.reasonId,
    required this.driverName,
    required this.driverPhone,
    required this.carMakerId,
    required this.carModelId,
    required this.carDkn,
    required this.carTransmissionType,
    required this.carDriveType,
    required this.note,
    required this.created,
    required this.smartViewUrl,
    required this.estimatedArrivalTime,
    required this.status,
  });

  factory RoadAssistanceRequestDto.fromJson(Map<String, dynamic> json) =>
      _$RoadAssistanceRequestDtoFromJson(json);

  Map<String, dynamic> toJson() => _$RoadAssistanceRequestDtoToJson(this);

  @JsonKey(name: 'id')
  final int? id;
  @Json<PERSON>ey(name: 'latitude')
  final double? latitude;
  @Json<PERSON>ey(name: 'longitude')
  final double? longitude;
  @Json<PERSON>ey(name: 'reasonId')
  final int reasonId;
  @Json<PERSON>ey(name: 'driverName')
  final String? driverName;
  @JsonKey(name: 'driverPhone')
  final String? driverPhone;
  @JsonKey(name: 'carMakerId')
  final int carMakerId;
  @JsonKey(name: 'carModelId')
  final int carModelId;
  @JsonKey(name: 'carDkn')
  final String carDkn;
  @JsonKey(name: 'carTransmissionType')
  final int carTransmissionType;
  @JsonKey(name: 'carDriveType')
  final int carDriveType;
  @JsonKey(name: 'note')
  final String? note;
  @JsonKey(name: 'created')
  final DateTime? created;
  @JsonKey(name: 'smartViewUrl')
  final String? smartViewUrl;
  @JsonKey(name: 'estimatedArrivalTime')
  final DateTime? estimatedArrivalTime;
  @JsonKey(name: 'status')
  final int? status;
}
