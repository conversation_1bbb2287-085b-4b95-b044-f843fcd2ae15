// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'road_assistance_request_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RoadAssistanceRequestDto _$RoadAssistanceRequestDtoFromJson(
  Map<String, dynamic> json,
) => RoadAssistanceRequestDto(
  id: (json['id'] as num?)?.toInt(),
  latitude: (json['latitude'] as num?)?.toDouble(),
  longitude: (json['longitude'] as num?)?.toDouble(),
  reasonId: (json['reasonId'] as num).toInt(),
  driverName: json['driverName'] as String?,
  driverPhone: json['driverPhone'] as String?,
  carMakerId: (json['carMakerId'] as num).toInt(),
  carModelId: (json['carModelId'] as num).toInt(),
  carDkn: json['carDkn'] as String,
  carTransmissionType: (json['carTransmissionType'] as num).toInt(),
  carDriveType: (json['carDriveType'] as num).toInt(),
  note: json['note'] as String?,
  created: json['created'] == null
      ? null
      : DateTime.parse(json['created'] as String),
  smartViewUrl: json['smartViewUrl'] as String?,
  estimatedArrivalTime: json['estimatedArrivalTime'] == null
      ? null
      : DateTime.parse(json['estimatedArrivalTime'] as String),
  status: (json['status'] as num?)?.toInt(),
);

Map<String, dynamic> _$RoadAssistanceRequestDtoToJson(
  RoadAssistanceRequestDto instance,
) => <String, dynamic>{
  'id': ?instance.id,
  'latitude': ?instance.latitude,
  'longitude': ?instance.longitude,
  'reasonId': instance.reasonId,
  'driverName': ?instance.driverName,
  'driverPhone': ?instance.driverPhone,
  'carMakerId': instance.carMakerId,
  'carModelId': instance.carModelId,
  'carDkn': instance.carDkn,
  'carTransmissionType': instance.carTransmissionType,
  'carDriveType': instance.carDriveType,
  'note': ?instance.note,
  'created': ?instance.created?.toIso8601String(),
  'smartViewUrl': ?instance.smartViewUrl,
  'estimatedArrivalTime': ?instance.estimatedArrivalTime?.toIso8601String(),
  'status': ?instance.status,
};
