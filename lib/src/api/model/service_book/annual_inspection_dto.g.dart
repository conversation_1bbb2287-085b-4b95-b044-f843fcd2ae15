// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'annual_inspection_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AnnualInspectionDto _$AnnualInspectionDtoFromJson(Map<String, dynamic> json) =>
    AnnualInspectionDto(
      id: (json['id'] as num?)?.toInt(),
      vehicleId: (json['carId'] as num?)?.toInt(),
      date: DateTime.parse(json['date'] as String),
      end: DateTime.parse(json['end'] as String),
      durationMonths: (json['durationMonths'] as num?)?.toInt(),
      station: json['station'] as String,
      mileage: (json['mileage'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      notes: json['note'] as String?,
    );

Map<String, dynamic> _$AnnualInspectionDtoToJson(
  AnnualInspectionDto instance,
) => <String, dynamic>{
  'id': instance.id,
  'carId': instance.vehicleId,
  'date': instance.date.toIso8601String(),
  'end': instance.end.toIso8601String(),
  'durationMonths': instance.durationMonths,
  'station': instance.station,
  'mileage': instance.mileage,
  'price': instance.price,
  'note': instance.notes,
};
