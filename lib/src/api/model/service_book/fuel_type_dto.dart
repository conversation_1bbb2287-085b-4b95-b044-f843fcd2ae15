import 'package:json_annotation/json_annotation.dart';

part 'fuel_type_dto.g.dart';

@JsonSerializable()
final class FuelTypeDto {
  FuelTypeDto({required this.id, required this.fuel});

  factory FuelTypeDto.fromJson(Map<String, dynamic> json) =>
      _$FuelTypeDtoFromJson(json);

  Map<String, dynamic> toJson() => _$FuelTypeDtoToJson(this);

  @JsonKey(name: 'id')
  final int id;

  @JsonKey(name: 'fuel')
  final String fuel;
}
