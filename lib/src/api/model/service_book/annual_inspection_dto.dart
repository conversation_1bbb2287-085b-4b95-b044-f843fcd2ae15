import 'package:json_annotation/json_annotation.dart';

part 'annual_inspection_dto.g.dart';

@JsonSerializable()
final class AnnualInspectionDto {
  AnnualInspectionDto({
    required this.id,
    required this.vehicleId,
    required this.date,
    required this.end,
    required this.durationMonths,
    required this.station,
    required this.mileage,
    required this.price,
    required this.notes,
  });

  factory AnnualInspectionDto.fromJson(Map<String, dynamic> json) =>
      _$AnnualInspectionDtoFromJson(json);

  Map<String, dynamic> toJson() => _$AnnualInspectionDtoToJson(this);

  @JsonKey(name: 'id')
  final int? id;
  @<PERSON>son<PERSON>ey(name: 'carId')
  final int? vehicleId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'date')
  final DateTime date;
  @JsonKey(name: 'end')
  final DateTime end;
  @<PERSON>son<PERSON>ey(name: 'durationMonths')
  final int? durationMonths;
  @JsonKey(name: 'station')
  final String station;
  @<PERSON>sonKey(name: 'mileage')
  final int mileage;
  @<PERSON>sonKey(name: 'price')
  final double price;
  @Json<PERSON>ey(name: 'note')
  final String? notes;
}
