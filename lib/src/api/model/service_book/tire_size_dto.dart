import 'package:json_annotation/json_annotation.dart';

part 'tire_size_dto.g.dart';

@JsonSerializable()
final class TireSizeDto {
  TireSizeDto({required this.size, required this.type});

  factory TireSizeDto.fromJson(Map<String, dynamic> json) =>
      _$TireSizeDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TireSizeDtoToJson(this);

  @Json<PERSON>ey(name: 'size')
  final double size;

  @Json<PERSON>ey(name: 'type')
  final int type;
}
