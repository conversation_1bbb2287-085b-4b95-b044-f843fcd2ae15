import 'package:json_annotation/json_annotation.dart';

part 'vehicle_tyre_swap_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class VehicleTyreSwapDto {
  VehicleTyreSwapDto({
    required this.tyresId,
    required this.autoservice,
    required this.switchDate,
    this.id,
    this.vehicleId,
  });

  factory VehicleTyreSwapDto.fromJson(Map<String, dynamic> json) =>
      _$VehicleTyreSwapDtoFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleTyreSwapDtoToJson(this);
  @JsonKey(name: 'id')
  final int? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'carId')
  final int? vehicleId;
  @J<PERSON><PERSON><PERSON>(name: 'tyresId')
  final int tyresId;
  @JsonKey(name: 'autoservice')
  final String autoservice;
  @Json<PERSON>ey(name: 'switchDate')
  final DateTime switchDate;
}
