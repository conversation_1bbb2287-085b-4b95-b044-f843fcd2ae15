import 'package:json_annotation/json_annotation.dart';

part 'fuel_traders_dto.g.dart';

@JsonSerializable()
final class FuelTradersDto {
  FuelTradersDto({
    required this.id,
    required this.name,
    required this.fuels,
  });

  factory FuelTradersDto.fromJson(Map<String, dynamic> json) =>
      _$FuelTradersDtoFromJson(json);

  Map<String, dynamic> toJson() => _$FuelTradersDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  final int id;
  @JsonKey(name: 'name')
  final String name;
  @JsonKey(name: 'fuels')
  final List<FuelDto> fuels;
}

@JsonSerializable()
final class FuelDto {
  FuelDto({
    required this.id,
    required this.name,
    required this.typeId,
    required this.type,
  });

  factory FuelDto.fromJson(Map<String, dynamic> json) =>
      _$FuelDtoFromJson(json);

  Map<String, dynamic> toJson() => _$FuelDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  final int id;
  @Json<PERSON>ey(name: 'name')
  final String name;
  @Json<PERSON>ey(name: 'typeId')
  final int typeId;
  @Json<PERSON>ey(name: 'type')
  final String type;
}
