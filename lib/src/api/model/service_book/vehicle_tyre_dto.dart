import 'package:json_annotation/json_annotation.dart';

part 'vehicle_tyre_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class VehicleTyreDto {
  VehicleTyreDto({
    required this.tyreBrandId,
    required this.tyreType,
    required this.tyreModel,
    required this.tyreDot,
    required this.buyDate,
    required this.mileage,
    required this.dealer,
    required this.tyreWidth,
    required this.tyreHeight,
    required this.tyreDiameter,
    this.id,
    this.vehicleId,
  });

  factory VehicleTyreDto.fromJson(Map<String, dynamic> json) =>
      _$VehicleTyreDtoFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleTyreDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  final int? id;
  @Json<PERSON>ey(name: 'carId')
  final int? vehicleId;
  @<PERSON>son<PERSON>ey(name: 'tyreBrandId')
  final int tyreBrandId;
  @Json<PERSON>ey(name: 'tyreType')
  final int tyreType;
  @Json<PERSON>ey(name: 'tyreModel')
  final String tyreModel;
  @Json<PERSON>ey(name: 'tyreDot')
  final String tyreDot;
  @Json<PERSON>ey(name: 'buyDate')
  final DateTime buyDate;
  @JsonKey(name: 'carMileage')
  final int mileage;
  @JsonKey(name: 'dealer')
  final String dealer;
  @JsonKey(name: 'tyreWidth')
  final double tyreWidth;
  @JsonKey(name: 'tyreHeight')
  final double tyreHeight;
  @JsonKey(name: 'tyreDiameter')
  final double tyreDiameter;
}
