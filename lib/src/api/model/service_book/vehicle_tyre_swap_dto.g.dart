// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_tyre_swap_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VehicleTyreSwapDto _$VehicleTyreSwapDtoFromJson(Map<String, dynamic> json) =>
    VehicleTyreSwapDto(
      tyresId: (json['tyresId'] as num).toInt(),
      autoservice: json['autoservice'] as String,
      switchDate: DateTime.parse(json['switchDate'] as String),
      id: (json['id'] as num?)?.toInt(),
      vehicleId: (json['carId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$VehicleTyreSwapDtoToJson(VehicleTyreSwapDto instance) =>
    <String, dynamic>{
      'id': ?instance.id,
      'carId': ?instance.vehicleId,
      'tyresId': instance.tyresId,
      'autoservice': instance.autoservice,
      'switchDate': instance.switchDate.toIso8601String(),
    };
