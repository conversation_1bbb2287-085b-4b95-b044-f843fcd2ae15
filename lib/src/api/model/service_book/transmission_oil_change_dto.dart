import 'package:json_annotation/json_annotation.dart';

part 'transmission_oil_change_dto.g.dart';

@JsonSerializable()
final class TransmissionOilChangeDto {
  TransmissionOilChangeDto({
    required this.changeDate,
    required this.autoService,
    required this.oilType,
    required this.oilQuantity,
    required this.mileage,
    required this.price,
    this.id,
    this.vehicleId,
    this.notes,
  });

  factory TransmissionOilChangeDto.fromJson(Map<String, dynamic> json) =>
      _$TransmissionOilChangeDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TransmissionOilChangeDtoToJson(this);

  @<PERSON>son<PERSON>ey(name: 'id')
  final int? id;
  @Json<PERSON>ey(name: 'carId')
  final int? vehicleId;
  @JsonKey(name: 'changeDate')
  final DateTime changeDate;
  @Json<PERSON>ey(name: 'autoservice')
  final String autoService;
  @JsonKey(name: 'oilType')
  final String oilType;
  @JsonKey(name: 'oilQuantity')
  final double oilQuantity;
  @Json<PERSON>ey(name: 'mileage')
  final int mileage;
  @Json<PERSON>ey(name: 'price')
  final double price;
  @Json<PERSON>ey(name: 'notes')
  final String? notes;
}
