// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_refuel_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VehicleRefuelDto _$VehicleRefuelDtoFromJson(Map<String, dynamic> json) =>
    VehicleRefuelDto(
      id: (json['id'] as num?)?.toInt(),
      vehicleId: (json['carId'] as num?)?.toInt(),
      date: DateTime.parse(json['date'] as String),
      traderId: (json['traderId'] as num).toInt(),
      fuelId: (json['fuelId'] as num).toInt(),
      quantity: (json['quantity'] as num).toDouble(),
      mileage: (json['mileage'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      value: (json['value'] as num).toDouble(),
      notes: json['note'] as String?,
    );

Map<String, dynamic> _$VehicleRefuelDtoToJson(VehicleRefuelDto instance) =>
    <String, dynamic>{
      'id': ?instance.id,
      'carId': ?instance.vehicleId,
      'date': instance.date.toIso8601String(),
      'traderId': instance.traderId,
      'fuelId': instance.fuelId,
      'quantity': instance.quantity,
      'mileage': instance.mileage,
      'price': instance.price,
      'value': instance.value,
      'note': ?instance.notes,
    };
