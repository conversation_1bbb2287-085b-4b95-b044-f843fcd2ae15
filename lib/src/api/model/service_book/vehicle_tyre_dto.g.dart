// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_tyre_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VehicleTyreDto _$VehicleTyreDtoFromJson(Map<String, dynamic> json) =>
    VehicleTyreDto(
      tyreBrandId: (json['tyreBrandId'] as num).toInt(),
      tyreType: (json['tyreType'] as num).toInt(),
      tyreModel: json['tyreModel'] as String,
      tyreDot: json['tyreDot'] as String,
      buyDate: DateTime.parse(json['buyDate'] as String),
      mileage: (json['carMileage'] as num).toInt(),
      dealer: json['dealer'] as String,
      tyreWidth: (json['tyreWidth'] as num).toDouble(),
      tyreHeight: (json['tyreHeight'] as num).toDouble(),
      tyreDiameter: (json['tyreDiameter'] as num).toDouble(),
      id: (json['id'] as num?)?.toInt(),
      vehicleId: (json['carId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$VehicleTyreDtoToJson(VehicleTyreDto instance) =>
    <String, dynamic>{
      'id': ?instance.id,
      'carId': ?instance.vehicleId,
      'tyreBrandId': instance.tyreBrandId,
      'tyreType': instance.tyreType,
      'tyreModel': instance.tyreModel,
      'tyreDot': instance.tyreDot,
      'buyDate': instance.buyDate.toIso8601String(),
      'carMileage': instance.mileage,
      'dealer': instance.dealer,
      'tyreWidth': instance.tyreWidth,
      'tyreHeight': instance.tyreHeight,
      'tyreDiameter': instance.tyreDiameter,
    };
