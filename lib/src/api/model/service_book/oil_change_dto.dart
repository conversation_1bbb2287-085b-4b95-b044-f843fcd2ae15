import 'package:json_annotation/json_annotation.dart';

part 'oil_change_dto.g.dart';

@JsonSerializable()
final class OilChangeDto {
  OilChangeDto({
    required this.changeDate,
    required this.autoService,
    required this.oilType,
    required this.oilQuantity,
    required this.mileage,
    required this.oilFilterIsChanged,
    required this.airFilterIsChanged,
    required this.cabinFilterIsChanged,
    required this.fuelFilterIsChanged,
    required this.price,
    this.id,
    this.vehicleId,
    this.notes,
  });

  factory OilChangeDto.fromJson(Map<String, dynamic> json) =>
      _$OilChangeDtoFromJson(json);

  Map<String, dynamic> toJson() => _$OilChangeDtoToJson(this);

  @JsonKey(name: 'id')
  final int? id;
  @JsonKey(name: 'carId')
  final int? vehicleId;
  @JsonKey(name: 'changeDate')
  final DateTime changeDate;
  @<PERSON>son<PERSON>ey(name: 'autoservice')
  final String autoService;
  @<PERSON>son<PERSON>ey(name: 'oilType')
  final String oilType;
  @JsonKey(name: 'oilQuantity')
  final double oilQuantity;
  @JsonKey(name: 'mileage')
  final int mileage;
  @JsonKey(name: 'oilFilterIsChanged')
  final bool oilFilterIsChanged;
  @JsonKey(name: 'airFilterIsChanged')
  final bool airFilterIsChanged;
  @JsonKey(name: 'cabinFilterIsChanged')
  final bool cabinFilterIsChanged;
  @JsonKey(name: 'fuelFilterIsChanged')
  final bool fuelFilterIsChanged;
  @JsonKey(name: 'price')
  final double price;
  @JsonKey(name: 'notes')
  final String? notes;
}
