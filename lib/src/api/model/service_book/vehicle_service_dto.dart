import 'package:json_annotation/json_annotation.dart';

part 'vehicle_service_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class VehicleServiceDto {
  VehicleServiceDto({
    required this.name,
    required this.serviceDate,
    this.id,
    this.vehicleId,
  });

  factory VehicleServiceDto.fromJson(Map<String, dynamic> json) =>
      _$VehicleServiceDtoFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleServiceDtoToJson(this);

  @JsonKey(name: 'id')
  final int? id;
  @<PERSON>son<PERSON>ey(name: 'carId')
  final int? vehicleId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  final String name;
  @Json<PERSON>ey(name: 'serviceDate')
  final DateTime serviceDate;
}
