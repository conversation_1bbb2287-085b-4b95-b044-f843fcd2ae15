import 'package:json_annotation/json_annotation.dart';

part 'tyre_brand_dto.g.dart';

@JsonSerializable()
final class TyreBrandDto {
  TyreBrandDto({
    required this.id,
    required this.brand,
  });

  factory TyreBrandDto.fromJson(Map<String, dynamic> json) =>
      _$TyreBrandDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TyreBrandDtoToJson(this);

  @JsonKey(name: 'id')
  final int id;

  @JsonKey(name: 'brand')
  final String brand;
}
