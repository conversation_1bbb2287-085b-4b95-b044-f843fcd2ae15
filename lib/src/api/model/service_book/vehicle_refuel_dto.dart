import 'package:json_annotation/json_annotation.dart';

part 'vehicle_refuel_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class VehicleRefuelDto {
  VehicleRefuelDto({
    required this.id,
    required this.vehicleId,
    required this.date,
    required this.traderId,
    required this.fuelId,
    required this.quantity,
    required this.mileage,
    required this.price,
    required this.value,
    required this.notes,
  });

  factory VehicleRefuelDto.fromJson(Map<String, dynamic> json) =>
      _$VehicleRefuelDtoFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleRefuelDtoToJson(this);

  @Json<PERSON><PERSON>(name: 'id')
  final int? id;
  @Json<PERSON>ey(name: 'carId')
  final int? vehicleId;
  @Json<PERSON>ey(name: 'date')
  final DateTime date;
  @JsonKey(name: 'traderId')
  final int traderId;
  @JsonKey(name: 'fuelId')
  final int fuelId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'quantity')
  final double quantity;
  @Json<PERSON>ey(name: 'mileage')
  final int mileage;
  @Json<PERSON>ey(name: 'price')
  final double price;
  @J<PERSON><PERSON>ey(name: 'value')
  final double value;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'note')
  final String? notes;
}
