// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_product_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubscriptionProductDto _$SubscriptionProductDtoFromJson(
  Map<String, dynamic> json,
) => SubscriptionProductDto(
  id: (json['id'] as num).toInt(),
  category: (json['category'] as num).toInt(),
  type: (json['type'] as num).toInt(),
  total: (json['total'] as num).toDouble(),
  packages: (json['packages'] as List<dynamic>)
      .map(
        (e) => _$recordConvert(
          e,
          ($jsonValue) => (
            id: ($jsonValue['id'] as num).toInt(),
            name: $jsonValue['name'] as String,
            promo: $jsonValue['promo'] as bool,
            total: ($jsonValue['total'] as num).toDouble(),
          ),
        ),
      )
      .toList(),
  coverages: (json['coverages'] as List<dynamic>)
      .map(
        (e) => _$recordConvert(
          e,
          ($jsonValue) => (
            coverage: $jsonValue['coverage'] as String,
            coverageText: $jsonValue['coverageText'] as String,
            id: ($jsonValue['id'] as num).toInt(),
          ),
        ),
      )
      .toList(),
);

Map<String, dynamic> _$SubscriptionProductDtoToJson(
  SubscriptionProductDto instance,
) => <String, dynamic>{
  'id': instance.id,
  'category': instance.category,
  'type': instance.type,
  'total': instance.total,
  'packages': instance.packages
      .map(
        (e) => <String, dynamic>{
          'id': e.id,
          'name': e.name,
          'promo': e.promo,
          'total': e.total,
        },
      )
      .toList(),
  'coverages': instance.coverages
      .map(
        (e) => <String, dynamic>{
          'coverage': e.coverage,
          'coverageText': e.coverageText,
          'id': e.id,
        },
      )
      .toList(),
};

$Rec _$recordConvert<$Rec>(Object? value, $Rec Function(Map) convert) =>
    convert(value as Map<String, dynamic>);
