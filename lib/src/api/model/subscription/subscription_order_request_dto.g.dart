// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_order_request_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubscriptionOrderRequestDto _$SubscriptionOrderRequestDtoFromJson(
  Map<String, dynamic> json,
) => SubscriptionOrderRequestDto(
  id: (json['id'] as num?)?.toInt(),
  carId: (json['carId'] as num).toInt(),
  oldCardId: (json['oldCardId'] as num?)?.toInt(),
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  email: json['email'] as String,
  mobile: json['mobile'] as String,
  cityId: (json['cityId'] as num).toInt(),
  address: json['address'] as String,
  cardCategory: (json['cardCategory'] as num).toInt(),
  cardType: (json['cardType'] as num).toInt(),
  status: (json['status'] as num?)?.toInt(),
  note: json['note'] as String?,
  delivery: DeliveryRequestDto.fromJson(
    json['delivery'] as Map<String, dynamic>,
  ),
  selectedPackageIds: (json['selectedPackageIds'] as List<dynamic>?)
      ?.map((e) => (e as num).toInt())
      .toList(),
  paymentType: (json['paymentType'] as num).toInt(),
  paymentStatus: (json['paymentStatus'] as num?)?.toInt(),
);

Map<String, dynamic> _$SubscriptionOrderRequestDtoToJson(
  SubscriptionOrderRequestDto instance,
) => <String, dynamic>{
  'id': ?instance.id,
  'carId': instance.carId,
  'oldCardId': ?instance.oldCardId,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'email': instance.email,
  'mobile': instance.mobile,
  'cityId': instance.cityId,
  'address': instance.address,
  'cardCategory': instance.cardCategory,
  'cardType': instance.cardType,
  'status': ?instance.status,
  'note': ?instance.note,
  'delivery': instance.delivery,
  'selectedPackageIds': ?instance.selectedPackageIds,
  'paymentType': instance.paymentType,
  'paymentStatus': ?instance.paymentStatus,
};
