// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subscription_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SubscriptionDto _$SubscriptionDtoFromJson(Map<String, dynamic> json) =>
    SubscriptionDto(
      id: (json['id'] as num).toInt(),
      cardNumber: json['cardNumber'] as String,
      memberId: (json['memberId'] as num).toInt(),
      carId: (json['carId'] as num).toInt(),
      dateFrom: DateTime.parse(json['dateFrom'] as String),
      dateTo: DateTime.parse(json['dateTo'] as String),
      cardType: (json['cardType'] as num).toInt(),
      cardCategory: (json['cardCategory'] as num).toInt(),
    );

Map<String, dynamic> _$SubscriptionDtoToJson(SubscriptionDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'cardNumber': instance.cardNumber,
      'memberId': instance.memberId,
      'carId': instance.carId,
      'dateFrom': instance.dateFrom.toIso8601String(),
      'dateTo': instance.dateTo.toIso8601String(),
      'cardType': instance.cardType,
      'cardCategory': instance.cardCategory,
    };
