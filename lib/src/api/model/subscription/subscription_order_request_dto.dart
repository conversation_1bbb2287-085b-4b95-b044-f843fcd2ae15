// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:json_annotation/json_annotation.dart';
import 'package:sba/src/api/model/request/delivery_request_dto.dart';

part 'subscription_order_request_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class SubscriptionOrderRequestDto {
  const SubscriptionOrderRequestDto({
    required this.id,
    required this.carId,
    required this.oldCardId,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.mobile,
    required this.cityId,
    required this.address,
    required this.cardCategory,
    required this.cardType,
    required this.status,
    required this.note,
    required this.delivery,
    required this.selectedPackageIds,
    required this.paymentType,
    required this.paymentStatus,
  });

  factory SubscriptionOrderRequestDto.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionOrderRequestDtoFromJson(json);

  Map<String, dynamic> toJson() => _$SubscriptionOrderRequestDtoToJson(this);
  @Json<PERSON>ey(name: 'id')
  final int? id;
  @J<PERSON><PERSON><PERSON>(name: 'carId')
  final int carId;
  @J<PERSON><PERSON><PERSON>(name: 'oldCardId')
  final int? oldCardId;
  @JsonKey(name: 'firstName')
  final String firstName;
  @JsonKey(name: 'lastName')
  final String lastName;
  @JsonKey(name: 'email')
  final String email;
  @JsonKey(name: 'mobile')
  final String mobile;
  @JsonKey(name: 'cityId')
  final int cityId;
  @JsonKey(name: 'address')
  final String address;
  @JsonKey(name: 'cardCategory')
  final int cardCategory;
  @JsonKey(name: 'cardType')
  final int cardType;
  @JsonKey(name: 'status')
  final int? status;
  @JsonKey(name: 'note')
  final String? note;
  @JsonKey(name: 'delivery')
  final DeliveryRequestDto delivery;
  @JsonKey(name: 'selectedPackageIds')
  final List<int>? selectedPackageIds;
  @JsonKey(name: 'paymentType')
  final int paymentType;
  @JsonKey(name: 'paymentStatus')
  final int? paymentStatus;
}
