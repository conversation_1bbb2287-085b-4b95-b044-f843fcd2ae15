import 'package:json_annotation/json_annotation.dart';

part 'subscription_product_dto.g.dart';

@JsonSerializable()
final class SubscriptionProductDto {
  SubscriptionProductDto({
    required this.id,
    required this.category,
    required this.type,
    required this.total,
    required this.packages,
    required this.coverages,
  });

  factory SubscriptionProductDto.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionProductDtoFromJson(json);

  Map<String, dynamic> toJson() => _$SubscriptionProductDtoToJson(this);

  @<PERSON>son<PERSON>ey(name: 'id')
  final int id;
  @<PERSON>sonKey(name: 'category')
  final int category;
  @<PERSON>son<PERSON>ey(name: 'type')
  final int type;
  @<PERSON>son<PERSON>ey(name: 'total')
  final double total;
  @<PERSON>son<PERSON>ey(name: 'packages')
  final List<({int id, String name, double total, bool promo})> packages;
  @Json<PERSON>ey(name: 'coverages')
  final List<({int id, String coverage, String coverageText})> coverages;
}
