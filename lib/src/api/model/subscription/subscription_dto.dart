import 'package:json_annotation/json_annotation.dart';

part 'subscription_dto.g.dart';

@JsonSerializable()
class SubscriptionDto {
  SubscriptionDto({
    required this.id,
    required this.cardNumber,
    required this.memberId,
    required this.carId,
    required this.dateFrom,
    required this.dateTo,
    required this.cardType,
    required this.cardCategory,
  });

  factory SubscriptionDto.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionDtoFromJson(json);

  Map<String, dynamic> toJson() => _$SubscriptionDtoToJson(this);


  @<PERSON>son<PERSON><PERSON>(name: 'id')
  int id;
  @Json<PERSON>ey(name: 'cardNumber')
  String cardNumber;
  @Json<PERSON><PERSON>(name: 'memberId')
  int memberId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'carId')
  int carId;
  @JsonKey(name: 'dateFrom')
  DateTime dateFrom;
  @Json<PERSON>ey(name: 'dateTo')
  DateTime dateTo;
  @Json<PERSON>ey(name: 'cardType')
  int cardType;
  @Json<PERSON><PERSON>(name: 'cardCategory')
  int cardCategory;

}
