import 'package:json_annotation/json_annotation.dart';

part 'notification_dto.g.dart';

@JsonSerializable()
final class NotificationDto {
  NotificationDto({
    required this.id,
    required this.userId,
    required this.title,
    required this.text,
    required this.created,
    required this.seen,
    this.sended,
    this.eol,
  });

  factory NotificationDto.fromJson(Map<String, dynamic> json) =>
      _$NotificationDtoFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'userId')
  final String userId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'title')
  final String title;
  @Json<PERSON>ey(name: 'text')
  final String text;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created')
  final DateTime created;
  @Json<PERSON>ey(name: 'sended')
  final DateTime? sended;
  @Json<PERSON>ey(name: 'eol')
  final DateTime? eol;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'seen')
  final bool seen;
}
