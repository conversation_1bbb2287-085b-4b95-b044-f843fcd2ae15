// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationDto _$NotificationDtoFromJson(Map<String, dynamic> json) =>
    NotificationDto(
      id: (json['id'] as num).toInt(),
      userId: json['userId'] as String,
      title: json['title'] as String,
      text: json['text'] as String,
      created: DateTime.parse(json['created'] as String),
      seen: json['seen'] as bool,
      sended: json['sended'] == null
          ? null
          : DateTime.parse(json['sended'] as String),
      eol: json['eol'] == null ? null : DateTime.parse(json['eol'] as String),
    );

Map<String, dynamic> _$NotificationDtoToJson(NotificationDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'title': instance.title,
      'text': instance.text,
      'created': instance.created.toIso8601String(),
      'sended': instance.sended?.toIso8601String(),
      'eol': instance.eol?.toIso8601String(),
      'seen': instance.seen,
    };
