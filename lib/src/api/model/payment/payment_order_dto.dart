import 'package:json_annotation/json_annotation.dart';

part 'payment_order_dto.g.dart';

@JsonSerializable()
final class PaymentOrderDto {
  PaymentOrderDto({
    this.orderId,
    this.orderNumber,
    this.formUrl,
    this.errorCode,
    this.errorMessage,
  });

  factory PaymentOrderDto.fromJson(Map<String, dynamic> json) =>
      _$PaymentOrderDtoFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentOrderDtoToJson(this);

  @Json<PERSON><PERSON>(name: 'orderId')
  final String? orderId;
  @Json<PERSON><PERSON>(name: 'orderNumber')
  final String? orderNumber;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'formUrl')
  final String? formUrl;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'errorCode')
  final String? errorCode;
  @Json<PERSON>ey(name: 'errorMessage')
  final String? errorMessage;
}
