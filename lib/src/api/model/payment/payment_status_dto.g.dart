// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_status_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentStatusDto _$PaymentStatusDtoFromJson(Map<String, dynamic> json) =>
    PaymentStatusDto(
      errorCode: json['errorCode'] as String?,
      errorMessage: json['errorMessage'] as String?,
      orderNumber: json['orderNumber'] as String?,
      amount: json['amount'] as String?,
      currency: json['currency'] as String?,
      orderStatus: (json['orderStatus'] as num?)?.toInt(),
      dateUtc: json['dateUtc'] == null
          ? null
          : DateTime.parse(json['dateUtc'] as String),
    );

Map<String, dynamic> _$PaymentStatusDtoToJson(PaymentStatusDto instance) =>
    <String, dynamic>{
      'errorCode': instance.errorCode,
      'errorMessage': instance.errorMessage,
      'orderNumber': instance.orderNumber,
      'amount': instance.amount,
      'currency': instance.currency,
      'orderStatus': instance.orderStatus,
      'dateUtc': instance.dateUtc?.toIso8601String(),
    };
