import 'package:json_annotation/json_annotation.dart';

part 'payment_status_dto.g.dart';

@JsonSerializable()
final class PaymentStatusDto {
  PaymentStatusDto({
    this.errorCode,
    this.errorMessage,
    this.orderNumber,
    this.amount,
    this.currency,
    this.orderStatus,
    this.dateUtc,
  });

  factory PaymentStatusDto.fromJson(Map<String, dynamic> json) =>
      _$PaymentStatusDtoFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentStatusDtoToJson(this);

  @<PERSON>son<PERSON>ey(name: 'errorCode')
  final String? errorCode;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'errorMessage')
  final String? errorMessage;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'orderNumber')
  final String? orderNumber;
  @JsonKey(name: 'amount')
  final String? amount;
  @<PERSON>son<PERSON><PERSON>(name: 'currency')
  final String? currency;
  @JsonKey(name: 'orderStatus')
  final int? orderStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'dateUtc')
  final DateTime? dateUtc;
}
