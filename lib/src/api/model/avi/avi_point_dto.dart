import 'package:json_annotation/json_annotation.dart';

part 'avi_point_dto.g.dart';

@JsonSerializable()
class AVIPointDto {
  AVIPointDto({
    required this.id,
    required this.nameBg,
    required this.cityBg,
    required this.addressBg,
    required this.lat,
    required this.lng,
    required this.freeCalendarSlots,
    required this.pricing,
  });

  factory AVIPointDto.fromJson(Map<String, dynamic> json) => _$AVIPointDtoFromJson(json);

  Map<String, dynamic> toJson() => _$AVIPointDtoToJson(this);

  @JsonKey(name: 'id')
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'nameBg')
  final String? nameBg;
  @Json<PERSON><PERSON>(name: 'cityBg')
  final String? cityBg;
  @Json<PERSON>ey(name: 'addressBg')
  final String? addressBg;
  @Json<PERSON>ey(name: 'lat')
  final double lat;
  @Json<PERSON>ey(name: 'lng')
  final double lng;
  @<PERSON>son<PERSON>ey(name: 'freeCalendarSlots')
  final List<({int lineId, String slot})>? freeCalendarSlots;
  @<PERSON><PERSON><PERSON>ey(name: 'pricing')
  final ({double gtpPrice})? pricing;
}
