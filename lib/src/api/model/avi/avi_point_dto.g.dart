// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'avi_point_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AVIPointDto _$AVIPointDtoFromJson(Map<String, dynamic> json) => AVIPointDto(
  id: (json['id'] as num).toInt(),
  nameBg: json['nameBg'] as String?,
  cityBg: json['cityBg'] as String?,
  addressBg: json['addressBg'] as String?,
  lat: (json['lat'] as num).toDouble(),
  lng: (json['lng'] as num).toDouble(),
  freeCalendarSlots: (json['freeCalendarSlots'] as List<dynamic>?)
      ?.map(
        (e) => _$recordConvert(
          e,
          ($jsonValue) => (
            lineId: ($jsonValue['lineId'] as num).toInt(),
            slot: $jsonValue['slot'] as String,
          ),
        ),
      )
      .toList(),
  pricing: _$recordConvertNullable(
    json['pricing'],
    ($jsonValue) => (gtpPrice: ($jsonValue['gtpPrice'] as num).toDouble()),
  ),
);

Map<String, dynamic> _$AVIPointDtoToJson(AVIPointDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nameBg': instance.nameBg,
      'cityBg': instance.cityBg,
      'addressBg': instance.addressBg,
      'lat': instance.lat,
      'lng': instance.lng,
      'freeCalendarSlots': instance.freeCalendarSlots
          ?.map((e) => <String, dynamic>{'lineId': e.lineId, 'slot': e.slot})
          .toList(),
      'pricing': instance.pricing == null
          ? null
          : <String, dynamic>{'gtpPrice': instance.pricing!.gtpPrice},
    };

$Rec _$recordConvert<$Rec>(Object? value, $Rec Function(Map) convert) =>
    convert(value as Map<String, dynamic>);

$Rec? _$recordConvertNullable<$Rec>(
  Object? value,
  $Rec Function(Map) convert,
) => value == null ? null : convert(value as Map<String, dynamic>);
