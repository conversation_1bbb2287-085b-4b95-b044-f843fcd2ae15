import 'package:json_annotation/json_annotation.dart';
import 'package:sba/src/api/model/avi/avi_point_dto.dart';

part 'avi_booking_dto.g.dart';

@JsonSerializable()
class AVIBookingDto {
  AVIBookingDto({
    required this.id,
    required this.state,
    required this.services,
    required this.date,
    required this.vehicleCategory,
    required this.slot,
    required this.plateNumber,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.locationDetails,
  });

  factory AVIBookingDto.fromJson(Map<String, dynamic> json) => _$AVIBookingDtoFromJson(json);

  Map<String, dynamic> toJson() => _$AVIBookingDtoToJson(this);

  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'state')
  final String state;

  @JsonKey(name: 'services')
  final List<int> services;

  @JsonKey(name: 'date')
  final DateTime date;

  @JsonKey(name: 'vehicleCategory')
  final int vehicleCategory;

  @JsonKey(name: 'slot')
  final String slot;

  @Json<PERSON>ey(name: 'plateNumber')
  final String plateNumber;

  @JsonKey(name: 'customerId')
  final String customerId;

  @JsonKey(name: 'customerName')
  final String customerName;

  @JsonKey(name: 'customerPhone')
  final String customerPhone;

  @JsonKey(name: 'locationDetails')
  final AVIPointDto locationDetails;
}
