// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'training_center_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TrainingCenterDto _$TrainingCenterDtoFromJson(Map<String, dynamic> json) =>
    TrainingCenterDto(
      id: (json['id'] as num).toInt(),
      latitude: (json['mapLat'] as num).toDouble(),
      longitude: (json['mapLong'] as num).toDouble(),
      name: json['nameBg'] as String?,
      city: json['cityBg'] as String?,
      address: json['addressBg'] as String?,
      phone: json['phone'] as String?,
      mobile: json['mobilePhone'] as String?,
      email: json['email'] as String?,
      wtMonday: json['workTimeM'] as String?,
      wtTuesday: json['workTimeT'] as String?,
      wtWednesday: json['workTimeW'] as String?,
      wtThursday: json['workTimeTH'] as String?,
      wtFriday: json['workTimeF'] as String?,
      wtSaturday: json['workTimeSat'] as String?,
      wtSunday: json['workTimeSun'] as String?,
    );

Map<String, dynamic> _$TrainingCenterDtoToJson(TrainingCenterDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'nameBg': instance.name,
      'cityBg': instance.city,
      'addressBg': instance.address,
      'mapLat': instance.latitude,
      'mapLong': instance.longitude,
      'phone': instance.phone,
      'mobilePhone': instance.mobile,
      'email': instance.email,
      'workTimeM': instance.wtMonday,
      'workTimeT': instance.wtTuesday,
      'workTimeW': instance.wtWednesday,
      'workTimeTH': instance.wtThursday,
      'workTimeF': instance.wtFriday,
      'workTimeSat': instance.wtSaturday,
      'workTimeSun': instance.wtSunday,
    };
