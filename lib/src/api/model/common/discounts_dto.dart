import 'package:json_annotation/json_annotation.dart';

part 'discounts_dto.g.dart';

@JsonSerializable()
final class DiscountCategoryDto {
  DiscountCategoryDto({
    required this.id,
    required this.name,
    required this.discounts,
  });

  factory DiscountCategoryDto.fromJson(Map<String, dynamic> json) =>
      _$DiscountCategoryDtoFromJson(json);

  Map<String, dynamic> toJson() => _$DiscountCategoryDtoToJson(this);

  @JsonKey(name: 'categoryId')
  final int id;

  @Json<PERSON>ey(name: 'categoryName')
  final String name;

  @Json<PERSON>ey(name: 'discounts')
  final List<DiscountItemDto> discounts;
}

@JsonSerializable()
final class DiscountItemDto {
  DiscountItemDto({
    required this.rank,
    required this.name,
    required this.announcement,
    required this.title,
    required this.content,
    required this.image,
  });

  factory DiscountItemDto.fromJson(Map<String, dynamic> json) =>
      _$DiscountItemDtoFromJson(json);

  Map<String, dynamic> toJson() => _$DiscountItemDtoToJson(this);

  @JsonKey(name: 'rank')
  final int rank;

  @Json<PERSON>ey(name: 'partnerBg')
  final String name;

  @JsonKey(name: 'announcementBg')
  final String? announcement;

  @JsonKey(name: 'titleBg')
  final String? title;

  @JsonKey(name: 'contentBg')
  final String? content;

  @JsonKey(name: 'imageUrl')
  final String? image;
}
