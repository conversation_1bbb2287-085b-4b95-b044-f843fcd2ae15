import 'package:json_annotation/json_annotation.dart';

part 'info_dto.g.dart';

@JsonSerializable()
final class InfoDto {
  InfoDto({
    required this.title,
    required this.content,
  });

  factory InfoDto.fromJson(Map<String, dynamic> json) =>
      _$InfoDtoFromJson(json);

  Map<String, dynamic> toJson() => _$InfoDtoToJson(this);
  @JsonKey(name: 'titleBg')
  final String title;
  @Json<PERSON>ey(name: 'contentBg')
  final String content;
}
