import 'package:json_annotation/json_annotation.dart';

part 'place_dto.g.dart';

@JsonSerializable()
final class PlaceDto {
  PlaceDto({
    required this.id,
    required this.name,
    required this.district,
    required this.postCode,
  });

  factory PlaceDto.fromJson(Map<String, dynamic> json) =>
      _$PlaceDto<PERSON>rom<PERSON>son(json);

  Map<String, dynamic> toJson() => _$PlaceDtoTo<PERSON>son(this);

  @JsonKey(name: 'id')
  final int id;

  @Json<PERSON>ey(name: 'name')
  final String name;

  @Json<PERSON>ey(name: 'district')
  final String district;

  @JsonKey(name: 'postCode')
  final int postCode;
}
