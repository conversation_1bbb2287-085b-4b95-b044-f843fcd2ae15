import 'package:json_annotation/json_annotation.dart';

part 'newspaper_dto.g.dart';

@JsonSerializable()
final class NewspaperDto {
  NewspaperDto({
    required this.id,
    required this.title,
    required this.year,
    required this.issue,
    required this.url,
  });

  factory NewspaperDto.fromJson(Map<String, dynamic> json) =>
      _$NewspaperDtoFromJson(json);

  Map<String, dynamic> toJson() => _$NewspaperDtoToJson(this);

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'title')
  final String? title;
  @<PERSON>son<PERSON>ey(name: 'year')
  final int year;
  @<PERSON>sonKey(name: 'issue')
  final int issue;
  @<PERSON>son<PERSON>ey(name: 'url')
  final String url;
}
