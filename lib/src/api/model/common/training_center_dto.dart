import 'package:json_annotation/json_annotation.dart';

part 'training_center_dto.g.dart';
@JsonSerializable()
final class TrainingCenterDto {
  TrainingCenterDto({
    required this.id,
    required this.latitude,
    required this.longitude,
    this.name,
    this.city,
    this.address,
    this.phone,
    this.mobile,
    this.email,
    this.wtMonday,
    this.wtTuesday,
    this.wtWednesday,
    this.wtThursday,
    this.wtFriday,
    this.wtSaturday,
    this.wtSunday,
  });

  factory TrainingCenterDto.fromJson(Map<String, dynamic> json) =>
      _$TrainingCenterDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TrainingCenterDtoToJson(this);

  @Json<PERSON><PERSON>(name: 'id')
  final int id;
  @Json<PERSON>ey(name: 'nameBg')
  final String? name;
  @Json<PERSON>ey(name: 'cityBg')
  final String? city;
  @JsonKey(name: 'addressBg')
  final String? address;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mapLat')
  final double latitude;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mapLong')
  final double longitude;
  @J<PERSON><PERSON><PERSON>(name: 'phone')
  final String? phone;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'mobilePhone')
  final String? mobile;
  @Json<PERSON>ey(name: 'email')
  final String? email;
  @JsonKey(name: 'workTimeM')
  final String? wtMonday;
  @JsonKey(name: 'workTimeT')
  final String? wtTuesday;
  @JsonKey(name: 'workTimeW')
  final String? wtWednesday;
  @JsonKey(name: 'workTimeTH')
  final String? wtThursday;
  @JsonKey(name: 'workTimeF')
  final String? wtFriday;
  @JsonKey(name: 'workTimeSat')
  final String? wtSaturday;
  @JsonKey(name: 'workTimeSun')
  final String? wtSunday;
}
