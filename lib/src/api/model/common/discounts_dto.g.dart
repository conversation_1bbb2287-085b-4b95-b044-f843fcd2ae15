// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'discounts_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DiscountCategoryDto _$DiscountCategoryDtoFromJson(Map<String, dynamic> json) =>
    DiscountCategoryDto(
      id: (json['categoryId'] as num).toInt(),
      name: json['categoryName'] as String,
      discounts: (json['discounts'] as List<dynamic>)
          .map((e) => DiscountItemDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DiscountCategoryDtoToJson(
  DiscountCategoryDto instance,
) => <String, dynamic>{
  'categoryId': instance.id,
  'categoryName': instance.name,
  'discounts': instance.discounts,
};

DiscountItemDto _$DiscountItemDtoFromJson(Map<String, dynamic> json) =>
    DiscountItemDto(
      rank: (json['rank'] as num).toInt(),
      name: json['partnerBg'] as String,
      announcement: json['announcementBg'] as String?,
      title: json['titleBg'] as String?,
      content: json['contentBg'] as String?,
      image: json['imageUrl'] as String?,
    );

Map<String, dynamic> _$DiscountItemDtoToJson(DiscountItemDto instance) =>
    <String, dynamic>{
      'rank': instance.rank,
      'partnerBg': instance.name,
      'announcementBg': instance.announcement,
      'titleBg': instance.title,
      'contentBg': instance.content,
      'imageUrl': instance.image,
    };
