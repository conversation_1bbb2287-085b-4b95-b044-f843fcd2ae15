import 'package:json_annotation/json_annotation.dart';

part 'cctv_dto.g.dart';

@JsonSerializable()
final class CCTVDto {
  CCTVDto({
    required this.id,
    required this.latitude,
    required this.longitude,
    this.name,
    this.description,
    this.url,
  });

  factory CCTVDto.fromJson(Map<String, dynamic> json) =>
      _$CCTVDtoFromJson(json);

  Map<String, dynamic> toJson() => _$CCTVDtoToJson(this);

  @<PERSON>son<PERSON><PERSON>(name: 'id')
  final int id;
  @Json<PERSON><PERSON>(name: 'name')
  final String? name;
  @Json<PERSON>ey(name: 'description')
  final String? description;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'latitude')
  final double latitude;
  @<PERSON>son<PERSON>ey(name: 'longitude')
  final double longitude;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'url')
  final String? url;
}
