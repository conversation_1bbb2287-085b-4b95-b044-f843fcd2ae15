import 'package:json_annotation/json_annotation.dart';

part 'questions_dto.g.dart';

@JsonSerializable()
final class QuestionGroupDto {
  QuestionGroupDto({
    required this.id,
    required this.name,
    required this.questions,
  });

  factory QuestionGroupDto.fromJson(Map<String, dynamic> json) =>
      _$QuestionGroupDtoFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionGroupDtoToJson(this);

  @JsonKey(name: 'groupId')
  final int id;

  @JsonKey(name: 'groupName')
  final String name;

  @J<PERSON><PERSON>ey(name: 'faq')
  final List<QuestionItemDto> questions;
}

@JsonSerializable()
final class QuestionItemDto {
  QuestionItemDto({
    required this.id,
    required this.question,
    required this.answer,
  });

  factory QuestionItemDto.fromJson(Map<String, dynamic> json) =>
      _$QuestionItemDtoFromJson(json);

  Map<String, dynamic> toJson() => _$QuestionItemDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  final int id;

  @J<PERSON><PERSON>ey(name: 'questionBg')
  final String question;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'answerBg')
  final String answer;
}
