// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'questions_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestionGroupDto _$QuestionGroupDtoFromJson(Map<String, dynamic> json) =>
    QuestionGroupDto(
      id: (json['groupId'] as num).toInt(),
      name: json['groupName'] as String,
      questions: (json['faq'] as List<dynamic>)
          .map((e) => QuestionItemDto.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$QuestionGroupDtoToJson(QuestionGroupDto instance) =>
    <String, dynamic>{
      'groupId': instance.id,
      'groupName': instance.name,
      'faq': instance.questions,
    };

QuestionItemDto _$QuestionItemDtoFromJson(Map<String, dynamic> json) =>
    QuestionItemDto(
      id: (json['id'] as num).toInt(),
      question: json['questionBg'] as String,
      answer: json['answerBg'] as String,
    );

Map<String, dynamic> _$QuestionItemDtoToJson(QuestionItemDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'questionBg': instance.question,
      'answerBg': instance.answer,
    };
