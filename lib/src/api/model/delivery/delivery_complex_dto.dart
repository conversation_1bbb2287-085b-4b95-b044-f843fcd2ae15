import 'package:json_annotation/json_annotation.dart';

part 'delivery_complex_dto.g.dart';

@JsonSerializable()
final class DeliveryComplexDto {

  DeliveryComplexDto({
    required this.id,
    required this.actualId,
    required this.siteId,
    required this.type,
    required this.typeEn,
    required this.name,
    required this.nameEn,
  });

  factory DeliveryComplexDto.fromJson(Map<String, dynamic> json) =>
      _$DeliveryComplexDtoFromJson(json);

  Map<String, dynamic> toJson() => _$DeliveryComplexDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'actualId')
  int actualId;
  @Json<PERSON><PERSON>(name: 'siteId')
  int siteId;
  @Json<PERSON>ey(name: 'type')
  String type;
  @JsonKey(name: 'typeEn')
  String typeEn;
  @JsonKey(name: 'name')
  String name;
  @Json<PERSON>ey(name: 'nameEn')
  String nameEn;
}
