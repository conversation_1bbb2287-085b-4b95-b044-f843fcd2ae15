import 'package:json_annotation/json_annotation.dart';

part 'delivery_street_dto.g.dart';

@JsonSerializable()
final class DeliveryStreetDto {
  const DeliveryStreetDto({
    required this.id,
    required this.actualId,
    required this.siteId,
    required this.type,
    required this.typeEn,
    required this.name,
    required this.nameEn,
  });

  factory DeliveryStreetDto.fromJson(Map<String, dynamic> json) =>
      _$DeliveryStreetDtoFromJson(json);

  Map<String, dynamic> toJson() => _$DeliveryStreetDtoToJson(this);
  @Json<PERSON>ey(name: 'id')
  final int id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'actualId')
  final int actualId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'siteId')
  final int siteId;
  @Json<PERSON>ey(name: 'type')
  final String type;
  @JsonKey(name: 'typeEn')
  final String typeEn;
  @JsonKey(name: 'name')
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'nameEn')
  final String nameEn;
}
