import 'package:json_annotation/json_annotation.dart';

part 'delivery_block_dto.g.dart';

@JsonSerializable()
final class DeliveryBlockDto {

  const DeliveryBlockDto({
    required this.siteId,
    required this.name,
    required this.nameEn,
  });

  factory DeliveryBlockDto.fromJson(Map<String, dynamic> json) =>
      _$DeliveryBlockDtoFromJson(json);

  Map<String, dynamic> toJson() => _$DeliveryBlockDtoToJson(this);
  @Json<PERSON>ey(name: 'siteId')
  final int siteId;

  @Json<PERSON>ey(name: 'name')
  final String name;

  @Json<PERSON>ey(name: 'nameEn')
  final String nameEn;
}
