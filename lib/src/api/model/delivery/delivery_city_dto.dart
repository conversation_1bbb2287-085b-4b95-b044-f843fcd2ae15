import 'package:json_annotation/json_annotation.dart';

part 'delivery_city_dto.g.dart';

@JsonSerializable()
final class DeliveryCityDto {

  const DeliveryCityDto({
    required this.id,
    required this.type,
    required this.typeEn,
    required this.name,
    required this.nameEn,
    required this.municipality,
    required this.municipalityEn,
    required this.region,
    required this.regionEn,
    required this.x,
    required this.y,
  });

  factory DeliveryCityDto.fromJson(Map<String, dynamic> json) =>
      _$DeliveryCityDtoFromJson(json);

  Map<String, dynamic> toJson() => _$DeliveryCityDtoToJson(this);

  @<PERSON>son<PERSON><PERSON>(name: 'id')
  final int id;
  @JsonKey(name: 'type')
  final String type;
  @Json<PERSON>ey(name: 'typeEn')
  final String typeEn;
  @JsonKey(name: 'name')
  final String name;
  @Json<PERSON>ey(name: 'nameEn')
  final String nameEn;
  @JsonKey(name: 'municipality')
  final String municipality;
  @JsonKey(name: 'municipalityEn')
  final String municipalityEn;
  @Json<PERSON>ey(name: 'region')
  final String region;
  @Json<PERSON>ey(name: 'regionEn')
  final String regionEn;
  @Json<PERSON>ey(name: 'x')
  final double x;
  @JsonKey(name: 'y')
  final double y;
}
