import 'package:json_annotation/json_annotation.dart';

part 'delivery_office_dto.g.dart';

@JsonSerializable(createFactory: true)
final class DeliveryOfficeDto {
  const DeliveryOfficeDto({
    required this.id,
    required this.siteId,
    required this.name,
    required this.nameEn,
    required this.address,
  });

  factory DeliveryOfficeDto.fromJson(Map<String, dynamic> json) =>
      _$DeliveryOfficeDtoFromJson(json);

  Map<String, dynamic> toJson() => _$DeliveryOfficeDtoToJson(this);

  @<PERSON>son<PERSON>ey(name: 'id')
  final int id;
  @<PERSON>son<PERSON>ey(name: 'siteId')
  final int siteId;
  @Json<PERSON>ey(name: 'name')
  final String name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'nameEn')
  final String nameEn;
  @JsonKey(name: 'address')
  final ({String fullAddressString, String localAddressString}) address;
}
