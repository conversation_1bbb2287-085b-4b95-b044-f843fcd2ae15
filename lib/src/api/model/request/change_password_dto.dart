import 'package:json_annotation/json_annotation.dart';

part 'change_password_dto.g.dart';

@JsonSerializable()
final class ChangePasswordDto {
  ChangePasswordDto({
    required this.username,
    required this.currentPassword,
    required this.newPassword,
    required this.passwordAgain,
  });

  factory ChangePasswordDto.fromJson(Map<String, dynamic> json) =>
      _$ChangePasswordDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ChangePasswordDtoToJson(this);

  @Json<PERSON>ey(name: 'userName')
  final String username;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'currentPassword')
  final String currentPassword;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'password')
  final String newPassword;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'passwordAgain')
  final String passwordAgain;
}
