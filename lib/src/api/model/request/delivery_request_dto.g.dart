// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_request_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeliveryRequestDto _$DeliveryRequestDtoFromJson(Map<String, dynamic> json) =>
    DeliveryRequestDto(
      deliveryTo: (json['deliveryTo'] as num).toInt(),
      speedyOfficeId: (json['speedyOfficeId'] as num?)?.toInt(),
      speedyOffice: json['speedyOffice'] as String?,
      cityId: (json['cityId'] as num?)?.toInt(),
      city: json['city'] as String?,
      complexId: (json['complexId'] as num?)?.toInt(),
      complex: json['complex'] as String?,
      streetId: (json['streetId'] as num?)?.toInt(),
      street: json['street'] as String?,
      streetNumber: json['streetNumber'] as String?,
      blockId: (json['blockId'] as num?)?.toInt(),
      block: json['block'] as String?,
      entranceNumber: json['entranceNumber'] as String?,
      floor: json['floor'] as String?,
      apartmentNumber: json['apartmentNumber'] as String?,
      note: json['note'] as String?,
    );

Map<String, dynamic> _$DeliveryRequestDtoToJson(DeliveryRequestDto instance) =>
    <String, dynamic>{
      'deliveryTo': instance.deliveryTo,
      'speedyOfficeId': ?instance.speedyOfficeId,
      'speedyOffice': ?instance.speedyOffice,
      'cityId': ?instance.cityId,
      'city': ?instance.city,
      'complexId': ?instance.complexId,
      'complex': ?instance.complex,
      'streetId': ?instance.streetId,
      'street': ?instance.street,
      'streetNumber': ?instance.streetNumber,
      'blockId': ?instance.blockId,
      'block': ?instance.block,
      'entranceNumber': ?instance.entranceNumber,
      'floor': ?instance.floor,
      'apartmentNumber': ?instance.apartmentNumber,
      'note': ?instance.note,
    };
