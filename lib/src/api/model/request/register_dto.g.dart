// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'register_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RegisterDto _$RegisterDtoFromJson(Map<String, dynamic> json) => RegisterDto(
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  email: json['email'] as String,
  emailInfo: const BoolIntConverter().fromJson(
    (json['emailInfo'] as num).toInt(),
  ),
  emailPromo: const BoolIntConverter().fromJson(
    (json['emailPromo'] as num).toInt(),
  ),
  phone: json['mobile'] as String,
  phoneInfo: const BoolIntConverter().fromJson(
    (json['mobileInfo'] as num).toInt(),
  ),
  phonePromo: const BoolIntConverter().fromJson(
    (json['mobilePromo'] as num).toInt(),
  ),
  city: (json['cityId'] as num).toInt(),
  address: json['address'] as String,
  password: json['password'] as String,
  confirmPassword: json['confirmPassword'] as String,
);

Map<String, dynamic> _$RegisterDtoToJson(RegisterDto instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'emailInfo': const BoolIntConverter().toJson(instance.emailInfo),
      'emailPromo': const BoolIntConverter().toJson(instance.emailPromo),
      'mobile': instance.phone,
      'mobileInfo': const BoolIntConverter().toJson(instance.phoneInfo),
      'mobilePromo': const BoolIntConverter().toJson(instance.phonePromo),
      'cityId': instance.city,
      'address': instance.address,
      'password': instance.password,
      'confirmPassword': instance.confirmPassword,
    };
