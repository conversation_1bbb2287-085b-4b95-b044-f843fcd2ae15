import 'package:json_annotation/json_annotation.dart';
import 'package:sba/src/api/converters/bool_int_converter.dart';

part 'register_dto.g.dart';

@JsonSerializable()
final class RegisterDto {
  RegisterDto({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.emailInfo,
    required this.emailPromo,
    required this.phone,
    required this.phoneInfo,
    required this.phonePromo,
    required this.city,
    required this.address,
    required this.password,
    required this.confirmPassword,
  });

  factory RegisterDto.fromJson(Map<String, dynamic> json) =>
      _$RegisterDtoFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterDtoToJson(this);

  @JsonKey(name: 'firstName')
  final String firstName;
  @JsonKey(name: 'lastName')
  final String lastName;
  @JsonKey(name: 'email')
  final String email;
  @BoolIntConverter()
  @<PERSON>son<PERSON>ey(name: 'emailInfo')
  final bool emailInfo;
  @BoolIntConverter()
  @<PERSON>sonKey(name: 'emailPromo')
  final bool emailPromo;
  @JsonKey(name: 'mobile')
  final String phone;
  @BoolIntConverter()
  @JsonKey(name: 'mobileInfo')
  final bool phoneInfo;
  @BoolIntConverter()
  @JsonKey(name: 'mobilePromo')
  final bool phonePromo;
  @JsonKey(name: 'cityId')
  final int city;
  @JsonKey(name: 'address')
  final String address;
  @JsonKey(name: 'password')
  final String password;
  @JsonKey(name: 'confirmPassword')
  final String confirmPassword;
}
