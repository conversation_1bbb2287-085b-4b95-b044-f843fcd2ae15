// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'change_consent_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChangeConsentDto _$ChangeConsentDtoFromJson(Map<String, dynamic> json) =>
    ChangeConsentDto(
      userId: (json['userId'] as num).toInt(),
      sessionId: json['sessionId'] as String,
      emailInfo: _$JsonConverterFromJson<int, bool>(
        json['emailInfo'],
        const BoolIntConverter().fromJson,
      ),
      emailPromo: _$JsonConverterFromJson<int, bool>(
        json['emailPromo'],
        const BoolIntConverter().fromJson,
      ),
      phoneInfo: _$JsonConverterFromJson<int, bool>(
        json['mobileInfo'],
        const BoolIntConverter().fromJson,
      ),
      phonePromo: _$JsonConverterFromJson<int, bool>(
        json['mobilePromo'],
        const BoolIntConverter().fromJson,
      ),
    );

Map<String, dynamic> _$ChangeConsentDtoToJson(ChangeConsentDto instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'sessionId': instance.sessionId,
      'emailInfo': ?_$JsonConverterToJson<int, bool>(
        instance.emailInfo,
        const BoolIntConverter().toJson,
      ),
      'emailPromo': ?_$JsonConverterToJson<int, bool>(
        instance.emailPromo,
        const BoolIntConverter().toJson,
      ),
      'mobileInfo': ?_$JsonConverterToJson<int, bool>(
        instance.phoneInfo,
        const BoolIntConverter().toJson,
      ),
      'mobilePromo': ?_$JsonConverterToJson<int, bool>(
        instance.phonePromo,
        const BoolIntConverter().toJson,
      ),
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) => json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) => value == null ? null : toJson(value);
