import 'package:json_annotation/json_annotation.dart';

part 'register_order_request_dto.g.dart';

@JsonSerializable()
final class RegisterOrderRequestDto {
  RegisterOrderRequestDto({
    required this.amount,
    required this.currency,
    required this.orderNumber,
    required this.returnUrl,
    required this.language,
  });

  factory RegisterOrderRequestDto.fromJson(Map<String, dynamic> json) =>
      _$RegisterOrderRequestDtoFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterOrderRequestDtoToJson(this);

  @JsonKey(name: 'amount')
  final int amount;
  @JsonKey(name: 'currency')
  final int currency;
  @JsonKey(name: 'orderNumber')
  final String orderNumber;
  @<PERSON>son<PERSON>ey(name: 'returnUrl')
  final String returnUrl;
  @JsonKey(name: 'language')
  final String language;
}
