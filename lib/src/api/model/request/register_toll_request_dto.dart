import 'package:json_annotation/json_annotation.dart';

part 'register_toll_request_dto.g.dart';

@JsonSerializable()
final class RegisterTollRequestDto {
  RegisterTollRequestDto({
    required this.activationDate,
    required this.email,
    required this.productId,
    required this.vehicle,
  });

  factory RegisterTollRequestDto.fromJson(Map<String, dynamic> json) =>
      _$RegisterTollRequestDtoFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterTollRequestDtoToJson(this);

  @Json<PERSON>ey(name: 'activationDate')
  final DateTime activationDate;
  @JsonKey(name: 'email')
  final String? email;
  @<PERSON>sonKey(name: 'kapschProductId')
  final int productId;
  @<PERSON>son<PERSON>ey(name: 'vehicle')
  final ({String lpn, String countryCode}) vehicle;
}
