import 'package:json_annotation/json_annotation.dart';
import 'package:sba/src/api/converters/bool_int_converter.dart';

part 'change_consent_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class ChangeConsentDto {
  ChangeConsentDto({
    required this.userId,
    required this.sessionId,
    this.emailInfo,
    this.emailPromo,
    this.phoneInfo,
    this.phonePromo,
  });

  factory ChangeConsentDto.fromJson(Map<String, dynamic> json) =>
      _$ChangeConsentDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ChangeConsentDtoToJson(this);

  @<PERSON>son<PERSON>ey(name: 'userId')
  final int userId;
  @J<PERSON><PERSON><PERSON>(name: 'sessionId')
  final String sessionId;
  @BoolIntConverter()
  @JsonKey(name: 'emailInfo')
  final bool? emailInfo;
  @BoolIntConverter()
  @Json<PERSON>ey(name: 'emailPromo')
  final bool? emailPromo;
  @BoolIntConverter()
  @JsonKey(name: 'mobileInfo')
  final bool? phoneInfo;
  @BoolIntConverter()
  @<PERSON>son<PERSON>ey(name: 'mobilePromo')
  final bool? phonePromo;
}
