import 'package:json_annotation/json_annotation.dart';

part 'change_address_dto.g.dart';

@JsonSerializable()
final class ChangeAddressDto {
  ChangeAddressDto({
    required this.userId,
    required this.sessionId,
    required this.city,
    required this.address,
  });

  factory ChangeAddressDto.fromJson(Map<String, dynamic> json) =>
      _$ChangeAddressDtoFromJson(json);

  Map<String, dynamic> toJson() => _$ChangeAddressDtoToJson(this);

  @Json<PERSON>ey(name: 'userId')
  final int userId;
  @<PERSON>son<PERSON>ey(name: 'sessionId')
  final String sessionId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'cityId')
  final int city;
  @Json<PERSON>ey(name: 'address')
  final String address;
}
