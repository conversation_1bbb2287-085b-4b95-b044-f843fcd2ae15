import 'package:json_annotation/json_annotation.dart';

part 'delivery_request_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class DeliveryRequestDto {
  const DeliveryRequestDto({
    required this.deliveryTo,
    required this.speedyOfficeId,
    required this.speedyOffice,
    required this.cityId,
    required this.city,
    required this.complexId,
    required this.complex,
    required this.streetId,
    required this.street,
    required this.streetNumber,
    required this.blockId,
    required this.block,
    required this.entranceNumber,
    required this.floor,
    required this.apartmentNumber,
    required this.note,
  });

  factory DeliveryRequestDto.fromJson(Map<String, dynamic> json) =>
      _$DeliveryRequestDtoFromJson(json);

  Map<String, dynamic> toJson() => _$DeliveryRequestDtoToJson(this);
  @JsonKey(name: 'deliveryTo')
  final int deliveryTo;
  @JsonKey(name: 'speedyOfficeId')
  final int? speedyOfficeId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'speedyOffice')
  final String? speedyOffice;
  @JsonKey(name: 'cityId')
  final int? cityId;
  @JsonKey(name: 'city')
  final String? city;
  @JsonKey(name: 'complexId')
  final int? complexId;
  @JsonKey(name: 'complex')
  final String? complex;
  @JsonKey(name: 'streetId')
  final int? streetId;
  @JsonKey(name: 'street')
  final String? street;
  @JsonKey(name: 'streetNumber')
  final String? streetNumber;
  @JsonKey(name: 'blockId')
  final int? blockId;
  @JsonKey(name: 'block')
  final String? block;
  @JsonKey(name: 'entranceNumber')
  final String? entranceNumber;
  @JsonKey(name: 'floor')
  final String? floor;
  @JsonKey(name: 'apartmentNumber')
  final String? apartmentNumber;
  @JsonKey(name: 'note')
  final String? note;
}
