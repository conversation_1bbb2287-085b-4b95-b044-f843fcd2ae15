import 'package:json_annotation/json_annotation.dart';

part 'token_dto.g.dart';

@JsonSerializable()
final class TokenDto {
  TokenDto({
    required this.accessToken,
    required this.refreshToken,
    required this.sessionId,
  });

  factory TokenDto.fromJson(Map<String, dynamic> json) =>
      _$TokenDtoFromJson(json);

  Map<String, dynamic> toJson() => _$TokenDtoToJson(this);

  @Json<PERSON>ey(name: 'token')
  final String accessToken;

  @JsonKey(name: 'refreshToken')
  final String refreshToken;

  @JsonKey(name: 'sessionId')
  final String? sessionId;
}
