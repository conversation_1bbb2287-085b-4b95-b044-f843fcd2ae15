// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'car_brand_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CarBrandDto _$CarBrandDtoFromJson(Map<String, dynamic> json) => CarBrandDto(
  id: (json['id'] as num).toInt(),
  brand: json['make'] as String,
  models: (json['models'] as List<dynamic>)
      .map((e) => CarModelDto.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$CarBrandDtoToJson(CarBrandDto instance) =>
    <String, dynamic>{
      'id': instance.id,
      'make': instance.brand,
      'models': instance.models,
    };

CarModelDto _$CarModelDtoFromJson(Map<String, dynamic> json) => CarModelDto(
  id: (json['id'] as num).toInt(),
  model: json['model'] as String,
);

Map<String, dynamic> _$CarModelDtoToJson(CarModelDto instance) =>
    <String, dynamic>{'id': instance.id, 'model': instance.model};
