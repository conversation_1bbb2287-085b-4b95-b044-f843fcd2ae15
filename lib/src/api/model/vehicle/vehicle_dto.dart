import 'package:json_annotation/json_annotation.dart';

part 'vehicle_dto.g.dart';

@JsonSerializable(includeIfNull: false)
final class VehicleDto {
  VehicleDto({
    required this.makerId,
    required this.modelId,
    required this.plateNumber,
    required this.city,
    required this.address,
    this.id,
  });

  factory VehicleDto.fromJson(Map<String, dynamic> json) =>
      _$VehicleDtoFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleDtoToJson(this);

  @JsonKey(name: 'id')
  final int? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'makerId')
  final int makerId;
  @<PERSON>son<PERSON><PERSON>(name: 'modelId')
  final int modelId;
  @<PERSON>son<PERSON>ey(name: 'dkn')
  final String plateNumber;
  @JsonKey(name: 'placeId')
  final int city;
  @<PERSON>son<PERSON>ey(name: 'address')
  final String address;
}
