import 'package:json_annotation/json_annotation.dart';

part 'car_brand_dto.g.dart';

@JsonSerializable()
final class CarBrandDto {
  CarBrandDto({required this.id, required this.brand, required this.models});

  factory CarBrandDto.fromJson(Map<String, dynamic> json) =>
      _$CarBrandDtoFromJson(json);

  Map<String, dynamic> toJson() => _$CarBrandDtoToJson(this);

  @JsonKey(name: 'id')
  final int id;

  @JsonKey(name: 'make')
  final String brand;

  @JsonKey(name: 'models')
  final List<CarModelDto> models;
}

@JsonSerializable()
final class CarModelDto {
  CarModelDto({required this.id, required this.model});

  factory CarModelDto.fromJson(Map<String, dynamic> json) =>
      _$CarModelDtoFromJson(json);

  Map<String, dynamic> toJson() => _$CarModelDtoToJson(this);

  @Json<PERSON>ey(name: 'id')
  final int id;

  @<PERSON>son<PERSON>ey(name: 'model')
  final String model;
}
