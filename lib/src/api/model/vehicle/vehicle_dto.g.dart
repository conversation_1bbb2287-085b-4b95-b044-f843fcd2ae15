// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_dto.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VehicleDto _$VehicleDtoFromJson(Map<String, dynamic> json) => VehicleDto(
  makerId: (json['makerId'] as num).toInt(),
  modelId: (json['modelId'] as num).toInt(),
  plateNumber: json['dkn'] as String,
  city: (json['placeId'] as num).toInt(),
  address: json['address'] as String,
  id: (json['id'] as num?)?.toInt(),
);

Map<String, dynamic> _$<PERSON>to<PERSON>o<PERSON>son(VehicleDto instance) =>
    <String, dynamic>{
      'id': ?instance.id,
      'makerId': instance.makerId,
      'modelId': instance.modelId,
      'dkn': instance.plateNumber,
      'placeId': instance.city,
      'address': instance.address,
    };
