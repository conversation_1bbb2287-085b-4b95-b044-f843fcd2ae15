import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/request/change_address_dto.dart';
import 'package:sba/src/api/model/request/change_consent_dto.dart';

part 'user_api.g.dart';

@RestApi(baseUrl: 'api/user')
abstract class UserApi {
  factory UserApi(Dio dio) = _UserApi;

  @POST('/update-address')
  Future<void> changeAddress({@Body() required ChangeAddressDto dto});

  @POST('/update-email-consent')
  Future<void> changeEmailConsent({@Body() required ChangeConsentDto dto});

  @POST('/update-mobile-consent')
  Future<void> changeMobileConsent({@Body() required ChangeConsentDto dto});
}
