import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/token_dto.dart';

part 'token_api.g.dart';

@RestApi(baseUrl: 'api/Token')
abstract class TokenApi {
  factory TokenApi(Dio dio) = _TokenApi;

  @POST('/refresh')
  Future<TokenDto> refresh({
    @Field('accessToken') required String accessToken,
    @Field('refreshToken') required String refreshToken,
  });

  @POST('/revoke')
  Future<TokenDto> revoke({
    @Query('refreshToken') required String refreshToken,
  });
}
