import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/notification/notification_dto.dart';

part 'notification_api.g.dart';

@RestApi(baseUrl: 'api/Notifications')
abstract class NotificationApi {
  factory NotificationApi(Dio dio) = _NotificationApi;

  @GET('/get-push-notifications')
  Future<List<NotificationDto>> getNotifications();

  @POST('/update-all-push-seen')
  Future<void> seeAllNotifications();
}
