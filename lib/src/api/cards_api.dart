import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/subscription/subscription_dto.dart';
import 'package:sba/src/api/model/subscription/subscription_order_request_dto.dart';
import 'package:sba/src/api/model/subscription/subscription_product_dto.dart';

part 'cards_api.g.dart';

@RestApi(baseUrl: 'api/Cards')
abstract class CardsApi {
  factory CardsApi(Dio dio) = _CardsApi;

  @GET('/card-prices')
  Future<List<SubscriptionProductDto>> getSubscriptionProducts();
  
  @GET('/user-cards')
  Future<List<SubscriptionDto>> getSubscriptions();

  @GET('/user-card-orders')
  Future<List<SubscriptionOrderRequestDto>> getOrders();

  @POST('/add-card-order')
  Future<SubscriptionOrderRequestDto> createOrderRequest({
    @Body() required SubscriptionOrderRequestDto request,
  });
}
