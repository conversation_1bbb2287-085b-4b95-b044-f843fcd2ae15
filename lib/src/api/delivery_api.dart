import 'package:dio/dio.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/delivery/delivery_block_dto.dart';
import 'package:sba/src/api/model/delivery/delivery_city_dto.dart';
import 'package:sba/src/api/model/delivery/delivery_complex_dto.dart';
import 'package:sba/src/api/model/delivery/delivery_office_dto.dart';
import 'package:sba/src/api/model/delivery/delivery_street_dto.dart';

part 'delivery_api.g.dart';

@RestApi(baseUrl: 'api/Delivery')
abstract class DeliveryApi {
  factory DeliveryApi(Dio dio) = _DeliveryApi;

  @GET('/find-city')
  Future<DeliveryResponseDto<List<DeliveryCityDto>>> findCity({
    @Query('name') required String name,
  });

  @GET('/find-street')
  Future<DeliveryResponseDto<List<DeliveryStreetDto>>> findStreet({
    @Query('name') required String name,
    @Query('siteId') required int siteId,
  });

  @GET('/find-complex')
  Future<DeliveryResponseDto<List<DeliveryComplexDto>>> findComplex({
    @Query('name') required String name,
    @Query('siteId') required int siteId,
  });

  @GET('/find-block')
  Future<DeliveryResponseDto<List<DeliveryBlockDto>>> findBlock({
    @Query('name') required String name,
    @Query('siteId') required int siteId,
  });

  @GET('/find-speedy-office')
  Future<DeliveryResponseDto<List<DeliveryOfficeDto>>> findOffice({
    @Query('name') required String name,
    @Query('siteId') int? siteId,
  });
}

@JsonSerializable(genericArgumentFactories: true)
final class DeliveryResponseDto<T> {
  DeliveryResponseDto({required this.data});

  factory DeliveryResponseDto.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$DeliveryResponseDtoFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T) toJsonT) =>
      _$DeliveryResponseDtoToJson(this, toJsonT);

  @JsonKey(name: 'data')
  final T data;
}
