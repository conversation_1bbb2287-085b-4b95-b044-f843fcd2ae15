// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'avi_api.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AVIResponseDto<T> _$AVIResponseDtoFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => AVIResponseDto<T>(
  items: (json['items'] as List<dynamic>).map(fromJsonT).toList(),
);

Map<String, dynamic> _$AVIResponseDtoToJson<T>(
  AVIResponseDto<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{'items': instance.items.map(toJsonT).toList()};

AVIBookingResponse _$AVIBookingResponseFromJson(Map<String, dynamic> json) =>
    AVIBookingResponse(
      status: json['status'] as String,
      message: json['message'] as String,
      bookingDetails: AVIBookingDto.fromJson(
        json['bookingDetails'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$AVIBookingResponseToJson(AVIBookingResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'bookingDetails': instance.bookingDetails,
    };

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers,unused_element,unnecessary_string_interpolations,unused_element_parameter

class _AviApi implements AviApi {
  _AviApi(this._dio, {this.baseUrl, this.errorLogger}) {
    baseUrl ??= 'api/AVI';
  }

  final Dio _dio;

  String? baseUrl;

  final ParseErrorLogger? errorLogger;

  @override
  Future<AVIResponseDto<AVIBookingDto>> getBookings() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<AVIResponseDto<AVIBookingDto>>(
      Options(method: 'GET', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/user-bookings',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late AVIResponseDto<AVIBookingDto> _value;
    try {
      _value = AVIResponseDto<AVIBookingDto>.fromJson(
        _result.data!,
        (json) => AVIBookingDto.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<AVIResponseDto<AVIPointDto>> findLocation({
    required String city,
    required String date,
    required int vehicleCategory,
    bool gtp = true,
    bool lpg = false,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'city': city,
      'date': date,
      'vehicleCategory': vehicleCategory,
      'gtp': gtp,
      'licenceNewLpg': lpg,
    };
    final _options = _setStreamType<AVIResponseDto<AVIPointDto>>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/find-location',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late AVIResponseDto<AVIPointDto> _value;
    try {
      _value = AVIResponseDto<AVIPointDto>.fromJson(
        _result.data!,
        (json) => AVIPointDto.fromJson(json as Map<String, dynamic>),
      );
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<AVIBookingResponse> book({
    required int lineId,
    required String date,
    required String slot,
    required int vehicleCategory,
    required String plateNumber,
    bool gtp = true,
    bool lpg = false,
  }) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = {
      'lineId': lineId,
      'date': date,
      'slot': slot,
      'vehicleCategory': vehicleCategory,
      'plateNumber': plateNumber,
      'gtp': gtp,
      'licenceNewLpg': lpg,
    };
    final _options = _setStreamType<AVIBookingResponse>(
      Options(method: 'POST', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/book',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    final _result = await _dio.fetch<Map<String, dynamic>>(_options);
    late AVIBookingResponse _value;
    try {
      _value = AVIBookingResponse.fromJson(_result.data!);
    } on Object catch (e, s) {
      errorLogger?.logError(e, s, _options);
      rethrow;
    }
    return _value;
  }

  @override
  Future<void> cancelBooking({required String bookingId}) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{r'bookingId': bookingId};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _options = _setStreamType<void>(
      Options(method: 'DELETE', headers: _headers, extra: _extra)
          .compose(
            _dio.options,
            '/cancel-booking',
            queryParameters: queryParameters,
            data: _data,
          )
          .copyWith(baseUrl: _combineBaseUrls(_dio.options.baseUrl, baseUrl)),
    );
    await _dio.fetch<void>(_options);
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(String dioBaseUrl, String? baseUrl) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
