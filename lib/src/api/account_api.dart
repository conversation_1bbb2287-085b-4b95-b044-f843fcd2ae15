import 'package:dio/dio.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/request/change_password_dto.dart';
import 'package:sba/src/api/model/request/register_dto.dart';
import 'package:sba/src/api/model/token_dto.dart';

part 'account_api.g.dart';

Object? _topLevelDecoder(Map<dynamic, dynamic> json, String key) => json;

@RestApi(baseUrl: 'api/Account')
abstract class AccountApi {
  factory AccountApi(Dio dio) = _AccountApi;

  @POST('/login')
  Future<TokenDto> login({
    @Field('userName') required String email,
    @Field('password') required String password,
  });

  @POST('/guest-login')
  Future<GuestLoginResponseDto> guestLogin({
    @Query('guestUserId') int? guestUserId,
  });

  @POST('/lost-password')
  Future<void> lostPassword({
    @Field('email') required String email,
  });

  @POST('/check-username')
  Future<void> checkUsername({
    @Field('email') required String email,
  });

  @POST('/register')
  Future<void> register({
    @Body() required RegisterDto data,
  });

  @POST('/send_activation_email')
  Future<void> sendActivationMail({
    @Field('email') required String email,
  });

  @POST('/change-password')
  Future<void> changePassword({@Body() required ChangePasswordDto dto});
}

@JsonSerializable()
final class GuestLoginResponseDto {
  GuestLoginResponseDto({required this.token, required this.userId});

  factory GuestLoginResponseDto.fromJson(Map<String, dynamic> json) =>
      _$GuestLoginResponseDtoFromJson(json);

  Map<String, dynamic> toJson() => _$GuestLoginResponseDtoToJson(this);

  static Object? topLevelDecoder(Map<dynamic, dynamic> json, String key) =>
      json;

  @JsonKey(readValue: _topLevelDecoder)
  final TokenDto token;
  @JsonKey(name: 'userId')
  final int userId;
}
