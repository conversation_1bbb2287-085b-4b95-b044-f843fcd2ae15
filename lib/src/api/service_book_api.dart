import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/service_book/annual_inspection_dto.dart';
import 'package:sba/src/api/model/service_book/oil_change_dto.dart';
import 'package:sba/src/api/model/service_book/transmission_oil_change_dto.dart';
import 'package:sba/src/api/model/service_book/vehicle_refuel_dto.dart';
import 'package:sba/src/api/model/service_book/vehicle_service_dto.dart';
import 'package:sba/src/api/model/service_book/vehicle_tyre_dto.dart';
import 'package:sba/src/api/model/service_book/vehicle_tyre_swap_dto.dart';

part 'service_book_api.g.dart';

@RestApi(baseUrl: 'api/ServiceBook')
abstract class ServiceBookApi {
  factory ServiceBookApi(Dio dio) = _ServiceBookApi;

  @GET('/engine-oil-changes')
  Future<List<OilChangeDto>> getEngineOilChanges({
    @Query('carId') required int vehicleId,
  });

  @POST('/engine-oil-changes/add')
  Future<void> addEngineOilChange({
    @Body() required OilChangeDto data,
  });

  @PUT('/engine-oil-changes/update')
  Future<void> updateEngineOilChange({
    @Body() required OilChangeDto data,
  });

  @DELETE('/engine-oil-changes/delete')
  Future<void> deleteEngineOilChange({
    @Query('id') required int id,
  });

  @GET('/transmission-oil-changes')
  Future<List<TransmissionOilChangeDto>> getTransmissionOil({
    @Query('carId') required int vehicleId,
  });

  @POST('/transmission-oil-changes/add')
  Future<void> addTransmissionOilChange({
    @Body() required TransmissionOilChangeDto data,
  });

  @PUT('/transmission-oil-changes/update')
  Future<void> updateTransmissionOilChange({
    @Body() required TransmissionOilChangeDto data,
  });

  @DELETE('/transmission-oil-changes/delete')
  Future<void> deleteTransmissionOilChange({
    @Query('id') required int id,
  });

  @GET('/tyres')
  Future<List<VehicleTyreDto>> getTyres({
    @Query('carId') required int vehicleId,
  });

  @POST('/tyres/add')
  Future<void> addTyres({
    @Body() required VehicleTyreDto data,
  });

  @PUT('/tyres/update')
  Future<void> updateTyres({
    @Body() required VehicleTyreDto data,
  });

  @DELETE('/tyres/delete')
  Future<void> deleteTyres({
    @Query('id') required int id,
  });

  @GET('/tyres-switches')
  Future<List<VehicleTyreSwapDto>> getTyreSwitches({
    @Query('carId') required int vehicleId,
  });

  @POST('/tyres-switches/add')
  Future<void> addTyreSwitch({
    @Body() required VehicleTyreSwapDto data,
  });

  @PUT('/tyres-switches/update')
  Future<void> updateTyreSwitch({
    @Body() required VehicleTyreSwapDto data,
  });

  @DELETE('/tyres-switches/delete')
  Future<void> deleteTyreSwitch({
    @Query('id') required int id,
  });

  @GET('/car-services')
  Future<List<VehicleServiceDto>> getVehicleServices({
    @Query('carId') required int vehicleId,
  });

  @POST('/car-services/add')
  Future<void> addVehicleService({
    @Body() required VehicleServiceDto data,
  });

  @PUT('/car-services/update')
  Future<void> updateVehicleService({
    @Body() required VehicleServiceDto data,
  });

  @DELETE('/car-services/delete')
  Future<void> deleteVehicleService({
    @Query('id') required int id,
  });

  @GET('/car-refuelings')
  Future<List<VehicleRefuelDto>> getRefuelings({
    @Query('carId') required int vehicleId,
  });

  @POST('/car-refuelings/add')
  Future<void> addRefueling({
    @Body() required VehicleRefuelDto data,
  });

  @PUT('/car-refuelings/update')
  Future<void> updateRefueling({
    @Body() required VehicleRefuelDto data,
  });

  @DELETE('/car-refuelings/delete')
  Future<void> deleteRefueling({
    @Query('id') required int id,
  });

  @GET('/annual-inspections')
  Future<List<AnnualInspectionDto>> getAnnualInspection({
    @Query('carId') required int vehicleId,
  });

  @POST('/annual-inspections/add')
  Future<void> addAnnualInspection({
    @Body() required AnnualInspectionDto data,
  });

  @PUT('/annual-inspections/update')
  Future<void> updateAnnualInspection({
    @Body() required AnnualInspectionDto data,
  });

  @DELETE('/annual-inspections/delete')
  Future<void> deleteAnnualInspection({
    @Query('id') required int id,
  });
}
