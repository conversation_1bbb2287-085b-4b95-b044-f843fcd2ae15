import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/road_assistance/road_assistance_request_dto.dart';

part 'road_assistance_api.g.dart';

@RestApi(baseUrl: 'api/road-assistance')
abstract class RoadAssistanceApi {
  factory RoadAssistanceApi(Dio dio) = _RoadAssistanceApi;

  @GET('/user-requests')
  Future<List<RoadAssistanceRequestDto>> getRequests();

  @POST('/add-request-for-current-user')
  Future<RoadAssistanceRequestDto> addRequestForCurrentUser({
    @Body() required RoadAssistanceRequestDto data,
  });

  @POST('/add-request-for-other-driver')
  Future<RoadAssistanceRequestDto> addRequestForOtherDriver({
    @Body() required RoadAssistanceRequestDto data,
  });

  @POST('/cancel-request')
  Future<void> cancelRequest({
    @Query('requestId') required int id,
  });
}
