import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:sba/src/api/model/common/discounts_dto.dart';
import 'package:sba/src/api/model/common/questions_dto.dart';
import 'package:sba/src/api/model/common/training_center_dto.dart';

part 'contacts_api.g.dart';

@RestApi(baseUrl: 'api/Contacts')
abstract class ContactsApi {
  factory ContactsApi(Dio dio) = _ContactsApi;

  @GET('/learning-centers')
  Future<List<TrainingCenterDto>> getTrainingCenters();

  @GET('/faq')
  Future<List<QuestionGroupDto>> getFAQ();

  @GET('/discounts')
  Future<List<DiscountCategoryDto>> getDiscounts();

  @POST('/send-legal-case')
  Future<void> sendLegalCase({@Query('message') required String question});
}
