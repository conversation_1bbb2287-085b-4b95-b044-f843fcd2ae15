final class Environment {
  factory Environment() => _instance ??= Environment._fromEnvironment();

  Environment._fromEnvironment()
      : name = const String.fromEnvironment('name'),
        logLevel = const int.fromEnvironment('logLevel'),
        baseUrl = const String.fromEnvironment('baseUrl'),
        oneSignalAppId = const String.fromEnvironment('oneSignalAppId'),
        contactNumber = const String.fromEnvironment('contactNumber');

  static Environment? _instance;
  final String name;
  final int logLevel;
  final String baseUrl;
  final String oneSignalAppId;
  final String contactNumber;
}
