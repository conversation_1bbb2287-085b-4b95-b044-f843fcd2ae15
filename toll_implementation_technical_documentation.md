# Toll Implementation Technical Documentation

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [API Layer](#api-layer)
3. [Repository Layer](#repository-layer)
4. [Feature Layer](#feature-layer)
5. [Data Flow](#data-flow)
6. [<PERSON>rro<PERSON> Handling](#error-handling)
7. [Design Patterns](#design-patterns)
8. [Dependencies](#dependencies)
9. [Areas for Improvement](#areas-for-improvement)

## Architecture Overview

The toll implementation follows a clean architecture pattern with three distinct layers:

```
┌─────────────────────────────────────────────────────────────┐
│                    Feature Layer (UI)                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   TollScreen    │ │ TollCheckScreen │ │  TollBuyScreen  ││
│  │   (Navigation)  │ │  (Check Toll)   │ │  (Purchase)     ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Repository Layer                          │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              TollRepository                             ││
│  │  • Data caching with RxDart streams                    ││
│  │  • Business logic and data transformation              ││
│  │  • Integration with multiple repositories              ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     API Layer                               │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                  TollApi                                ││
│  │  • RESTful endpoints with Retrofit                     ││
│  │  • JSON serialization with json_annotation             ││
│  │  • Comprehensive error handling                        ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## API Layer

### TollApi Class

The `TollApi` class is built using Retrofit and provides a clean interface to the toll service endpoints.

**Base Configuration:**
- Base URL: `api/Vignette`
- Uses Dio for HTTP client
- JSON serialization with `json_annotation`

### Endpoints

#### 1. Get Countries
```dart
@GET('/countries')
Future<List<TollCountryDto>> getCountries();
```
- **Purpose**: Retrieve available toll countries
- **Response**: List of country DTOs with id, code, and name

#### 2. Get Products
```dart
@GET('/products')
Future<List<TollProductDto>> getProducts();
```
- **Purpose**: Fetch available toll products/types
- **Response**: List of products with pricing and validity information

#### 3. Check Toll Validity
```dart
@GET('/check-validity')
Future<TollResponseDto<TollCheckDto>> tollCheck({
  @Query('countryCode') required String countryCode,
  @Query('lpn') required String plateNumber,
});
```
- **Purpose**: Verify existing toll validity for a vehicle
- **Parameters**: Country code and license plate number
- **Response**: Wrapped toll check data with validity status

#### 4. Register Toll
```dart
@POST('/register')
Future<TollResponseDto<TollRegisterDto>> registerToll({
  @Field('saleRows') required List<RegisterTollRequestDto> request,
});
```
- **Purpose**: Register a new toll purchase
- **Parameters**: List of toll registration requests
- **Response**: Registration confirmation with transaction details

#### 5. Activate Toll
```dart
@POST('/activate')
Future<TollResponseDto<TollSaleRowDto>> activateToll({
  @Query('vignetteId') required String id,
});
```
- **Purpose**: Activate a registered toll
- **Parameters**: Vignette ID
- **Response**: Activation confirmation

#### 6. User Toll Management
```dart
@GET('/user-vignettes')
Future<List<TollDto>> getUserToll();

@POST('/add')
Future<TollDto> addToll({@Body() required TollDto dto});

@POST('/update')
Future<void> updateToll({@Body() required TollDto dto});

@POST('/set-vehicle-vignette')
Future<TollDto> updateTollCheck({@Body() required TollDto dto});
```

### Data Transfer Objects (DTOs)

#### TollResponseDto<T>
Generic wrapper for API responses:
```dart
final class TollResponseDto<T> {
  final T data;                    // Actual response data
  final Map<String, dynamic> json; // Raw JSON for debugging
}
```

#### Key DTOs:
- **TollDto**: Complete toll record with all fields
- **TollCheckDto**: Toll validity check result
- **TollCountryDto**: Country information
- **TollProductDto**: Product/pricing information
- **TollRegisterDto**: Registration response
- **TollErrorDto**: Structured error information
- **RegisterTollRequestDto**: Registration request payload

### Authentication
- Uses Dio interceptors for authentication (inherited from base configuration)
- No explicit authentication mechanisms in TollApi class

## Repository Layer

### TollRepository Class

The `TollRepository` serves as the data access layer with sophisticated caching and business logic.

#### Dependencies
```dart
TollRepository({
  required Logger logger,
  required TollApi tollApi,
  required AuthRepository authRepository,
  required UserRepository userRepository,
  required VehicleRepository vehicleRepository,
  required PaymentProcessor paymentProcessor,
})
```

#### Caching Strategy

**Stream-based Caching with RxDart:**
```dart
final _cachedResponse = BehaviorSubject<Result<List<TollData>>?>.seeded(null);
late final Stream<Result<List<TollData>>> _tollStream;
```

**Cache Invalidation:**
- Automatic refresh on authentication state changes
- Manual refresh via `refresh()` method
- Cache updates on successful operations

#### Key Methods

##### 1. Data Retrieval
```dart
Stream<Result<List<TollData>>> get toll => _tollStream;
Future<TollData?> getValidTollForVehicle(String plateNumber);
Future<Result<List<TollCountry>>> getCountries();
Future<Result<List<TollProduct>>> getProducts();
```

##### 2. Toll Operations
```dart
Future<Result<TollCheckData?>> checkToll({
  required TollCountry country,
  required String plateNumber,
});

Future<Result<TollData>> registerToll(TollRegisterData data);
Future<Result<TollData>> activateToll(TollData data); // Not implemented
```

#### Data Transformation

**DTO to Domain Model Conversion:**
- `TollDto` → `TollData`
- `TollCountryDto` → `TollCountry`
- `TollProductDto` → `TollProduct`
- `TollCheckDto` → `TollCheckData`

**Business Logic:**
- Validity checking based on date ranges
- Status management with `TollStatus` enum
- User type handling (normal vs guest users)

#### Integration Patterns

**Multi-Repository Coordination:**
- Fetches user data for toll operations
- Validates vehicle information
- Updates local cache on successful operations
- Coordinates with authentication state

## Feature Layer

### Architecture Pattern: Elementary

The feature layer uses the Elementary pattern for state management:
- **Screen**: UI widget (extends `ElementaryWidget`)
- **WidgetModel**: State management and business logic
- **Model**: Data access and repository integration

### Main Navigation Screen

#### TollScreen
**Purpose**: Entry point with navigation options

<augment_code_snippet path="lib/src/feature/toll/view/toll_screen.dart" mode="EXCERPT">
````dart
class TollScreen extends ElementaryWidget<ITollWidgetModel> {
  @override
  Widget build(ITollWidgetModel wm) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, _) => [
          SliverTitle(text: context.l10n.navigation_toll),
        ],
        body: CustomScrollView(
          slivers: [
            SliverGrid.count(
              crossAxisCount: 2,
              children: [
                NavigationBox(
                  icon: Assets.icons.searchCheck.svg(),
                  text: context.l10n.navigation_toll_check,
                  onTap: wm.onCheckTap,
                ),
                NavigationBox(
                  icon: Assets.icons.addShoppingCart.svg(),
                  text: context.l10n.navigation_toll_buy,
                  onTap: wm.onBuyTap,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
````
</augment_code_snippet>

### Toll Check Feature

#### TollCheckScreen
**Purpose**: Check existing toll validity

**State Management:**
```dart
abstract interface class ITollCheckWidgetModel implements IWidgetModel {
  void onCheck(TollCheckRecord record);
  StateNotifier<List<TollCountry>> get countries;
  StateNotifier<List<VehicleData>> get vehicles;
  EntityStateNotifier<TollCheckData?> get checkState;
}
```

**User Flow:**
1. Select country from dropdown
2. Choose vehicle or enter plate number manually
3. Submit check request
4. Display validity results

### Toll Purchase Feature

#### TollBuyScreen
**Purpose**: Multi-step toll purchase process

**State Management:**
```dart
abstract interface class ITollBuyWidgetModel implements IWidgetModel {
  StateNotifier<TollBuyStep> get step;
  StateNotifier<TollRegisterData> get data;
  StateNotifier<List<TollProduct>> get products;
  // ... other state notifiers
}
```

**Purchase Steps:**
1. **Vehicle Type Selection**: Choose vehicle category
2. **Product Selection**: Select toll type and duration
3. **Data Entry**: Enter vehicle and contact details
4. **Payment Type**: Choose payment method
5. **Payment Processing**: Complete transaction

### Navigation Flow

**Route Configuration:**
```dart
TypedGoRoute<TollRoute>(
  path: '/toll',
  routes: [
    TypedGoRoute<CheckTollRoute>(path: 'check'),
    TypedGoRoute<BuyTollRoute>(path: 'buy'),
  ],
),
```

**Navigation Pattern:**
- Uses `go_router` with type-safe routing
- Declarative navigation with `GoRouteData`
- Nested routes for feature organization

## Data Flow

### Toll Check Flow

```
User Input (Country + Plate)
    ↓
TollCheckWidgetModel.onCheck()
    ↓
TollCheckModel.tollCheck()
    ↓
TollRepository.checkToll()
    ↓
TollApi.tollCheck()
    ↓
API Response (TollResponseDto<TollCheckDto>)
    ↓
Transform to TollCheckData
    ↓
Update UI State
    ↓
Display Results to User
```

### Toll Purchase Flow

```
User Selects Vehicle Type
    ↓
User Selects Product
    ↓
User Enters Data
    ↓
TollBuyWidgetModel.onPaymentTap()
    ↓
TollBuyModel.registerToll()
    ↓
TollRepository.registerToll()
    ↓
TollApi.registerToll() + TollApi.addToll()
    ↓
Payment Processing
    ↓
Update Cache & UI
    ↓
Show Confirmation
```

### Data Caching Flow

```
Authentication State Change
    ↓
TollRepository._authRepository.loggedInStream
    ↓
Cache Invalidation (_cachedResponse.add(null))
    ↓
_getTollData() called
    ↓
TollApi.getUserToll()
    ↓
Transform DTOs to Domain Models
    ↓
Update Stream (_tollStream)
    ↓
UI Automatically Updates
```

## Error Handling

### Result Type System

The implementation uses a comprehensive `Result<T>` type for error handling:

```dart
sealed class Result<S> {
  factory Result.success(S data);
  factory Result.networkError();
  factory Result.httpError(int code, String message, {dynamic data});
  factory Result.canceled({S? data});
  factory Result.paymentError();
  factory Result.tollError(TollErrorDto error);
  factory Result.otherError(Object error);
}
```

### Error Handling Functions

#### callWithResult<T>
**Purpose**: Standard API call wrapper with Dio exception handling

```dart
Future<Result<T>> callWithResult<T>(CallFunction<T> call) async {
  try {
    final result = await call();
    return Result.success(result);
  } on DioException catch (e) {
    // Handle different DioException types
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return Result.networkError();
      case DioExceptionType.badResponse:
        return Result.httpError(code, message, data: data);
      // ... other cases
    }
  }
}
```

#### callWithTollResult<T>
**Purpose**: Specialized wrapper for toll API calls with custom error parsing

```dart
Future<Result<T>> callWithTollResult<T>(CallFunction<T> call) async {
  final result = await callWithResult(call);
  if (result is HttpError && error.code == 500) {
    // Parse toll-specific error format
    final tollError = TollErrorDto.fromJson(parsedJson);
    return Result.tollError(tollError);
  }
  return result;
}
```

#### callWithCachedResult<T>
**Purpose**: Adds memory caching to API calls

```dart
Future<Result<T>> callWithCachedResult<T>(
  CallFunction<T> call, {
  String? cacheKey,
  Duration? ttl,
}) async {
  final key = cacheKey ?? call.toString();
  final cached = MemoryCache.instance.read<Result<T>>(key);

  if (cached != null) return cached;

  final result = await callWithResult(call);
  if (result.isSuccess) {
    MemoryCache.instance.createIfAbsent(key, result, expiry: ttl);
  }
  return result;
}
```

### Toll-Specific Error Codes

The system handles specific toll error codes with localized messages:

```dart
TollError(error: final error) => switch (error.code) {
  202 || 206 || 207 => context.l10n.error_toll_start_date,
  203 => context.l10n.error_toll_email,
  204 => context.l10n.error_toll_vehicle,
  210 => context.l10n.error_toll_activated,
  217 => context.l10n.error_toll_has_prucase,
  _ => context.l10n.error_general_text,
}
```

### UI Error Handling

**Loading States:**
- Uses `EntityStateNotifier` for loading/content/error states
- Shows loading dialogs during API calls
- Displays error dialogs with localized messages

**Error Recovery:**
- Retry mechanisms for network errors
- Graceful degradation for non-critical failures
- User-friendly error messages
## Design Patterns

### 1. Repository Pattern
**Implementation**: `TollRepository` class
- **Purpose**: Abstracts data access logic from business logic
- **Benefits**:
  - Centralized data access
  - Testability through dependency injection
  - Consistent error handling
  - Caching strategy encapsulation

### 2. Elementary Pattern (MVP variant)
**Components**:
- **Model**: Data access and business logic (`TollCheckModel`, `TollBuyModel`)
- **View**: UI widgets (`TollCheckScreen`, `TollBuyScreen`)
- **WidgetModel**: State management and user interaction handling

**Benefits**:
- Clear separation of concerns
- Testable business logic
- Reactive state management
- Lifecycle management

### 3. Result Pattern
**Implementation**: `Result<T>` sealed class
- **Purpose**: Functional error handling without exceptions
- **Benefits**:
  - Explicit error handling
  - Type-safe error propagation
  - Composable error transformations
  - Consistent error representation

### 4. Factory Pattern
**Implementation**: DTO factories and widget model factories
```dart
factory TollDto.fromJson(Map<String, dynamic> json) => _$TollDtoFromJson(json);
TollCheckWidgetModel defaultTollCheckWidgetModelFactory(BuildContext context);
```

### 5. Observer Pattern
**Implementation**: RxDart streams for reactive programming
- **Purpose**: Automatic UI updates on data changes
- **Benefits**:
  - Reactive data flow
  - Automatic cache invalidation
  - Decoupled components

### 6. Strategy Pattern
**Implementation**: Different error handling strategies
- `callWithResult`: Standard error handling
- `callWithTollResult`: Toll-specific error parsing
- `callWithCachedResult`: Cached responses

## Dependencies

### Core Dependencies

#### API Layer
```yaml
dependencies:
  dio: ^5.3.2                    # HTTP client
  retrofit: ^4.0.3               # REST API client generator
  json_annotation: ^4.8.1       # JSON serialization annotations

dev_dependencies:
  retrofit_generator: ^8.0.4     # Code generation for Retrofit
  json_serializable: ^6.7.1     # JSON serialization code generation
```

#### Repository Layer
```yaml
dependencies:
  rxdart: ^0.27.7               # Reactive extensions
  logger: ^2.0.2                # Logging
  memory_cache: ^2.0.2          # In-memory caching
  collection: ^1.17.2           # Collection utilities
```

#### Feature Layer
```yaml
dependencies:
  elementary: ^2.0.0            # Elementary architecture
  elementary_helper: ^2.0.0     # Elementary helpers
  flutter_form_builder: ^9.1.1  # Form building
  form_builder_validators: ^9.1.0 # Form validation
  go_router: ^12.1.3            # Navigation
  gap: ^3.0.1                   # Spacing widgets
```

### Dependency Injection

**Service Locator Pattern** with `get_it`:
```dart
// Registration (typically in DI setup)
get<TollRepository>()
get<VehicleRepository>()
get<UserRepository>()
get<AuthRepository>()

// Usage in widget models
TollCheckModel(
  tollRepository: get(),
  vehicleRepository: get(),
  errorHandler: get(),
)
```

### Repository Dependencies

**TollRepository** depends on:
- `TollApi`: API communication
- `AuthRepository`: Authentication state
- `UserRepository`: User data
- `VehicleRepository`: Vehicle information
- `PaymentProcessor`: Payment handling
- `Logger`: Logging

**Dependency Graph**:
```
TollRepository
├── TollApi (Dio)
├── AuthRepository
├── UserRepository
├── VehicleRepository
├── PaymentProcessor
└── Logger
```

## Key Classes and Responsibilities

### API Layer Classes

| Class | Responsibility | Key Methods |
|-------|---------------|-------------|
| `TollApi` | REST API interface | `getCountries()`, `tollCheck()`, `registerToll()` |
| `TollDto` | Complete toll data transfer | `fromJson()`, `toJson()` |
| `TollCheckDto` | Toll validity check data | `fromJson()`, `toJson()` |
| `TollResponseDto<T>` | Generic API response wrapper | `fromJson()`, `toJson()` |
| `TollErrorDto` | Structured error information | `fromJson()`, `toJson()` |

### Repository Layer Classes

| Class | Responsibility | Key Methods |
|-------|---------------|-------------|
| `TollRepository` | Data access and caching | `checkToll()`, `registerToll()`, `refresh()` |
| `TollData` | Domain model for toll | `fromDto()`, `toDto()`, `isValid` |
| `TollCountry` | Country domain model | `fromDto()`, equality operators |
| `TollProduct` | Product domain model | `fromDto()`, equality operators |
| `TollStatus` | Toll status enumeration | `fromCode()` |

### Feature Layer Classes

| Class | Responsibility | Key Methods |
|-------|---------------|-------------|
| `TollScreen` | Main navigation UI | `build()` |
| `TollCheckScreen` | Toll check UI | `build()` |
| `TollBuyScreen` | Toll purchase UI | `build()` |
| `TollCheckWidgetModel` | Check feature state management | `onCheck()` |
| `TollBuyWidgetModel` | Purchase feature state management | `onPaymentTap()`, `onDataTap()` |
| `TollCheckModel` | Check feature data access | `tollCheck()`, `countries`, `vehicles` |
| `TollBuyModel` | Purchase feature data access | `registerToll()`, `products` |

## Areas for Improvement

### 1. Code Quality Issues

#### Incomplete Implementation
- **Issue**: `activateToll()` method returns `Result.otherError('Not implemented')`
- **Impact**: Feature incompleteness
- **Recommendation**: Complete the activation flow or remove if not needed

#### Error Handling Inconsistencies
- **Issue**: Mixed error handling patterns (some methods use try-catch, others rely on Result pattern)
- **Recommendation**: Standardize on Result pattern throughout

#### Magic Numbers
- **Issue**: Hard-coded error codes (202, 203, 204, etc.) without constants
- **Recommendation**: Define error code constants with descriptive names

### 2. Architecture Improvements

#### Repository Coupling
- **Issue**: `TollRepository` depends on multiple repositories, creating tight coupling
- **Recommendation**: Consider using domain services or use cases to coordinate between repositories

#### Caching Strategy
- **Issue**: Simple memory cache without expiration strategies or cache size limits
- **Recommendation**: Implement more sophisticated caching with TTL and LRU eviction

#### State Management Complexity
- **Issue**: Multiple `StateNotifier` instances in widget models can become hard to manage
- **Recommendation**: Consider using a single state object or state machine pattern

### 3. Performance Optimizations

#### Network Efficiency
- **Issue**: No request deduplication or batching
- **Recommendation**: Implement request deduplication for concurrent identical requests

#### Memory Usage
- **Issue**: Unlimited cache growth potential
- **Recommendation**: Implement cache size limits and cleanup strategies

#### UI Performance
- **Issue**: Potential unnecessary rebuilds with multiple state notifiers
- **Recommendation**: Use `Selector` widgets or optimize state granularity

### 4. Testing Improvements

#### Test Coverage
- **Issue**: No visible test files in the analyzed code
- **Recommendation**: Implement comprehensive unit and integration tests

#### Testability
- **Issue**: Some dependencies are hard to mock (static method calls)
- **Recommendation**: Inject all dependencies and avoid static calls

### 5. Security Considerations

#### Data Validation
- **Issue**: Limited input validation on API requests
- **Recommendation**: Add comprehensive input validation and sanitization

#### Error Information Exposure
- **Issue**: Raw error data might expose sensitive information
- **Recommendation**: Sanitize error messages before displaying to users

### 6. User Experience Enhancements

#### Offline Support
- **Issue**: No offline capabilities
- **Recommendation**: Implement offline data storage and sync

#### Loading States
- **Issue**: Basic loading dialogs without progress indication
- **Recommendation**: Add progress indicators and better loading UX

#### Error Recovery
- **Issue**: Limited error recovery options
- **Recommendation**: Add retry mechanisms and alternative flows

### 7. Maintenance and Scalability

#### Code Generation Dependencies
- **Issue**: Heavy reliance on code generation
- **Recommendation**: Ensure build process is robust and well-documented

#### Localization
- **Issue**: Hard-coded error messages in some places
- **Recommendation**: Ensure all user-facing text is localized

#### Documentation
- **Issue**: Limited inline documentation
- **Recommendation**: Add comprehensive code documentation and API docs

## Conclusion

The toll implementation demonstrates a well-structured Flutter application following clean architecture principles. The use of the Repository pattern, Elementary architecture, and functional error handling with the Result type creates a maintainable and testable codebase.

Key strengths include:
- Clear separation of concerns across layers
- Comprehensive error handling
- Reactive data flow with caching
- Type-safe navigation and API calls

Areas for improvement focus on completing unfinished features, optimizing performance, and enhancing the user experience with better offline support and error recovery mechanisms.

The architecture provides a solid foundation that can be extended and maintained as the application grows.