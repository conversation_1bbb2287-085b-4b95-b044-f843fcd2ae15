# Toll Implementation Technical Documentation

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [API Layer](#api-layer)
3. [Repository Layer](#repository-layer)
4. [Data Flow](#data-flow)
5. [Error Handling](#error-handling)
6. [Design Patterns](#design-patterns)
7. [Dependencies](#dependencies)
8. [Areas for Improvement](#areas-for-improvement)

## Architecture Overview

The toll implementation follows a clean architecture pattern with two main layers:

```
┌─────────────────────────────────────────────────────────────┐
│                   Repository Layer                          │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              TollRepository                             ││
│  │  • Data caching with RxDart streams                    ││
│  │  • Business logic and data transformation              ││
│  │  • Integration with multiple repositories              ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     API Layer                               │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                  TollApi                                ││
│  │  • RESTful endpoints with Retrofit                     ││
│  │  • JSON serialization with json_annotation             ││
│  │  • Comprehensive error handling                        ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## API Layer

### TollApi Class

The `TollApi` class is built using Retrofit and provides a clean interface to the toll service endpoints.

**Base Configuration:**
- Base URL: `api/Vignette`
- Uses Dio for HTTP client
- JSON serialization with `json_annotation`

### Endpoints

#### 1. Get Countries
```dart
@GET('/countries')
Future<List<TollCountryDto>> getCountries();
```
- **Purpose**: Retrieve available toll countries
- **Response**: List of country DTOs with id, code, and name

#### 2. Get Products
```dart
@GET('/products')
Future<List<TollProductDto>> getProducts();
```
- **Purpose**: Fetch available toll products/types
- **Response**: List of products with pricing and validity information

#### 3. Check Toll Validity
```dart
@GET('/check-validity')
Future<TollResponseDto<TollCheckDto>> tollCheck({
  @Query('countryCode') required String countryCode,
  @Query('lpn') required String plateNumber,
});
```
- **Purpose**: Verify existing toll validity for a vehicle
- **Parameters**: Country code and license plate number
- **Response**: Wrapped toll check data with validity status

#### 4. Register Toll
```dart
@POST('/register')
Future<TollResponseDto<TollRegisterDto>> registerToll({
  @Field('saleRows') required List<RegisterTollRequestDto> request,
});
```
- **Purpose**: Register a new toll purchase
- **Parameters**: List of toll registration requests
- **Response**: Registration confirmation with transaction details

#### 5. Activate Toll
```dart
@POST('/activate')
Future<TollResponseDto<TollSaleRowDto>> activateToll({
  @Query('vignetteId') required String id,
});
```
- **Purpose**: Activate a registered toll
- **Parameters**: Vignette ID
- **Response**: Activation confirmation

#### 6. User Toll Management
```dart
@GET('/user-vignettes')
Future<List<TollDto>> getUserToll();

@POST('/add')
Future<TollDto> addToll({@Body() required TollDto dto});

@POST('/update')
Future<void> updateToll({@Body() required TollDto dto});

@POST('/set-vehicle-vignette')
Future<TollDto> updateTollCheck({@Body() required TollDto dto});
```

### Data Transfer Objects (DTOs)

#### TollResponseDto<T>
Generic wrapper for API responses:
```dart
final class TollResponseDto<T> {
  final T data;                    // Actual response data
  final Map<String, dynamic> json; // Raw JSON for debugging
}
```

#### Key DTOs:
- **TollDto**: Complete toll record with all fields
- **TollCheckDto**: Toll validity check result
- **TollCountryDto**: Country information
- **TollProductDto**: Product/pricing information
- **TollRegisterDto**: Registration response
- **TollErrorDto**: Structured error information
- **RegisterTollRequestDto**: Registration request payload

### Authentication
- Uses Dio interceptors for authentication (inherited from base configuration)
- No explicit authentication mechanisms in TollApi class

## Repository Layer

### TollRepository Class

The `TollRepository` serves as the data access layer with sophisticated caching and business logic.

#### Dependencies
```dart
TollRepository({
  required Logger logger,
  required TollApi tollApi,
  required AuthRepository authRepository,
  required UserRepository userRepository,
  required VehicleRepository vehicleRepository,
  required PaymentProcessor paymentProcessor,
})
```

#### Caching Strategy

**Stream-based Caching with RxDart:**
```dart
final _cachedResponse = BehaviorSubject<Result<List<TollData>>?>.seeded(null);
late final Stream<Result<List<TollData>>> _tollStream;
```

**Cache Invalidation:**
- Automatic refresh on authentication state changes
- Manual refresh via `refresh()` method
- Cache updates on successful operations

#### Key Methods

##### 1. Data Retrieval
```dart
Stream<Result<List<TollData>>> get toll => _tollStream;
Future<TollData?> getValidTollForVehicle(String plateNumber);
Future<Result<List<TollCountry>>> getCountries();
Future<Result<List<TollProduct>>> getProducts();
```

##### 2. Toll Operations
```dart
Future<Result<TollCheckData?>> checkToll({
  required TollCountry country,
  required String plateNumber,
});

Future<Result<TollData>> registerToll(TollRegisterData data);
Future<Result<TollData>> activateToll(TollData data); // Not implemented
```

#### Data Transformation

**DTO to Domain Model Conversion:**
- `TollDto` → `TollData`
- `TollCountryDto` → `TollCountry`
- `TollProductDto` → `TollProduct`
- `TollCheckDto` → `TollCheckData`

**Business Logic:**
- Validity checking based on date ranges
- Status management with `TollStatus` enum
- User type handling (normal vs guest users)

#### Integration Patterns

**Multi-Repository Coordination:**
- Fetches user data for toll operations
- Validates vehicle information
- Updates local cache on successful operations
- Coordinates with authentication state



## Data Flow

### Toll Check Flow

```
Client Request (Country + Plate)
    ↓
TollRepository.checkToll()
    ↓
TollApi.tollCheck()
    ↓
API Response (TollResponseDto<TollCheckDto>)
    ↓
Transform to TollCheckData
    ↓
Update Cache
    ↓
Return Result to Client
```

### Toll Purchase Flow

```
Client Registration Request
    ↓
TollRepository.registerToll()
    ↓
TollApi.registerToll() + TollApi.addToll()
    ↓
API Response Processing
    ↓
Transform to TollData
    ↓
Update Cache
    ↓
Return Result to Client
```

### Data Caching Flow

```
Authentication State Change
    ↓
TollRepository._authRepository.loggedInStream
    ↓
Cache Invalidation (_cachedResponse.add(null))
    ↓
_getTollData() called
    ↓
TollApi.getUserToll()
    ↓
Transform DTOs to Domain Models
    ↓
Update Stream (_tollStream)
    ↓
Cached Data Available for Clients
```

## Error Handling

### Result Type System

The implementation uses a comprehensive `Result<T>` type for error handling:

```dart
sealed class Result<S> {
  factory Result.success(S data);
  factory Result.networkError();
  factory Result.httpError(int code, String message, {dynamic data});
  factory Result.canceled({S? data});
  factory Result.paymentError();
  factory Result.tollError(TollErrorDto error);
  factory Result.otherError(Object error);
}
```

### Error Handling Functions

#### callWithResult<T>
**Purpose**: Standard API call wrapper with Dio exception handling

```dart
Future<Result<T>> callWithResult<T>(CallFunction<T> call) async {
  try {
    final result = await call();
    return Result.success(result);
  } on DioException catch (e) {
    // Handle different DioException types
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return Result.networkError();
      case DioExceptionType.badResponse:
        return Result.httpError(code, message, data: data);
      // ... other cases
    }
  }
}
```

#### callWithTollResult<T>
**Purpose**: Specialized wrapper for toll API calls with custom error parsing

```dart
Future<Result<T>> callWithTollResult<T>(CallFunction<T> call) async {
  final result = await callWithResult(call);
  if (result is HttpError && error.code == 500) {
    // Parse toll-specific error format
    final tollError = TollErrorDto.fromJson(parsedJson);
    return Result.tollError(tollError);
  }
  return result;
}
```

#### callWithCachedResult<T>
**Purpose**: Adds memory caching to API calls

```dart
Future<Result<T>> callWithCachedResult<T>(
  CallFunction<T> call, {
  String? cacheKey,
  Duration? ttl,
}) async {
  final key = cacheKey ?? call.toString();
  final cached = MemoryCache.instance.read<Result<T>>(key);

  if (cached != null) return cached;

  final result = await callWithResult(call);
  if (result.isSuccess) {
    MemoryCache.instance.createIfAbsent(key, result, expiry: ttl);
  }
  return result;
}
```

### Toll-Specific Error Codes

The system handles specific toll error codes with localized messages:

```dart
TollError(error: final error) => switch (error.code) {
  202 || 206 || 207 => context.l10n.error_toll_start_date,
  203 => context.l10n.error_toll_email,
  204 => context.l10n.error_toll_vehicle,
  210 => context.l10n.error_toll_activated,
  217 => context.l10n.error_toll_has_prucase,
  _ => context.l10n.error_general_text,
}
```

### UI Error Handling

**Loading States:**
- Uses `EntityStateNotifier` for loading/content/error states
- Shows loading dialogs during API calls
- Displays error dialogs with localized messages

**Error Recovery:**
- Retry mechanisms for network errors
- Graceful degradation for non-critical failures
- User-friendly error messages
## Design Patterns

### 1. Repository Pattern
**Implementation**: `TollRepository` class
- **Purpose**: Abstracts data access logic from business logic
- **Benefits**:
  - Centralized data access
  - Testability through dependency injection
  - Consistent error handling
  - Caching strategy encapsulation



### 2. Result Pattern
**Implementation**: `Result<T>` sealed class
- **Purpose**: Functional error handling without exceptions
- **Benefits**:
  - Explicit error handling
  - Type-safe error propagation
  - Composable error transformations
  - Consistent error representation

### 3. Factory Pattern
**Implementation**: DTO factories
```dart
factory TollDto.fromJson(Map<String, dynamic> json) => _$TollDtoFromJson(json);
factory TollCountry.fromDto(TollCountryDto dto) => TollCountry(...);
```

### 4. Observer Pattern
**Implementation**: RxDart streams for reactive programming
- **Purpose**: Automatic data updates and cache invalidation
- **Benefits**:
  - Reactive data flow
  - Automatic cache invalidation
  - Decoupled components

### 5. Strategy Pattern
**Implementation**: Different error handling strategies
- `callWithResult`: Standard error handling
- `callWithTollResult`: Toll-specific error parsing
- `callWithCachedResult`: Cached responses

## Dependencies

### Core Dependencies

#### API Layer
```yaml
dependencies:
  dio: ^5.3.2                    # HTTP client
  retrofit: ^4.0.3               # REST API client generator
  json_annotation: ^4.8.1       # JSON serialization annotations

dev_dependencies:
  retrofit_generator: ^8.0.4     # Code generation for Retrofit
  json_serializable: ^6.7.1     # JSON serialization code generation
```

#### Repository Layer
```yaml
dependencies:
  rxdart: ^0.27.7               # Reactive extensions
  logger: ^2.0.2                # Logging
  memory_cache: ^2.0.2          # In-memory caching
  collection: ^1.17.2           # Collection utilities
```



### Dependency Injection

**Service Locator Pattern** with `get_it`:
```dart
// Registration (typically in DI setup)
get<TollRepository>()
get<VehicleRepository>()
get<UserRepository>()
get<AuthRepository>()

// Usage in repository
TollRepository(
  tollApi: get(),
  authRepository: get(),
  userRepository: get(),
  vehicleRepository: get(),
  paymentProcessor: get(),
  logger: get(),
)
```

### Repository Dependencies

**TollRepository** depends on:
- `TollApi`: API communication
- `AuthRepository`: Authentication state
- `UserRepository`: User data
- `VehicleRepository`: Vehicle information
- `PaymentProcessor`: Payment handling
- `Logger`: Logging

**Dependency Graph**:
```
TollRepository
├── TollApi (Dio)
├── AuthRepository
├── UserRepository
├── VehicleRepository
├── PaymentProcessor
└── Logger
```

## Key Classes and Responsibilities

### API Layer Classes

| Class | Responsibility | Key Methods |
|-------|---------------|-------------|
| `TollApi` | REST API interface | `getCountries()`, `tollCheck()`, `registerToll()` |
| `TollDto` | Complete toll data transfer | `fromJson()`, `toJson()` |
| `TollCheckDto` | Toll validity check data | `fromJson()`, `toJson()` |
| `TollResponseDto<T>` | Generic API response wrapper | `fromJson()`, `toJson()` |
| `TollErrorDto` | Structured error information | `fromJson()`, `toJson()` |

### Repository Layer Classes

| Class | Responsibility | Key Methods |
|-------|---------------|-------------|
| `TollRepository` | Data access and caching | `checkToll()`, `registerToll()`, `refresh()` |
| `TollData` | Domain model for toll | `fromDto()`, `toDto()`, `isValid` |
| `TollCountry` | Country domain model | `fromDto()`, equality operators |
| `TollProduct` | Product domain model | `fromDto()`, equality operators |
| `TollStatus` | Toll status enumeration | `fromCode()` |



## Areas for Improvement

### 1. Code Quality Issues

#### Incomplete Implementation
- **Issue**: `activateToll()` method returns `Result.otherError('Not implemented')`
- **Impact**: Feature incompleteness
- **Recommendation**: Complete the activation flow or remove if not needed

#### Error Handling Inconsistencies
- **Issue**: Mixed error handling patterns (some methods use try-catch, others rely on Result pattern)
- **Recommendation**: Standardize on Result pattern throughout

#### Magic Numbers
- **Issue**: Hard-coded error codes (202, 203, 204, etc.) without constants
- **Recommendation**: Define error code constants with descriptive names

### 2. Architecture Improvements

#### Repository Coupling
- **Issue**: `TollRepository` depends on multiple repositories, creating tight coupling
- **Recommendation**: Consider using domain services or use cases to coordinate between repositories

#### Caching Strategy
- **Issue**: Simple memory cache without expiration strategies or cache size limits
- **Recommendation**: Implement more sophisticated caching with TTL and LRU eviction



### 3. Performance Optimizations

#### Network Efficiency
- **Issue**: No request deduplication or batching
- **Recommendation**: Implement request deduplication for concurrent identical requests

#### Memory Usage
- **Issue**: Unlimited cache growth potential
- **Recommendation**: Implement cache size limits and cleanup strategies



### 4. Testing Improvements

#### Test Coverage
- **Issue**: No visible test files in the analyzed code
- **Recommendation**: Implement comprehensive unit and integration tests

#### Testability
- **Issue**: Some dependencies are hard to mock (static method calls)
- **Recommendation**: Inject all dependencies and avoid static calls

### 5. Security Considerations

#### Data Validation
- **Issue**: Limited input validation on API requests
- **Recommendation**: Add comprehensive input validation and sanitization

#### Error Information Exposure
- **Issue**: Raw error data might expose sensitive information
- **Recommendation**: Sanitize error messages before displaying to users

### 6. User Experience Enhancements

#### Offline Support
- **Issue**: No offline capabilities
- **Recommendation**: Implement offline data storage and sync

#### Error Recovery
- **Issue**: Limited error recovery options for API failures
- **Recommendation**: Add retry mechanisms and fallback strategies

### 7. Maintenance and Scalability

#### Code Generation Dependencies
- **Issue**: Heavy reliance on code generation
- **Recommendation**: Ensure build process is robust and well-documented

#### Localization
- **Issue**: Hard-coded error messages in some places
- **Recommendation**: Ensure all user-facing text is localized

#### Documentation
- **Issue**: Limited inline documentation
- **Recommendation**: Add comprehensive code documentation and API docs

## Conclusion

The toll implementation demonstrates a well-structured backend service layer following clean architecture principles. The use of the Repository pattern and functional error handling with the Result type creates a maintainable and testable codebase.

Key strengths include:
- Clear separation of concerns between API and Repository layers
- Comprehensive error handling with specialized toll error processing
- Reactive data flow with sophisticated caching strategies
- Type-safe API calls with comprehensive DTOs
- Multi-repository coordination for complex business logic

Areas for improvement focus on completing unfinished features, optimizing performance, and enhancing error recovery mechanisms.

The architecture provides a solid foundation that can be extended and maintained as the application grows.