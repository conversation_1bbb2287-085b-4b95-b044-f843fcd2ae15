name: Internal Deploy

on:
  push:
    tags:
      - "internal-v*.*.*" # on every version tag will build a new android artifact example: v3.1.2+6

permissions:
  contents: write

jobs:
  build:
    name: build_artifacts_and_release
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4

      - name: install_release_certificates
        env:
          P12_PASSWORD: '123321'
          KEYCHAIN_PASSWORD: 'ChaChing'
          CERTIFICATE_PATH: 'ios/SBAAppStoreCertificate.p12'
          PP_PATH: 'ios/SBAProvisioningProfile.mobileprovision'
        run: |
          # create variables
          KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db
          
          # create temporary keychain
          security create-keychain -p $KEYCHAIN_PASSWORD $KEYCHAIN_PATH
          security set-keychain-settings -lut 21600 $KEYCHAIN_PATH
          security unlock-keychain -p $KEYCHAIN_PASSWORD $KEYCHAIN_PATH
          
          # import certificate to keychain
          security import $CERTIFICATE_PATH -P $P12_PASSWORD -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
          security set-key-partition-list -S apple-tool:,apple: -k $KEYCHAIN_PASSWORD $KEYCHAIN_PATH
          security list-keychain -d user -s $KEYCHAIN_PATH
          
          # apply provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles  

      - uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version: 3.35.2
          cache: true

      - uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: latest-stable

      - name: get_packages
        run: flutter pub get

      - name: build_android
        run: flutter build apk --release --dart-define-from-file=config/dev-config.json

      - name: build_ios
        run: flutter build ipa --export-options-plist=ios/exportOptions.plist --dart-define-from-file=config/dev-config.json

      - name: create_github_release
        uses: ncipollo/release-action@v1
        with:
          artifacts: "build/app/outputs/flutter-apk/app-release.apk,build/ios/ipa/sba.ipa"
          allowUpdates: true

      - name: save_android
        uses: actions/upload-artifact@v4
        with:
          name: APK
          path: build/app/outputs/flutter-apk/app-release.apk

      - name: save_ios
        uses: actions/upload-artifact@v4
        with:
          name: IPA
          path: build/ios/ipa/sba.ipa
  release_internal:
    name: release_artifacts
    needs: [ build ]
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4

      - name: get_android_artifact
        uses: actions/download-artifact@v4
        with:
          name: APK

      - name: get_ios_artifact
        uses: actions/download-artifact@v4
        with:
          name: IPA

      - name: release_android
        uses: testappio/github-action@v5
        with:
          api_token: ${{secrets.TESTAPPIO_API_TOKEN}}
          app_id: ${{secrets.TESTAPPIO_APP_ID}}
          file: app-release.apk
          release_notes: ""
          git_release_notes: true
          include_git_commit_id: true
          notify: true

      - name: configure_appstore_upload_key
        env:
          API_PATH: 'ios/SBAAuthKey.p8'
          API_KEY: ${{ secrets.iOS_APPSTORE_CONNECT_API_KEY_ID }}
        run: |
          mkdir -p ~/private_keys
          ls ~/private_keys
          cp $API_PATH ~/private_keys/AuthKey_$API_KEY.p8
          echo "After saving: "
          ls ~/private_keys    

      - name: upload_to_appstore
        env:
          ISSUER_ID: ${{ secrets.IOS_APPSTORE_CONNECT_ISSUER_ID }}
          API_KEY: ${{ secrets.IOS_APPSTORE_CONNECT_API_KEY_ID }}
        run: |
          echo "Before uploading: "
          ls ~/private_keys
          xcrun altool --upload-app -f $GITHUB_WORKSPACE/sba.ipa -t ios --apiKey $API_KEY --apiIssuer "$ISSUER_ID"
          ls ~/private_keys