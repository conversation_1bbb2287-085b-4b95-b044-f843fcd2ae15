name: sba
description: "SBA Mobile Application"

publish_to: 'none'

version: 0.0.6+12

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_svg: ^2.2.1
  go_router: ^16.2.1
  elementary: ^3.2.1
  elementary_helper: ^1.0.3
  flutter_form_builder: ^10.2.0
  form_builder_extra_fields: ^12.1.0
  form_builder_validators: ^11.2.0
  gap: ^3.0.1
  collection: ^1.19.1
  timeago: ^3.7.1
  skeletonizer: ^2.1.0+1
  get_it: ^8.2.0
  latlong2: ^0.9.1
  flutter_map: ^7.0.2
  flutter_map_marker_popup: ^7.0.0
  flutter_map_location_marker: ^9.1.1
  flutter_map_animations: ^0.8.0
  modal_bottom_sheet: ^3.0.0
  retrofit: ^4.7.2
  logger: ^2.6.1
  dio: ^5.9.0
  json_annotation: ^4.9.0
  rxdart: ^0.28.0
  cached_network_image: ^3.4.1
  url_launcher: ^6.3.2
  memory_cache: ^1.2.0
  location_picker_flutter_map:
    git:
      url: https://github.com/sawongam/location_picker_flutter_map
      ref: main
  flutter_carousel_widget: ^3.1.0
  sliver_tools: ^0.2.12
  rx_shared_preferences: ^4.0.0
  barcode_widget: ^2.0.4
  time_range_picker: ^2.3.1
  vector_graphics: any
  intl: any
  toastification: ^2.3.0
  flutter_widget_from_html_core: ^0.17.0
  app_links: ^6.4.1
  uuid: ^4.5.1
  flutter_sms:
    git:
      url: https://github.com/mariogorki94/flutter_sms
      ref: master
  after_layout: ^1.2.0
  expandable_page_view: ^1.0.17
  onesignal_flutter: ^5.3.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.7.0
  flutter_gen_runner: ^5.11.0
  very_good_analysis: ^9.0.0
  go_router_builder: ^4.0.0
  flutter_launcher_icons: ^0.14.4
  retrofit_generator: ^10.0.4
  json_serializable: ^6.11.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/

  fonts:
    - family: OpenSans
      fonts:
        - asset: assets/fonts/OpenSans-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/OpenSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/OpenSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/OpenSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/OpenSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/OpenSans-Light.ttf
          weight: 300

flutter_gen:
  output: lib/src/generated
  line_length: 80

  integrations:
    flutter_svg: true
