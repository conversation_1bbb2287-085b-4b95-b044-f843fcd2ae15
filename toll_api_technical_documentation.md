# Toll API Layer Technical Documentation

## Table of Contents
1. [Overview](#overview)
2. [API Configuration](#api-configuration)
3. [Endpoints](#endpoints)
4. [Data Transfer Objects (DTOs)](#data-transfer-objects-dtos)
5. [API Call Flows](#api-call-flows)
6. [Error Handling](#error-handling)
7. [Authentication](#authentication)
8. [Usage Examples](#usage-examples)

## Overview

The Toll API layer provides a RESTful interface for toll-related operations using Retrofit for HTTP client generation. It handles all communication with the toll service backend and provides type-safe API calls with comprehensive error handling.

**Key Features:**
- RESTful API design with Retrofit
- Comprehensive DTO system for type safety
- Generic response wrapper for consistent data handling
- Specialized error handling for toll-specific errors
- JSON serialization with code generation

## API Configuration

### Base Configuration
```dart
@RestApi(baseUrl: 'api/Vignette')
abstract class TollApi {
  factory TollApi(Dio dio) = _TollApi;
}
```

**Configuration Details:**
- **Base URL**: `api/Vignette`
- **HTTP Client**: Dio
- **Code Generation**: Retrofit with json_annotation
- **Serialization**: Automatic JSON serialization/deserialization

### Dependencies
```yaml
dependencies:
  dio: ^5.3.2                    # HTTP client
  retrofit: ^4.0.3               # REST API client generator
  json_annotation: ^4.8.1       # JSON serialization annotations

dev_dependencies:
  retrofit_generator: ^8.0.4     # Code generation for Retrofit
  json_serializable: ^6.7.1     # JSON serialization code generation
```

## Endpoints

### 1. Get Countries
**Retrieve available toll countries**

```dart
@GET('/countries')
Future<List<TollCountryDto>> getCountries();
```

- **Method**: GET
- **Path**: `/countries`
- **Purpose**: Fetch list of countries where toll services are available
- **Response**: Array of country objects with id, code, and name
- **Called by**: `TollRepository.getCountries()`

### 2. Get Products
**Fetch available toll products and pricing**

```dart
@GET('/products')
Future<List<TollProductDto>> getProducts();
```

- **Method**: GET
- **Path**: `/products`
- **Purpose**: Retrieve toll product catalog with pricing and validity information
- **Response**: Array of product objects with pricing details
- **Called by**: `TollRepository.getProducts()`

### 3. Check Toll Validity
**Verify existing toll validity for a vehicle**

```dart
@GET('/check-validity')
Future<TollResponseDto<TollCheckDto>> tollCheck({
  @Query('countryCode') required String countryCode,
  @Query('lpn') required String plateNumber,
});
```

- **Method**: GET
- **Path**: `/check-validity`
- **Parameters**:
  - `countryCode`: Country code (query parameter)
  - `lpn`: License plate number (query parameter)
- **Purpose**: Check if a vehicle has valid toll for specified country
- **Response**: Wrapped toll check data with validity status
- **Called by**: `TollRepository.checkToll()`

### 4. Register Toll
**Register a new toll purchase**

```dart
@POST('/register')
Future<TollResponseDto<TollRegisterDto>> registerToll({
  @Field('saleRows') required List<RegisterTollRequestDto> request,
});
```

- **Method**: POST
- **Path**: `/register`
- **Parameters**:
  - `saleRows`: Array of toll registration requests (form field)
- **Purpose**: Register toll purchase with payment details
- **Response**: Registration confirmation with transaction details
- **Called by**: `TollRepository.registerToll()`

### 5. Activate Toll
**Activate a registered toll**

```dart
@POST('/activate')
Future<TollResponseDto<TollSaleRowDto>> activateToll({
  @Query('vignetteId') required String id,
});
```

- **Method**: POST
- **Path**: `/activate`
- **Parameters**:
  - `vignetteId`: Vignette identifier (query parameter)
- **Purpose**: Activate a previously registered toll
- **Response**: Activation confirmation
- **Called by**: `TollRepository.activateToll()` (not implemented)

### 6. Get User Tolls
**Retrieve user's toll history**

```dart
@GET('/user-vignettes')
Future<List<TollDto>> getUserToll();
```

- **Method**: GET
- **Path**: `/user-vignettes`
- **Purpose**: Fetch all tolls associated with authenticated user
- **Response**: Array of user's toll records
- **Called by**: `TollRepository._getTollData()`

### 7. Add Toll
**Add toll record to user's account**

```dart
@POST('/add')
Future<TollDto> addToll({@Body() required TollDto dto});
```

- **Method**: POST
- **Path**: `/add`
- **Parameters**:
  - `dto`: Complete toll data object (request body)
- **Purpose**: Add toll record to user's account after registration
- **Response**: Created toll record
- **Called by**: `TollRepository.registerToll()`

### 8. Update Toll
**Update existing toll record**

```dart
@POST('/update')
Future<void> updateToll({@Body() required TollDto dto});
```

- **Method**: POST
- **Path**: `/update`
- **Parameters**:
  - `dto`: Updated toll data object (request body)
- **Purpose**: Update existing toll record
- **Response**: No content (void)
- **Called by**: Not currently used in repository

### 9. Update Toll Check
**Associate toll with vehicle after validity check**

```dart
@POST('/set-vehicle-vignette')
Future<TollDto> updateTollCheck({@Body() required TollDto dto});
```

- **Method**: POST
- **Path**: `/set-vehicle-vignette`
- **Parameters**:
  - `dto`: Toll data with vehicle association (request body)
- **Purpose**: Link toll record to vehicle after successful validity check
- **Response**: Updated toll record
- **Called by**: `TollRepository.checkToll()`

## Data Transfer Objects (DTOs)

### TollResponseDto<T>
**Generic wrapper for API responses**

```dart
@JsonSerializable(genericArgumentFactories: true)
final class TollResponseDto<T> {
  TollResponseDto({required this.data, required this.json});

  factory TollResponseDto.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$TollResponseDtoFromJson(json, fromJsonT);

  @JsonKey(readValue: _topLevelDecoder)
  final T data;                    // Actual response data
  @JsonKey(readValue: _topLevelDecoder)
  final Map<String, dynamic> json; // Raw JSON for debugging
}
```

**Purpose**: Provides a consistent wrapper for API responses that need additional metadata
**Used by**: `tollCheck()`, `registerToll()`, `activateToll()`

### TollDto
**Complete toll record data transfer object**

```dart
@JsonSerializable()
final class TollDto {
  TollDto({
    required this.id,
    required this.userId,
    required this.customer,
    required this.eik,
    required this.email,
    required this.phone,
    required this.date,
    required this.countryCode,
    required this.vehicleType,
    required this.plateNumber,
    required this.from,
    required this.to,
    required this.price,
    required this.state,
    required this.vignetteId,
    required this.issued,
    required this.registerJson,
    required this.activateJson,
    required this.checkJson,
  });

  @JsonKey(name: 'id')
  final int? id;
  @JsonKey(name: 'mobileUserId')
  final int userId;
  @JsonKey(name: 'customer')
  final String customer;
  @JsonKey(name: 'eik')
  final String? eik;
  @JsonKey(name: 'email')
  final String? email;
  @JsonKey(name: 'phone')
  final String? phone;
  @JsonKey(name: 'date')
  final DateTime date;
  @JsonKey(name: 'countryCode')
  final String countryCode;
  @JsonKey(name: 'vehicleType')
  final int vehicleType;
  @JsonKey(name: 'dkn')
  final String plateNumber;
  @JsonKey(name: 'from')
  final DateTime from;
  @JsonKey(name: 'to')
  final DateTime to;
  @JsonKey(name: 'price')
  final double price;
  @JsonKey(name: 'state')
  final int? state;
  @JsonKey(name: 'vignetteId')
  final String vignetteId;
  @JsonKey(name: 'issued')
  final DateTime? issued;
  @JsonKey(name: 'registerJsonResponse')
  final Map<String, dynamic>? registerJson;
  @JsonKey(name: 'activateJsonResponse')
  final Map<String, dynamic>? activateJson;
  @JsonKey(name: 'checkJsonResponse')
  final Map<String, dynamic>? checkJson;
}
```

**Purpose**: Represents a complete toll record with all associated data
**Used by**: `getUserToll()`, `addToll()`, `updateToll()`, `updateTollCheck()`

### TollCheckDto
**Toll validity check result**

```dart
@JsonSerializable()
final class TollCheckDto {
  TollCheckDto({
    required this.code,
    required this.vehicleClass,
    required this.validityStartDate,
    required this.validityEndDate,
    required this.validityStartFormatted,
    required this.validityEndFormatted,
    required this.price,
    required this.currency,
    required this.status,
    required this.statusCode,
    required this.productId,
    required this.valid,
  });

  @JsonKey(name: 'vignetteCode')
  final String? code;
  @JsonKey(name: 'vehicleClass')
  final String? vehicleClass;
  @JsonKey(name: 'validityStartDate')
  final DateTime validityStartDate;
  @JsonKey(name: 'validityEndDate')
  final DateTime validityEndDate;
  @JsonKey(name: 'validityStartFormatted')
  final String? validityStartFormatted;
  @JsonKey(name: 'validityEndFormatted')
  final String? validityEndFormatted;
  @JsonKey(name: 'price')
  final String? price;
  @JsonKey(name: 'currency')
  final String? currency;
  @JsonKey(name: 'status')
  final String? status;
  @JsonKey(name: 'statusCode')
  final int statusCode;
  @JsonKey(name: 'productId')
  final int productId;
  @JsonKey(name: 'valid')
  final bool valid;
}
```

**Purpose**: Contains toll validity check results with status and validity period
**Used by**: `tollCheck()` response

### TollCountryDto
**Country information**

```dart
@JsonSerializable()
final class TollCountryDto {
  TollCountryDto({
    required this.id,
    required this.code,
    required this.name,
  });

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'code')
  final String code;
  @JsonKey(name: 'name')
  final String name;
}
```

**Purpose**: Represents a country where toll services are available
**Used by**: `getCountries()` response

### TollProductDto
**Product and pricing information**

```dart
@JsonSerializable()
final class TollProductDto {
  TollProductDto({
    required this.id,
    required this.description,
    required this.vehicleType,
    required this.validityType,
    required this.emissionClass,
    required this.price,
  });

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'vehicleTypeName')
  final String vehicleType;
  @JsonKey(name: 'validityType')
  final String validityType;
  @JsonKey(name: 'emissionClass')
  final String emissionClass;
  @JsonKey(name: 'price')
  final double price;
}
```

**Purpose**: Represents toll product with pricing and validity information
**Used by**: `getProducts()` response

### TollRegisterDto
**Registration response data**

```dart
@JsonSerializable()
final class TollRegisterDto {
  TollRegisterDto({
    required this.id,
    required this.total,
    required this.currency,
    required this.createdOn,
    required this.active,
    required this.saleRows,
  });

  @JsonKey(name: 'id')
  final String? id;
  @JsonKey(name: 'total')
  final double? total;
  @JsonKey(name: 'currency')
  final String? currency;
  @JsonKey(name: 'createdOn')
  final DateTime? createdOn;
  @JsonKey(name: 'active')
  final bool active;
  @JsonKey(name: 'saleRows')
  final List<TollSaleRowDto> saleRows;
}
```

**Purpose**: Contains registration confirmation with transaction details
**Used by**: `registerToll()` response

### RegisterTollRequestDto
**Registration request payload**

```dart
@JsonSerializable()
final class RegisterTollRequestDto {
  RegisterTollRequestDto({
    required this.activationDate,
    required this.email,
    required this.productId,
    required this.vehicle,
  });

  @JsonKey(name: 'activationDate')
  final DateTime activationDate;
  @JsonKey(name: 'email')
  final String? email;
  @JsonKey(name: 'kapschProductId')
  final int productId;
  @JsonKey(name: 'vehicle')
  final ({String lpn, String countryCode}) vehicle;
}
```

**Purpose**: Request payload for toll registration
**Used by**: `registerToll()` request

### TollErrorDto
**Structured error information**

```dart
@JsonSerializable()
final class TollErrorDto {
  TollErrorDto({
    required this.code,
    required this.message,
    required this.status,
  });

  @JsonKey(name: 'code')
  final int code;
  @JsonKey(name: 'message')
  final String? message;
  @JsonKey(name: 'status')
  final String? status;
}
```

**Purpose**: Provides structured error information for toll-specific errors
**Used by**: Error handling in specialized toll API calls

## API Call Flows

### 1. Toll Check Flow
**Repository → API → Server**

```mermaid
sequenceDiagram
    participant Repository as TollRepository
    participant API as TollApi
    participant Server as Toll Server

    Repository->>API: tollCheck(countryCode, plateNumber)
    API->>Server: GET /check-validity?countryCode=XX&lpn=ABC123
    Server-->>API: TollResponseDto<TollCheckDto>
    API-->>Repository: Response with validity data

    alt Valid toll found
        Repository->>API: updateTollCheck(tollDto)
        API->>Server: POST /set-vehicle-vignette
        Server-->>API: TollDto (updated)
        API-->>Repository: Updated toll record
    end
```

**Flow Description:**
1. Repository calls `tollCheck()` with country code and plate number
2. API makes GET request to `/check-validity` endpoint
3. Server returns wrapped toll check data
4. If toll is valid, repository updates toll-vehicle association
5. API makes POST request to `/set-vehicle-vignette`
6. Server returns updated toll record

### 2. Toll Registration Flow
**Repository → API → Server**

```mermaid
sequenceDiagram
    participant Repository as TollRepository
    participant API as TollApi
    participant Server as Toll Server

    Repository->>API: registerToll(requestList)
    API->>Server: POST /register (saleRows)
    Server-->>API: TollResponseDto<TollRegisterDto>
    API-->>Repository: Registration confirmation

    Repository->>API: addToll(tollDto)
    API->>Server: POST /add
    Server-->>API: TollDto (created)
    API-->>Repository: Created toll record
```

**Flow Description:**
1. Repository calls `registerToll()` with registration data
2. API makes POST request to `/register` with sale rows
3. Server returns registration confirmation
4. Repository calls `addToll()` to add record to user account
5. API makes POST request to `/add` endpoint
6. Server returns created toll record

### 3. User Data Retrieval Flow
**Repository → API → Server**

```mermaid
sequenceDiagram
    participant Repository as TollRepository
    participant API as TollApi
    participant Server as Toll Server

    Repository->>API: getUserToll()
    API->>Server: GET /user-vignettes
    Server-->>API: List<TollDto>
    API-->>Repository: User's toll records

    Repository->>API: getCountries()
    API->>Server: GET /countries
    Server-->>API: List<TollCountryDto>
    API-->>Repository: Available countries

    Repository->>API: getProducts()
    API->>Server: GET /products
    Server-->>API: List<TollProductDto>
    API-->>Repository: Available products
```

**Flow Description:**
1. Repository requests user's toll data, countries, and products
2. API makes respective GET requests to server
3. Server returns data arrays
4. Repository receives and processes the data

### 4. Cache Refresh Flow
**Authentication → Repository → API → Server**

```mermaid
sequenceDiagram
    participant Auth as AuthRepository
    participant Repository as TollRepository
    participant API as TollApi
    participant Server as Toll Server

    Auth->>Repository: loggedInStream (state change)
    Repository->>Repository: Invalidate cache
    Repository->>API: getUserToll()
    API->>Server: GET /user-vignettes
    Server-->>API: List<TollDto>
    API-->>Repository: Fresh toll data
    Repository->>Repository: Update cache stream
```

**Flow Description:**
1. Authentication state changes trigger cache invalidation
2. Repository automatically refreshes toll data
3. API fetches fresh data from server
4. Repository updates internal cache stream

## Error Handling

### Error Response Structure

The API uses a specialized error handling system that wraps standard HTTP errors with toll-specific error information.

### Standard Error Handling
**Function**: `callWithResult<T>`

```dart
Future<Result<T>> callWithResult<T>(CallFunction<T> call) async {
  try {
    final result = await call();
    return Result.success(result);
  } on DioException catch (e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return Result.networkError();
      case DioExceptionType.badResponse:
        return Result.httpError(e.response?.statusCode ?? 0,
                               e.response?.statusMessage ?? '',
                               data: e.response?.data);
      case DioExceptionType.cancel:
        return Result.canceled();
      case DioExceptionType.unknown:
        return Result.otherError(e);
    }
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### Toll-Specific Error Handling
**Function**: `callWithTollResult<T>`

```dart
Future<Result<T>> callWithTollResult<T>(CallFunction<T> call) async {
  try {
    final result = await callWithResult(call);
    if (result is HttpError) {
      final error = result as HttpError;
      if (error.code == 500) {
        // Parse toll-specific error format from response data
        final jsonString = error.data.substring(
          error.data.indexOf('{'),
          error.data.lastIndexOf('}') + 1,
        );
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        final first = (json['errors'] as List?)?.firstOrNull;

        if (first != null) {
          return Result.tollError(
            TollErrorDto.fromJson(first as Map<String, dynamic>),
          );
        }
      }
    }
    return result;
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### Toll Error Codes

| Code | Description | Localization Key |
|------|-------------|------------------|
| 202, 206, 207 | Invalid start date | `error_toll_start_date` |
| 203 | Invalid email address | `error_toll_email` |
| 204 | Invalid vehicle information | `error_toll_vehicle` |
| 210 | Toll already activated | `error_toll_activated` |
| 217 | Toll already purchased | `error_toll_has_prucase` |

### Error Handling by Endpoint

| Endpoint | Error Handler | Error Types |
|----------|---------------|-------------|
| `getCountries()` | `callWithCachedResult` | Network, HTTP, Cache |
| `getProducts()` | `callWithCachedResult` | Network, HTTP, Cache |
| `tollCheck()` | `callWithResult` | Network, HTTP, Validation |
| `registerToll()` | `callWithTollResult` | Network, HTTP, Toll-specific |
| `activateToll()` | `callWithTollResult` | Network, HTTP, Toll-specific |
| `getUserToll()` | `callWithResult` | Network, HTTP, Authentication |
| `addToll()` | `callWithResult` | Network, HTTP, Validation |
| `updateToll()` | `callWithResult` | Network, HTTP, Validation |
| `updateTollCheck()` | `callWithResult` | Network, HTTP, Validation |

## Authentication

### Authentication Mechanism
The TollApi does not implement explicit authentication mechanisms. Authentication is handled at the Dio client level through interceptors.

**Authentication Flow:**
1. Dio interceptors automatically add authentication headers
2. Authentication state is managed by `AuthRepository`
3. Token refresh is handled transparently by interceptors
4. API calls automatically include required authentication

### Authentication Dependencies
```dart
// Authentication is injected at the Dio level
final dio = Dio();
// Add authentication interceptors to dio instance
final tollApi = TollApi(dio);
```

### Authenticated Endpoints
All endpoints require authentication except:
- `getCountries()` - Public endpoint
- `getProducts()` - Public endpoint

### Authentication Errors
- **401 Unauthorized**: Token expired or invalid
- **403 Forbidden**: Insufficient permissions
- **Authentication errors are handled by Dio interceptors**

## Usage Examples

### 1. Basic API Usage in Repository

```dart
class TollRepository {
  final TollApi _tollApi;

  Future<Result<List<TollCountry>>> getCountries() async {
    try {
      final result = await callWithCachedResult(_tollApi.getCountries);
      return result.listMap(TollCountry.fromDto);
    } catch (e) {
      return Result.otherError(e);
    }
  }
}
```

### 2. Toll Check with Error Handling

```dart
Future<Result<TollCheckData?>> checkToll({
  required TollCountry country,
  required String plateNumber,
}) async {
  try {
    final result = await callWithResult(
      () => _tollApi.tollCheck(
        countryCode: country.code,
        plateNumber: plateNumber,
      ),
    );

    if (result.isFailure) {
      return (result as Failure).transform();
    }

    // Process successful response
    return result.map((e) => TollCheckData.fromDto(e.data));
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### 3. Toll Registration with Multiple API Calls

```dart
Future<Result<TollData>> registerToll(TollRegisterData data) async {
  try {
    // Step 1: Register toll
    final registerResult = await callWithTollResult(
      () => _tollApi.registerToll(request: [data.toDto()]),
    );

    if (registerResult.isFailure) {
      return (registerResult as Failure).transform();
    }

    // Step 2: Add toll to user account
    final addResult = await callWithResult(
      () => _tollApi.addToll(
        dto: TollData.fromRegisterData(user, registerResult.maybeValue!).toDto(),
      ),
    );

    return addResult.map(TollData.fromDto);
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### 4. Cached Data Retrieval

```dart
Future<Result<List<TollProduct>>> getProducts() async {
  try {
    final result = await callWithCachedResult(
      _tollApi.getProducts,
      cacheKey: 'toll_products',
      ttl: Duration(hours: 1),
    );
    return result.listMap(TollProduct.fromDto);
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### 5. Stream-based Data Updates

```dart
// Repository automatically updates stream when API data changes
Stream<Result<List<TollData>>> get toll => _tollStream;

// API calls trigger stream updates
Future<void> refresh() async {
  _cachedResponse.add(null); // Invalidate cache
  // Stream automatically refreshes via _getTollData()
}
```

## Summary

The Toll API layer provides a comprehensive, type-safe interface for toll operations with:

- **9 RESTful endpoints** covering all toll operations
- **8 specialized DTOs** for type-safe data transfer
- **Generic response wrapper** for consistent API responses
- **Specialized error handling** for toll-specific errors
- **Automatic authentication** via Dio interceptors
- **Caching support** for performance optimization
- **Repository integration** for business logic separation

The API is designed to be consumed exclusively by the `TollRepository`, which handles caching, error processing, and data transformation to domain models.