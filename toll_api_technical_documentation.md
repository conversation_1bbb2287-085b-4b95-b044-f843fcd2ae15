# Toll API Layer Technical Documentation

## Table of Contents
1. [Overview](#overview)
2. [API Configuration](#api-configuration)
3. [Endpoints](#endpoints)
4. [Data Transfer Objects (DTOs)](#data-transfer-objects-dtos)
5. [API Call Flows](#api-call-flows)
6. [Error Handling](#error-handling)
7. [Authentication](#authentication)
8. [Usage Examples](#usage-examples)
9. [Proposed Refactoring](#proposed-refactoring)

## Overview

The Toll API layer provides a RESTful interface for toll-related operations using Retrofit for HTTP client generation. It handles all communication with the toll service backend and provides type-safe API calls with comprehensive error handling.

**Key Features:**
- RESTful API design with Retrofit
- Comprehensive DTO system for type safety
- Generic response wrapper for consistent data handling
- Specialized error handling for toll-specific errors
- JSON serialization with code generation

## API Configuration

### Base Configuration
```dart
@RestApi(baseUrl: 'api/Vignette')
abstract class TollApi {
  factory TollApi(Dio dio) = _TollApi;
}
```

**Configuration Details:**
- **Base URL**: `api/Vignette`
- **HTTP Client**: Dio
- **Code Generation**: Retrofit with json_annotation
- **Serialization**: Automatic JSON serialization/deserialization

## Endpoints

### API Endpoints Summary

| Endpoint | Method | Path | Purpose | Request | Response | Auth Required | Error Handler |
|----------|--------|------|---------|---------|----------|---------------|---------------|
| `getCountries()` | GET | `/countries` | Get available countries | None | `List<TollCountryDto>` | No | `callWithCachedResult` |
| `getProducts()` | GET | `/products` | Get toll products | None | `List<TollProductDto>` | No | `callWithCachedResult` |
| `tollCheck()` | GET | `/check-validity` | Check toll validity | Query params | `TollResponseDto<TollCheckDto>` | Yes | `callWithResult` |
| `registerToll()` | POST | `/register` | Register toll purchase | Form field | `TollResponseDto<TollRegisterDto>` | Yes | `callWithTollResult` |
| `activateToll()` | POST | `/activate` | Activate registered toll | Query param | `TollResponseDto<TollSaleRowDto>` | Yes | `callWithTollResult` |
| `getUserToll()` | GET | `/user-vignettes` | Get user's tolls | None | `List<TollDto>` | Yes | `callWithResult` |
| `addToll()` | POST | `/add` | Add toll to account | Request body | `TollDto` | Yes | `callWithResult` |
| `updateToll()` | POST | `/update` | Update toll record | Request body | `void` | Yes | `callWithResult` |
| `updateTollCheck()` | POST | `/set-vehicle-vignette` | Link toll to vehicle | Request body | `TollDto` | Yes | `callWithResult` |

## Data Transfer Objects (DTOs)

### DTO Summary

| DTO | Purpose | Key Fields | Used By | Direction |
|-----|---------|------------|---------|-----------|
| `TollResponseDto<T>` | Generic API response wrapper | `data`, `json` | Wrapped responses | Response |
| `TollDto` | Complete toll record | `id`, `userId`, `plateNumber`, `from`, `to`, `price` | User toll operations | Both |
| `TollCheckDto` | Toll validity check result | `valid`, `validityStartDate`, `validityEndDate`, `statusCode` | Toll validation | Response |
| `TollCountryDto` | Country information | `id`, `code`, `name` | Country selection | Response |
| `TollProductDto` | Product and pricing | `id`, `description`, `vehicleType`, `price` | Product catalog | Response |
| `TollRegisterDto` | Registration confirmation | `id`, `total`, `currency`, `saleRows` | Registration process | Response |
| `RegisterTollRequestDto` | Registration request | `activationDate`, `email`, `productId`, `vehicle` | Registration process | Request |
| `TollErrorDto` | Structured error info | `code`, `message`, `status` | Error handling | Response |

### Detailed DTO Documentation

### TollResponseDto<T>
**Generic wrapper for API responses**

```dart
@JsonSerializable(genericArgumentFactories: true)
final class TollResponseDto<T> {
  TollResponseDto({required this.data, required this.json});

  factory TollResponseDto.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$TollResponseDtoFromJson(json, fromJsonT);

  @JsonKey(readValue: _topLevelDecoder)
  final T data;                    // Actual response data
  @JsonKey(readValue: _topLevelDecoder)
  final Map<String, dynamic> json; // Raw JSON for debugging
}
```

**Purpose**: Provides a consistent wrapper for API responses that need additional metadata
**Used by**: `tollCheck()`, `registerToll()`, `activateToll()`

### TollDto
**Complete toll record data transfer object**

```dart
@JsonSerializable()
final class TollDto {
  TollDto({
    required this.id,
    required this.userId,
    required this.customer,
    required this.eik,
    required this.email,
    required this.phone,
    required this.date,
    required this.countryCode,
    required this.vehicleType,
    required this.plateNumber,
    required this.from,
    required this.to,
    required this.price,
    required this.state,
    required this.vignetteId,
    required this.issued,
    required this.registerJson,
    required this.activateJson,
    required this.checkJson,
  });

  @JsonKey(name: 'id')
  final int? id;
  @JsonKey(name: 'mobileUserId')
  final int userId;
  @JsonKey(name: 'customer')
  final String customer;
  @JsonKey(name: 'eik')
  final String? eik;
  @JsonKey(name: 'email')
  final String? email;
  @JsonKey(name: 'phone')
  final String? phone;
  @JsonKey(name: 'date')
  final DateTime date;
  @JsonKey(name: 'countryCode')
  final String countryCode;
  @JsonKey(name: 'vehicleType')
  final int vehicleType;
  @JsonKey(name: 'dkn')
  final String plateNumber;
  @JsonKey(name: 'from')
  final DateTime from;
  @JsonKey(name: 'to')
  final DateTime to;
  @JsonKey(name: 'price')
  final double price;
  @JsonKey(name: 'state')
  final int? state;
  @JsonKey(name: 'vignetteId')
  final String vignetteId;
  @JsonKey(name: 'issued')
  final DateTime? issued;
  @JsonKey(name: 'registerJsonResponse')
  final Map<String, dynamic>? registerJson;
  @JsonKey(name: 'activateJsonResponse')
  final Map<String, dynamic>? activateJson;
  @JsonKey(name: 'checkJsonResponse')
  final Map<String, dynamic>? checkJson;
}
```

**Purpose**: Represents a complete toll record with all associated data
**Used by**: `getUserToll()`, `addToll()`, `updateToll()`, `updateTollCheck()`

### TollCheckDto
**Toll validity check result**

```dart
@JsonSerializable()
final class TollCheckDto {
  TollCheckDto({
    required this.code,
    required this.vehicleClass,
    required this.validityStartDate,
    required this.validityEndDate,
    required this.validityStartFormatted,
    required this.validityEndFormatted,
    required this.price,
    required this.currency,
    required this.status,
    required this.statusCode,
    required this.productId,
    required this.valid,
  });

  @JsonKey(name: 'vignetteCode')
  final String? code;
  @JsonKey(name: 'vehicleClass')
  final String? vehicleClass;
  @JsonKey(name: 'validityStartDate')
  final DateTime validityStartDate;
  @JsonKey(name: 'validityEndDate')
  final DateTime validityEndDate;
  @JsonKey(name: 'validityStartFormatted')
  final String? validityStartFormatted;
  @JsonKey(name: 'validityEndFormatted')
  final String? validityEndFormatted;
  @JsonKey(name: 'price')
  final String? price;
  @JsonKey(name: 'currency')
  final String? currency;
  @JsonKey(name: 'status')
  final String? status;
  @JsonKey(name: 'statusCode')
  final int statusCode;
  @JsonKey(name: 'productId')
  final int productId;
  @JsonKey(name: 'valid')
  final bool valid;
}
```

**Purpose**: Contains toll validity check results with status and validity period
**Used by**: `tollCheck()` response

### TollCountryDto
**Country information**

```dart
@JsonSerializable()
final class TollCountryDto {
  TollCountryDto({
    required this.id,
    required this.code,
    required this.name,
  });

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'code')
  final String code;
  @JsonKey(name: 'name')
  final String name;
}
```

**Purpose**: Represents a country where toll services are available
**Used by**: `getCountries()` response

### TollProductDto
**Product and pricing information**

```dart
@JsonSerializable()
final class TollProductDto {
  TollProductDto({
    required this.id,
    required this.description,
    required this.vehicleType,
    required this.validityType,
    required this.emissionClass,
    required this.price,
  });

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'vehicleTypeName')
  final String vehicleType;
  @JsonKey(name: 'validityType')
  final String validityType;
  @JsonKey(name: 'emissionClass')
  final String emissionClass;
  @JsonKey(name: 'price')
  final double price;
}
```

**Purpose**: Represents toll product with pricing and validity information
**Used by**: `getProducts()` response

### TollRegisterDto
**Registration response data**

```dart
@JsonSerializable()
final class TollRegisterDto {
  TollRegisterDto({
    required this.id,
    required this.total,
    required this.currency,
    required this.createdOn,
    required this.active,
    required this.saleRows,
  });

  @JsonKey(name: 'id')
  final String? id;
  @JsonKey(name: 'total')
  final double? total;
  @JsonKey(name: 'currency')
  final String? currency;
  @JsonKey(name: 'createdOn')
  final DateTime? createdOn;
  @JsonKey(name: 'active')
  final bool active;
  @JsonKey(name: 'saleRows')
  final List<TollSaleRowDto> saleRows;
}
```

**Purpose**: Contains registration confirmation with transaction details
**Used by**: `registerToll()` response

### RegisterTollRequestDto
**Registration request payload**

```dart
@JsonSerializable()
final class RegisterTollRequestDto {
  RegisterTollRequestDto({
    required this.activationDate,
    required this.email,
    required this.productId,
    required this.vehicle,
  });

  @JsonKey(name: 'activationDate')
  final DateTime activationDate;
  @JsonKey(name: 'email')
  final String? email;
  @JsonKey(name: 'kapschProductId')
  final int productId;
  @JsonKey(name: 'vehicle')
  final ({String lpn, String countryCode}) vehicle;
}
```

**Purpose**: Request payload for toll registration
**Used by**: `registerToll()` request

### TollErrorDto
**Structured error information**

```dart
@JsonSerializable()
final class TollErrorDto {
  TollErrorDto({
    required this.code,
    required this.message,
    required this.status,
  });

  @JsonKey(name: 'code')
  final int code;
  @JsonKey(name: 'message')
  final String? message;
  @JsonKey(name: 'status')
  final String? status;
}
```

**Purpose**: Provides structured error information for toll-specific errors
**Used by**: Error handling in specialized toll API calls

## API Call Flows

### 1. Toll Check Flow
**Repository → API → Server**

<details>
<summary><strong>🔍 Click to expand: Toll Validity Check Process</strong></summary>

#### Phase 1: Initial Toll Check
```
🏛️ Repository
    ↓ calls tollCheck(countryCode, plateNumber)
🌐 TollApi
    ↓ GET /check-validity?countryCode=XX&lpn=ABC123
🖥️ Toll Server
    ↓ validates toll for vehicle
    ↓ returns TollResponseDto<TollCheckDto>
🌐 TollApi
    ↓ passes validity data
🏛️ Repository
```

**What happens here:**
- Repository receives country code and license plate number
- API constructs GET request with query parameters
- Server checks if vehicle has valid toll for the specified country
- Response includes validity status, dates, and toll details

#### Phase 2: Conditional Vehicle Association
```
🏛️ Repository (if toll is valid)
    ↓ calls updateTollCheck(tollDto)
🌐 TollApi
    ↓ POST /set-vehicle-vignette
🖥️ Toll Server
    ↓ associates toll with vehicle
    ↓ returns TollDto (updated)
🌐 TollApi
    ↓ passes updated record
🏛️ Repository
```

**What happens here:**
- Only executes if toll validity check returns `valid: true`
- Creates association between toll record and vehicle
- Updates toll record with vehicle information
- Returns complete toll record with vehicle association

#### Error Scenarios
- **Network Error**: Connection timeout, no internet
- **Validation Error**: Invalid country code or plate number format
- **Not Found**: No toll found for the specified vehicle
- **Server Error**: Internal server processing error

</details>

**🎯 Flow Summary:**
- **Trigger**: `TollRepository.checkToll()` method call
- **Primary Purpose**: Verify if vehicle has valid toll in specified country
- **Secondary Action**: Associate valid toll with vehicle if found
- **Error Strategy**: Standard error handling via `callWithResult`

### 2. Toll Registration Flow
**Repository → API → Server**

<details>
<summary><strong>💳 Click to expand: Toll Purchase & Registration Process</strong></summary>

#### Phase 1: Toll Registration
```
🏛️ Repository
    ↓ calls registerToll(requestList)
    ↓ [RegisterTollRequestDto with payment details]
🌐 TollApi
    ↓ POST /register
    ↓ Form field: saleRows=[registration data]
🖥️ Toll Server
    ↓ processes payment
    ↓ creates toll registration
    ↓ returns TollResponseDto<TollRegisterDto>
🌐 TollApi
    ↓ passes registration confirmation
🏛️ Repository
```

**What happens here:**
- Repository sends registration request with payment details
- Server processes payment and creates toll registration
- Registration includes transaction ID, total amount, and sale rows
- Response confirms successful payment and registration

#### Phase 2: Account Addition
```
🏛️ Repository
    ↓ calls addToll(tollDto)
    ↓ [Complete TollDto with user info]
🌐 TollApi
    ↓ POST /add
    ↓ Request body: TollDto
🖥️ Toll Server
    ↓ adds toll to user's account
    ↓ returns TollDto (created)
🌐 TollApi
    ↓ passes created record
🏛️ Repository
```

**What happens here:**
- Repository creates complete toll record from registration data
- Server adds toll record to user's personal account
- Record includes all toll details, validity dates, and user association
- Response provides complete toll record with assigned ID

#### Error Scenarios
- **Payment Error**: Credit card declined, insufficient funds
- **Toll Error 202-207**: Invalid start date or activation date
- **Toll Error 203**: Invalid email address format
- **Toll Error 204**: Invalid vehicle information
- **Toll Error 217**: Toll already purchased for this vehicle

</details>

**🎯 Flow Summary:**
- **Trigger**: `TollRepository.registerToll()` method call
- **Two-Phase Process**: Payment registration → Account record creation
- **Payment Integration**: Handles payment processing and confirmation
- **Error Strategy**: Toll-specific error handling via `callWithTollResult`

### 3. User Data Retrieval Flow
**Repository → API → Server**

<details>
<summary><strong>📊 Click to expand: Batch Data Loading Process</strong></summary>

#### Stream 1: User Toll History
```
🏛️ Repository
    ↓ calls getUserToll()
🌐 TollApi
    ↓ GET /user-vignettes
    ↓ [Requires Authentication]
🖥️ Toll Server
    ↓ fetches user's toll records
    ↓ returns List<TollDto>
🌐 TollApi
    ↓ passes toll records
🏛️ Repository
    ↓ transforms to domain models
    ↓ updates cache stream
```

**What happens here:**
- Retrieves all toll records associated with authenticated user
- Includes active, expired, and pending tolls
- Data used for user's toll history and validity checks
- **Authentication Required**: Uses user's auth token

#### Stream 2: Available Countries
```
🏛️ Repository
    ↓ calls getCountries()
🌐 TollApi
    ↓ GET /countries
    ↓ [Public Endpoint - No Auth]
🖥️ Toll Server
    ↓ fetches available countries
    ↓ returns List<TollCountryDto>
🌐 TollApi
    ↓ passes country list
🏛️ Repository
    ↓ caches with TTL
```

**What happens here:**
- Retrieves list of countries where toll services are available
- Data used for country selection dropdowns
- **Cached**: Uses `callWithCachedResult` for performance
- **Public**: No authentication required

#### Stream 3: Product Catalog
```
🏛️ Repository
    ↓ calls getProducts()
🌐 TollApi
    ↓ GET /products
    ↓ [Public Endpoint - No Auth]
🖥️ Toll Server
    ↓ fetches toll products & pricing
    ↓ returns List<TollProductDto>
🌐 TollApi
    ↓ passes product catalog
🏛️ Repository
    ↓ caches with TTL
```

**What happens here:**
- Retrieves toll products with pricing and validity information
- Data used for product selection during toll purchase
- **Cached**: Uses `callWithCachedResult` for performance
- **Public**: No authentication required

#### Parallel Execution
These three streams can execute **concurrently** for optimal performance:
- User tolls load independently of reference data
- Countries and products can be cached separately
- Repository coordinates all three data sources

</details>

**🎯 Flow Summary:**
- **Trigger**: Repository initialization and cache refresh
- **Parallel Loading**: Three independent data streams
- **Mixed Authentication**: User data requires auth, reference data is public
- **Caching Strategy**: Reference data cached, user data always fresh

## Error Handling

### Error Response Structure

The API uses a specialized error handling system that wraps standard HTTP errors with toll-specific error information.

### Standard Error Handling
**Function**: `callWithResult<T>`

```dart
Future<Result<T>> callWithResult<T>(CallFunction<T> call) async {
  try {
    final result = await call();
    return Result.success(result);
  } on DioException catch (e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return Result.networkError();
      case DioExceptionType.badResponse:
        return Result.httpError(e.response?.statusCode ?? 0,
                               e.response?.statusMessage ?? '',
                               data: e.response?.data);
      case DioExceptionType.cancel:
        return Result.canceled();
      case DioExceptionType.unknown:
        return Result.otherError(e);
    }
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### Toll-Specific Error Handling
**Function**: `callWithTollResult<T>`

```dart
Future<Result<T>> callWithTollResult<T>(CallFunction<T> call) async {
  try {
    final result = await callWithResult(call);
    if (result is HttpError) {
      final error = result as HttpError;
      if (error.code == 500) {
        // Parse toll-specific error format from response data
        final jsonString = error.data.substring(
          error.data.indexOf('{'),
          error.data.lastIndexOf('}') + 1,
        );
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        final first = (json['errors'] as List?)?.firstOrNull;

        if (first != null) {
          return Result.tollError(
            TollErrorDto.fromJson(first as Map<String, dynamic>),
          );
        }
      }
    }
    return result;
  } catch (e) {
    return Result.otherError(e);
  }
}

## Proposed Refactoring

### Overview
The current API implementation has complex multi-step flows that require client-side orchestration. This section proposes a simplified server-centric approach that reduces API calls and moves business logic to the server.

### Current vs Proposed Architecture

#### **Current Implementation Issues:**
- **Multi-step client orchestration**: Repository manages complex workflows
- **Redundant API calls**: Multiple calls for single business operations
- **Client-side business logic**: Repository decides when to associate tolls with vehicles
- **Complex error handling**: Different error strategies for related operations
- **Unnecessary DTOs**: Intermediate response objects that add complexity

#### **Proposed Improvements:**
- **Single API calls**: One call per business operation
- **Server-side business logic**: Server handles all workflow orchestration
- **Simplified responses**: Direct `TollDto` responses without wrappers
- **Consistent error handling**: Unified error strategy per operation type
- **Reduced complexity**: Fewer DTOs and endpoints to maintain

### Detailed Refactoring Plan

<details>
<summary><strong>🔄 Click to expand: Toll Check Flow Refactoring</strong></summary>

#### Current Implementation
```
🏛️ Repository
    ↓ calls tollCheck(countryCode, plateNumber)
🌐 TollApi
    ↓ GET /check-validity → TollResponseDto<TollCheckDto>
🏛️ Repository
    ↓ evaluates response.data.valid
    ↓ if valid: calls updateTollCheck(tollDto)
🌐 TollApi
    ↓ POST /set-vehicle-vignette → TollDto
```

#### Proposed Implementation
```
🏛️ Repository
    ↓ calls tollCheck(countryCode, plateNumber)
🌐 TollApi
    ↓ GET /check-validity → TollDto
🖥️ Server
    ↓ validates toll for vehicle
    ↓ automatically associates toll with vehicle if valid
    ↓ returns complete TollDto with association
```

**Benefits:**
- **50% fewer API calls**: From 2 calls to 1 call
- **Server-side logic**: Server decides association automatically
- **Simpler response**: Direct `TollDto` instead of wrapped response
- **Atomic operation**: Check and association happen together

</details>

<details>
<summary><strong>💳 Click to expand: Registration Flow Refactoring</strong></summary>

#### Current Implementation
```
🏛️ Repository
    ↓ calls registerToll(requestList)
🌐 TollApi
    ↓ POST /register → TollResponseDto<TollRegisterDto>
🏛️ Repository
    ↓ creates TollDto from registration data
    ↓ calls addToll(tollDto)
🌐 TollApi
    ↓ POST /add → TollDto
🏛️ Repository
    ↓ may call activateToll(vignetteId)
🌐 TollApi
    ↓ POST /activate → TollResponseDto<TollSaleRowDto>
```

#### Proposed Implementation
```
🏛️ Repository
    ↓ calls registerToll(requestList)
🌐 TollApi
    ↓ POST /register → TollDto
🖥️ Server
    ↓ processes payment
    ↓ creates toll registration
    ↓ adds toll to user account
    ↓ activates toll automatically
    ↓ returns complete TollDto
```

**Benefits:**
- **67% fewer API calls**: From 3 calls to 1 call
- **Atomic transaction**: Payment, registration, and activation together
- **Server-side orchestration**: Server manages entire workflow
- **Simplified error handling**: Single point of failure instead of multiple

</details>

### API Changes Summary

#### **Endpoints to Modify:**
| Current Endpoint | Current Response | Proposed Response | Change |
|------------------|------------------|-------------------|---------|
| `tollCheck()` | `TollResponseDto<TollCheckDto>` | `TollDto` | Simplified response |
| `registerToll()` | `TollResponseDto<TollRegisterDto>` | `TollDto` | Simplified response |

#### **Endpoints to Remove:**
| Endpoint | Reason | Replacement |
|----------|--------|-------------|
| `updateTollCheck()` | Server handles association | Logic moved to `tollCheck()` |
| `addToll()` | Server handles account addition | Logic moved to `registerToll()` |
| `activateToll()` | Server handles activation | Logic moved to `registerToll()` |
| `updateToll()` | Not needed in simplified flow | Removed entirely |

#### **Endpoints to Keep Unchanged:**
| Endpoint | Response | Reason |
|----------|----------|---------|
| `getCountries()` | `List<TollCountryDto>` | Reference data - no change needed |
| `getProducts()` | `List<TollProductDto>` | Reference data - no change needed |
| `getUserToll()` | `List<TollDto>` | User data retrieval - no change needed |

### DTO Changes

#### **DTOs to Remove:**
- `TollResponseDto<T>` - No longer needed with simplified responses
- `TollCheckDto` - Replaced by direct `TollDto` response
- `TollRegisterDto` - Replaced by direct `TollDto` response

#### **DTOs to Keep:**
- `TollDto` - Primary data transfer object
- `RegisterTollRequestDto` - Still needed for registration input
- `TollCountryDto` - Reference data
- `TollProductDto` - Reference data
- `TollErrorDto` - Error handling

### Error Handling Changes

#### **Simplified Error Strategy:**
| Endpoint | Current Handler | Proposed Handler | Reason |
|----------|----------------|------------------|---------|
| `tollCheck()` | `callWithResult` | `callWithResult` | No change needed |
| `registerToll()` | `callWithTollResult` | `callWithTollResult` | Keep toll-specific errors |

#### **Removed Error Handlers:**
- Error handling for `updateTollCheck()`, `addToll()`, `activateToll()`, `updateToll()`

### Implementation Benefits

#### **Developer Experience:**
- **Simpler Repository**: Fewer methods and less orchestration logic
- **Reduced Complexity**: Fewer DTOs and endpoints to understand
- **Better Testability**: Single API calls easier to test and mock
- **Clearer Intent**: Each API call has single, clear purpose

#### **Performance:**
- **Fewer Network Calls**: Reduced latency and bandwidth usage
- **Atomic Operations**: Better consistency and reliability
- **Server Optimization**: Server can optimize workflows internally

#### **Maintenance:**
- **Less Code**: Fewer endpoints and DTOs to maintain
- **Simpler Documentation**: Clearer API contracts
- **Reduced Bugs**: Fewer integration points mean fewer failure modes

### Migration Strategy

1. **Phase 1**: Implement new server-side logic while keeping old endpoints
2. **Phase 2**: Update client to use new simplified endpoints
3. **Phase 3**: Remove deprecated endpoints and DTOs
4. **Phase 4**: Update documentation and examples

## Summary

The Toll API layer provides a comprehensive, type-safe interface for toll operations with:

**Current Implementation:**
- **9 RESTful endpoints** covering all toll operations
- **8 specialized DTOs** for type-safe data transfer
- **Generic response wrapper** for consistent API responses
- **Specialized error handling** for toll-specific errors
- **Automatic authentication** via Dio interceptors
- **Caching support** for performance optimization
- **Repository integration** for business logic separation

**After Proposed Refactoring:**
- **5 RESTful endpoints** (simplified) covering all toll operations
- **5 specialized DTOs** for type-safe data transfer
- **Direct responses** without unnecessary wrappers
- **Specialized error handling** for toll-specific errors
- **Automatic authentication** via Dio interceptors
- **Caching support** for performance optimization
- **Repository integration** with simplified business logic

The API is designed to be consumed exclusively by the `TollRepository`, which handles caching, error processing, and data transformation to domain models.
