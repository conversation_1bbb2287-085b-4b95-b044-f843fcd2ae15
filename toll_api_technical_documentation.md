# Toll API Layer Technical Documentation

## Table of Contents
1. [Overview](#overview)
2. [API Configuration](#api-configuration)
3. [Endpoints](#endpoints)
4. [Data Transfer Objects (DTOs)](#data-transfer-objects-dtos)
5. [API Call Flows](#api-call-flows)
6. [Error Handling](#error-handling)
7. [Authentication](#authentication)
8. [Usage Examples](#usage-examples)

## Overview

The Toll API layer provides a RESTful interface for toll-related operations using Retrofit for HTTP client generation. It handles all communication with the toll service backend and provides type-safe API calls with comprehensive error handling.

**Key Features:**
- RESTful API design with Retrofit
- Comprehensive DTO system for type safety
- Generic response wrapper for consistent data handling
- Specialized error handling for toll-specific errors
- JSON serialization with code generation

## API Configuration

### Base Configuration
```dart
@RestApi(baseUrl: 'api/Vignette')
abstract class TollApi {
  factory TollApi(Dio dio) = _TollApi;
}
```

**Configuration Details:**
- **Base URL**: `api/Vignette`
- **HTTP Client**: Dio
- **Code Generation**: Retrofit with json_annotation
- **Serialization**: Automatic JSON serialization/deserialization

### Dependencies
```yaml
dependencies:
  dio: ^5.3.2                    # HTTP client
  retrofit: ^4.0.3               # REST API client generator
  json_annotation: ^4.8.1       # JSON serialization annotations

dev_dependencies:
  retrofit_generator: ^8.0.4     # Code generation for Retrofit
  json_serializable: ^6.7.1     # JSON serialization code generation
```

## Endpoints

### API Endpoints Summary

| Endpoint | Method | Path | Purpose | Request | Response | Auth Required | Error Handler |
|----------|--------|------|---------|---------|----------|---------------|---------------|
| `getCountries()` | GET | `/countries` | Get available countries | None | `List<TollCountryDto>` | No | `callWithCachedResult` |
| `getProducts()` | GET | `/products` | Get toll products | None | `List<TollProductDto>` | No | `callWithCachedResult` |
| `tollCheck()` | GET | `/check-validity` | Check toll validity | Query params | `TollResponseDto<TollCheckDto>` | Yes | `callWithResult` |
| `registerToll()` | POST | `/register` | Register toll purchase | Form field | `TollResponseDto<TollRegisterDto>` | Yes | `callWithTollResult` |
| `activateToll()` | POST | `/activate` | Activate registered toll | Query param | `TollResponseDto<TollSaleRowDto>` | Yes | `callWithTollResult` |
| `getUserToll()` | GET | `/user-vignettes` | Get user's tolls | None | `List<TollDto>` | Yes | `callWithResult` |
| `addToll()` | POST | `/add` | Add toll to account | Request body | `TollDto` | Yes | `callWithResult` |
| `updateToll()` | POST | `/update` | Update toll record | Request body | `void` | Yes | `callWithResult` |
| `updateTollCheck()` | POST | `/set-vehicle-vignette` | Link toll to vehicle | Request body | `TollDto` | Yes | `callWithResult` |

### Detailed Endpoint Documentation

### 1. Get Countries
**Retrieve available toll countries**

```dart
@GET('/countries')
Future<List<TollCountryDto>> getCountries();
```

- **Method**: GET
- **Path**: `/countries`
- **Purpose**: Fetch list of countries where toll services are available
- **Response**: Array of country objects with id, code, and name
- **Called by**: `TollRepository.getCountries()`

### 2. Get Products
**Fetch available toll products and pricing**

```dart
@GET('/products')
Future<List<TollProductDto>> getProducts();
```

- **Method**: GET
- **Path**: `/products`
- **Purpose**: Retrieve toll product catalog with pricing and validity information
- **Response**: Array of product objects with pricing details
- **Called by**: `TollRepository.getProducts()`

### 3. Check Toll Validity
**Verify existing toll validity for a vehicle**

```dart
@GET('/check-validity')
Future<TollResponseDto<TollCheckDto>> tollCheck({
  @Query('countryCode') required String countryCode,
  @Query('lpn') required String plateNumber,
});
```

- **Method**: GET
- **Path**: `/check-validity`
- **Parameters**:
  - `countryCode`: Country code (query parameter)
  - `lpn`: License plate number (query parameter)
- **Purpose**: Check if a vehicle has valid toll for specified country
- **Response**: Wrapped toll check data with validity status
- **Called by**: `TollRepository.checkToll()`

### 4. Register Toll
**Register a new toll purchase**

```dart
@POST('/register')
Future<TollResponseDto<TollRegisterDto>> registerToll({
  @Field('saleRows') required List<RegisterTollRequestDto> request,
});
```

- **Method**: POST
- **Path**: `/register`
- **Parameters**:
  - `saleRows`: Array of toll registration requests (form field)
- **Purpose**: Register toll purchase with payment details
- **Response**: Registration confirmation with transaction details
- **Called by**: `TollRepository.registerToll()`

### 5. Activate Toll
**Activate a registered toll**

```dart
@POST('/activate')
Future<TollResponseDto<TollSaleRowDto>> activateToll({
  @Query('vignetteId') required String id,
});
```

- **Method**: POST
- **Path**: `/activate`
- **Parameters**:
  - `vignetteId`: Vignette identifier (query parameter)
- **Purpose**: Activate a previously registered toll
- **Response**: Activation confirmation
- **Called by**: `TollRepository.activateToll()` (not implemented)

### 6. Get User Tolls
**Retrieve user's toll history**

```dart
@GET('/user-vignettes')
Future<List<TollDto>> getUserToll();
```

- **Method**: GET
- **Path**: `/user-vignettes`
- **Purpose**: Fetch all tolls associated with authenticated user
- **Response**: Array of user's toll records
- **Called by**: `TollRepository._getTollData()`

### 7. Add Toll
**Add toll record to user's account**

```dart
@POST('/add')
Future<TollDto> addToll({@Body() required TollDto dto});
```

- **Method**: POST
- **Path**: `/add`
- **Parameters**:
  - `dto`: Complete toll data object (request body)
- **Purpose**: Add toll record to user's account after registration
- **Response**: Created toll record
- **Called by**: `TollRepository.registerToll()`

### 8. Update Toll
**Update existing toll record**

```dart
@POST('/update')
Future<void> updateToll({@Body() required TollDto dto});
```

- **Method**: POST
- **Path**: `/update`
- **Parameters**:
  - `dto`: Updated toll data object (request body)
- **Purpose**: Update existing toll record
- **Response**: No content (void)
- **Called by**: Not currently used in repository

### 9. Update Toll Check
**Associate toll with vehicle after validity check**

```dart
@POST('/set-vehicle-vignette')
Future<TollDto> updateTollCheck({@Body() required TollDto dto});
```

- **Method**: POST
- **Path**: `/set-vehicle-vignette`
- **Parameters**:
  - `dto`: Toll data with vehicle association (request body)
- **Purpose**: Link toll record to vehicle after successful validity check
- **Response**: Updated toll record
- **Called by**: `TollRepository.checkToll()`

## Data Transfer Objects (DTOs)

### TollResponseDto<T>
**Generic wrapper for API responses**

```dart
@JsonSerializable(genericArgumentFactories: true)
final class TollResponseDto<T> {
  TollResponseDto({required this.data, required this.json});

  factory TollResponseDto.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$TollResponseDtoFromJson(json, fromJsonT);

  @JsonKey(readValue: _topLevelDecoder)
  final T data;                    // Actual response data
  @JsonKey(readValue: _topLevelDecoder)
  final Map<String, dynamic> json; // Raw JSON for debugging
}
```

**Purpose**: Provides a consistent wrapper for API responses that need additional metadata
**Used by**: `tollCheck()`, `registerToll()`, `activateToll()`

### TollDto
**Complete toll record data transfer object**

```dart
@JsonSerializable()
final class TollDto {
  TollDto({
    required this.id,
    required this.userId,
    required this.customer,
    required this.eik,
    required this.email,
    required this.phone,
    required this.date,
    required this.countryCode,
    required this.vehicleType,
    required this.plateNumber,
    required this.from,
    required this.to,
    required this.price,
    required this.state,
    required this.vignetteId,
    required this.issued,
    required this.registerJson,
    required this.activateJson,
    required this.checkJson,
  });

  @JsonKey(name: 'id')
  final int? id;
  @JsonKey(name: 'mobileUserId')
  final int userId;
  @JsonKey(name: 'customer')
  final String customer;
  @JsonKey(name: 'eik')
  final String? eik;
  @JsonKey(name: 'email')
  final String? email;
  @JsonKey(name: 'phone')
  final String? phone;
  @JsonKey(name: 'date')
  final DateTime date;
  @JsonKey(name: 'countryCode')
  final String countryCode;
  @JsonKey(name: 'vehicleType')
  final int vehicleType;
  @JsonKey(name: 'dkn')
  final String plateNumber;
  @JsonKey(name: 'from')
  final DateTime from;
  @JsonKey(name: 'to')
  final DateTime to;
  @JsonKey(name: 'price')
  final double price;
  @JsonKey(name: 'state')
  final int? state;
  @JsonKey(name: 'vignetteId')
  final String vignetteId;
  @JsonKey(name: 'issued')
  final DateTime? issued;
  @JsonKey(name: 'registerJsonResponse')
  final Map<String, dynamic>? registerJson;
  @JsonKey(name: 'activateJsonResponse')
  final Map<String, dynamic>? activateJson;
  @JsonKey(name: 'checkJsonResponse')
  final Map<String, dynamic>? checkJson;
}
```

**Purpose**: Represents a complete toll record with all associated data
**Used by**: `getUserToll()`, `addToll()`, `updateToll()`, `updateTollCheck()`

### TollCheckDto
**Toll validity check result**

```dart
@JsonSerializable()
final class TollCheckDto {
  TollCheckDto({
    required this.code,
    required this.vehicleClass,
    required this.validityStartDate,
    required this.validityEndDate,
    required this.validityStartFormatted,
    required this.validityEndFormatted,
    required this.price,
    required this.currency,
    required this.status,
    required this.statusCode,
    required this.productId,
    required this.valid,
  });

  @JsonKey(name: 'vignetteCode')
  final String? code;
  @JsonKey(name: 'vehicleClass')
  final String? vehicleClass;
  @JsonKey(name: 'validityStartDate')
  final DateTime validityStartDate;
  @JsonKey(name: 'validityEndDate')
  final DateTime validityEndDate;
  @JsonKey(name: 'validityStartFormatted')
  final String? validityStartFormatted;
  @JsonKey(name: 'validityEndFormatted')
  final String? validityEndFormatted;
  @JsonKey(name: 'price')
  final String? price;
  @JsonKey(name: 'currency')
  final String? currency;
  @JsonKey(name: 'status')
  final String? status;
  @JsonKey(name: 'statusCode')
  final int statusCode;
  @JsonKey(name: 'productId')
  final int productId;
  @JsonKey(name: 'valid')
  final bool valid;
}
```

**Purpose**: Contains toll validity check results with status and validity period
**Used by**: `tollCheck()` response

### TollCountryDto
**Country information**

```dart
@JsonSerializable()
final class TollCountryDto {
  TollCountryDto({
    required this.id,
    required this.code,
    required this.name,
  });

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'code')
  final String code;
  @JsonKey(name: 'name')
  final String name;
}
```

**Purpose**: Represents a country where toll services are available
**Used by**: `getCountries()` response

### TollProductDto
**Product and pricing information**

```dart
@JsonSerializable()
final class TollProductDto {
  TollProductDto({
    required this.id,
    required this.description,
    required this.vehicleType,
    required this.validityType,
    required this.emissionClass,
    required this.price,
  });

  @JsonKey(name: 'id')
  final int id;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'vehicleTypeName')
  final String vehicleType;
  @JsonKey(name: 'validityType')
  final String validityType;
  @JsonKey(name: 'emissionClass')
  final String emissionClass;
  @JsonKey(name: 'price')
  final double price;
}
```

**Purpose**: Represents toll product with pricing and validity information
**Used by**: `getProducts()` response

### TollRegisterDto
**Registration response data**

```dart
@JsonSerializable()
final class TollRegisterDto {
  TollRegisterDto({
    required this.id,
    required this.total,
    required this.currency,
    required this.createdOn,
    required this.active,
    required this.saleRows,
  });

  @JsonKey(name: 'id')
  final String? id;
  @JsonKey(name: 'total')
  final double? total;
  @JsonKey(name: 'currency')
  final String? currency;
  @JsonKey(name: 'createdOn')
  final DateTime? createdOn;
  @JsonKey(name: 'active')
  final bool active;
  @JsonKey(name: 'saleRows')
  final List<TollSaleRowDto> saleRows;
}
```

**Purpose**: Contains registration confirmation with transaction details
**Used by**: `registerToll()` response

### RegisterTollRequestDto
**Registration request payload**

```dart
@JsonSerializable()
final class RegisterTollRequestDto {
  RegisterTollRequestDto({
    required this.activationDate,
    required this.email,
    required this.productId,
    required this.vehicle,
  });

  @JsonKey(name: 'activationDate')
  final DateTime activationDate;
  @JsonKey(name: 'email')
  final String? email;
  @JsonKey(name: 'kapschProductId')
  final int productId;
  @JsonKey(name: 'vehicle')
  final ({String lpn, String countryCode}) vehicle;
}
```

**Purpose**: Request payload for toll registration
**Used by**: `registerToll()` request

### TollErrorDto
**Structured error information**

```dart
@JsonSerializable()
final class TollErrorDto {
  TollErrorDto({
    required this.code,
    required this.message,
    required this.status,
  });

  @JsonKey(name: 'code')
  final int code;
  @JsonKey(name: 'message')
  final String? message;
  @JsonKey(name: 'status')
  final String? status;
}
```

**Purpose**: Provides structured error information for toll-specific errors
**Used by**: Error handling in specialized toll API calls

## API Call Flows

### 1. Toll Check Flow
**Repository → API → Server**

| Step | Actor | Action | Details | Response |
|------|-------|--------|---------|----------|
| 1 | Repository | Call API | `tollCheck(countryCode, plateNumber)` | - |
| 2 | API | HTTP Request | `GET /check-validity?countryCode=XX&lpn=ABC123` | - |
| 3 | Server | Process Request | Validate toll for vehicle | `TollResponseDto<TollCheckDto>` |
| 4 | API | Return Response | Pass validity data to repository | Response with validity data |
| 5 | Repository | Conditional Call | If toll valid: `updateTollCheck(tollDto)` | - |
| 6 | API | HTTP Request | `POST /set-vehicle-vignette` | - |
| 7 | Server | Update Record | Associate toll with vehicle | `TollDto` (updated) |
| 8 | API | Return Response | Pass updated record to repository | Updated toll record |

**Flow Summary:**
- **Primary Flow**: Check toll validity for a specific vehicle in a country
- **Conditional Flow**: If valid toll found, associate it with the vehicle
- **Error Handling**: Standard error handling via `callWithResult`
- **Repository Integration**: Called by `TollRepository.checkToll()`

### 2. Toll Registration Flow
**Repository → API → Server**

| Step | Actor | Action | Details | Response |
|------|-------|--------|---------|----------|
| 1 | Repository | Call API | `registerToll(requestList)` | - |
| 2 | API | HTTP Request | `POST /register` with `saleRows` form field | - |
| 3 | Server | Process Registration | Create toll registration with payment | `TollResponseDto<TollRegisterDto>` |
| 4 | API | Return Response | Pass registration confirmation | Registration confirmation |
| 5 | Repository | Call API | `addToll(tollDto)` - add to user account | - |
| 6 | API | HTTP Request | `POST /add` with toll DTO in body | - |
| 7 | Server | Create Record | Add toll record to user's account | `TollDto` (created) |
| 8 | API | Return Response | Pass created record to repository | Created toll record |

**Flow Summary:**
- **Two-Phase Process**: Registration + Account Addition
- **Payment Integration**: Registration includes payment processing
- **Error Handling**: Toll-specific error handling via `callWithTollResult`
- **Repository Integration**: Called by `TollRepository.registerToll()`

### 3. User Data Retrieval Flow
**Repository → API → Server**

| Step | Actor | Action | Details | Response |
|------|-------|--------|---------|----------|
| **User Toll Data** |
| 1 | Repository | Call API | `getUserToll()` | - |
| 2 | API | HTTP Request | `GET /user-vignettes` | - |
| 3 | Server | Fetch Data | Retrieve user's toll records | `List<TollDto>` |
| 4 | API | Return Response | Pass toll records to repository | User's toll records |
| **Country Data** |
| 5 | Repository | Call API | `getCountries()` | - |
| 6 | API | HTTP Request | `GET /countries` | - |
| 7 | Server | Fetch Data | Retrieve available countries | `List<TollCountryDto>` |
| 8 | API | Return Response | Pass countries to repository | Available countries |
| **Product Data** |
| 9 | Repository | Call API | `getProducts()` | - |
| 10 | API | HTTP Request | `GET /products` | - |
| 11 | Server | Fetch Data | Retrieve toll products and pricing | `List<TollProductDto>` |
| 12 | API | Return Response | Pass products to repository | Available products |

**Flow Summary:**
- **Batch Operations**: Multiple independent data retrieval calls
- **Caching Strategy**: Countries and products use `callWithCachedResult`
- **Authentication**: User tolls require authentication, others are public
- **Repository Integration**: Called during cache refresh and initialization

### 4. Cache Refresh Flow
**Authentication → Repository → API → Server**

| Step | Actor | Action | Details | Response |
|------|-------|--------|---------|----------|
| 1 | AuthRepository | State Change | User login/logout event | - |
| 2 | AuthRepository | Notify Stream | `loggedInStream` emits new state | Authentication state |
| 3 | Repository | Listen Stream | Receive authentication state change | - |
| 4 | Repository | Invalidate Cache | `_cachedResponse.add(null)` | - |
| 5 | Repository | Trigger Refresh | `_getTollData()` called automatically | - |
| 6 | Repository | Call API | `getUserToll()` for fresh data | - |
| 7 | API | HTTP Request | `GET /user-vignettes` | - |
| 8 | Server | Fetch Data | Retrieve current user's toll records | `List<TollDto>` |
| 9 | API | Return Response | Pass fresh data to repository | Fresh toll data |
| 10 | Repository | Update Cache | Transform DTOs and update `_tollStream` | - |
| 11 | Repository | Notify Clients | Stream emits updated data | Updated cache stream |

**Flow Summary:**
- **Reactive Pattern**: Authentication changes automatically trigger cache refresh
- **Stream-Based**: Uses RxDart BehaviorSubject for cache management
- **Automatic Process**: No manual intervention required for cache updates
- **Repository Integration**: Core part of `TollRepository` caching strategy

## Error Handling

### Error Response Structure

The API uses a specialized error handling system that wraps standard HTTP errors with toll-specific error information.

### Standard Error Handling
**Function**: `callWithResult<T>`

```dart
Future<Result<T>> callWithResult<T>(CallFunction<T> call) async {
  try {
    final result = await call();
    return Result.success(result);
  } on DioException catch (e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.connectionError:
        return Result.networkError();
      case DioExceptionType.badResponse:
        return Result.httpError(e.response?.statusCode ?? 0,
                               e.response?.statusMessage ?? '',
                               data: e.response?.data);
      case DioExceptionType.cancel:
        return Result.canceled();
      case DioExceptionType.unknown:
        return Result.otherError(e);
    }
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### Toll-Specific Error Handling
**Function**: `callWithTollResult<T>`

```dart
Future<Result<T>> callWithTollResult<T>(CallFunction<T> call) async {
  try {
    final result = await callWithResult(call);
    if (result is HttpError) {
      final error = result as HttpError;
      if (error.code == 500) {
        // Parse toll-specific error format from response data
        final jsonString = error.data.substring(
          error.data.indexOf('{'),
          error.data.lastIndexOf('}') + 1,
        );
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        final first = (json['errors'] as List?)?.firstOrNull;

        if (first != null) {
          return Result.tollError(
            TollErrorDto.fromJson(first as Map<String, dynamic>),
          );
        }
      }
    }
    return result;
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### Toll Error Codes

| Code | Description | Localization Key |
|------|-------------|------------------|
| 202, 206, 207 | Invalid start date | `error_toll_start_date` |
| 203 | Invalid email address | `error_toll_email` |
| 204 | Invalid vehicle information | `error_toll_vehicle` |
| 210 | Toll already activated | `error_toll_activated` |
| 217 | Toll already purchased | `error_toll_has_prucase` |

### Error Handling by Endpoint

| Endpoint | Error Handler | Error Types |
|----------|---------------|-------------|
| `getCountries()` | `callWithCachedResult` | Network, HTTP, Cache |
| `getProducts()` | `callWithCachedResult` | Network, HTTP, Cache |
| `tollCheck()` | `callWithResult` | Network, HTTP, Validation |
| `registerToll()` | `callWithTollResult` | Network, HTTP, Toll-specific |
| `activateToll()` | `callWithTollResult` | Network, HTTP, Toll-specific |
| `getUserToll()` | `callWithResult` | Network, HTTP, Authentication |
| `addToll()` | `callWithResult` | Network, HTTP, Validation |
| `updateToll()` | `callWithResult` | Network, HTTP, Validation |
| `updateTollCheck()` | `callWithResult` | Network, HTTP, Validation |

## Authentication

### Authentication Mechanism
The TollApi does not implement explicit authentication mechanisms. Authentication is handled at the Dio client level through interceptors.

**Authentication Flow:**
1. Dio interceptors automatically add authentication headers
2. Authentication state is managed by `AuthRepository`
3. Token refresh is handled transparently by interceptors
4. API calls automatically include required authentication

### Authentication Dependencies
```dart
// Authentication is injected at the Dio level
final dio = Dio();
// Add authentication interceptors to dio instance
final tollApi = TollApi(dio);
```

### Authenticated Endpoints
All endpoints require authentication except:
- `getCountries()` - Public endpoint
- `getProducts()` - Public endpoint

### Authentication Errors
- **401 Unauthorized**: Token expired or invalid
- **403 Forbidden**: Insufficient permissions
- **Authentication errors are handled by Dio interceptors**

## Usage Examples

### 1. Basic API Usage in Repository

```dart
class TollRepository {
  final TollApi _tollApi;

  Future<Result<List<TollCountry>>> getCountries() async {
    try {
      final result = await callWithCachedResult(_tollApi.getCountries);
      return result.listMap(TollCountry.fromDto);
    } catch (e) {
      return Result.otherError(e);
    }
  }
}
```

### 2. Toll Check with Error Handling

```dart
Future<Result<TollCheckData?>> checkToll({
  required TollCountry country,
  required String plateNumber,
}) async {
  try {
    final result = await callWithResult(
      () => _tollApi.tollCheck(
        countryCode: country.code,
        plateNumber: plateNumber,
      ),
    );

    if (result.isFailure) {
      return (result as Failure).transform();
    }

    // Process successful response
    return result.map((e) => TollCheckData.fromDto(e.data));
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### 3. Toll Registration with Multiple API Calls

```dart
Future<Result<TollData>> registerToll(TollRegisterData data) async {
  try {
    // Step 1: Register toll
    final registerResult = await callWithTollResult(
      () => _tollApi.registerToll(request: [data.toDto()]),
    );

    if (registerResult.isFailure) {
      return (registerResult as Failure).transform();
    }

    // Step 2: Add toll to user account
    final addResult = await callWithResult(
      () => _tollApi.addToll(
        dto: TollData.fromRegisterData(user, registerResult.maybeValue!).toDto(),
      ),
    );

    return addResult.map(TollData.fromDto);
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### 4. Cached Data Retrieval

```dart
Future<Result<List<TollProduct>>> getProducts() async {
  try {
    final result = await callWithCachedResult(
      _tollApi.getProducts,
      cacheKey: 'toll_products',
      ttl: Duration(hours: 1),
    );
    return result.listMap(TollProduct.fromDto);
  } catch (e) {
    return Result.otherError(e);
  }
}
```

### 5. Stream-based Data Updates

```dart
// Repository automatically updates stream when API data changes
Stream<Result<List<TollData>>> get toll => _tollStream;

// API calls trigger stream updates
Future<void> refresh() async {
  _cachedResponse.add(null); // Invalidate cache
  // Stream automatically refreshes via _getTollData()
}
```

## Summary

The Toll API layer provides a comprehensive, type-safe interface for toll operations with:

- **9 RESTful endpoints** covering all toll operations
- **8 specialized DTOs** for type-safe data transfer
- **Generic response wrapper** for consistent API responses
- **Specialized error handling** for toll-specific errors
- **Automatic authentication** via Dio interceptors
- **Caching support** for performance optimization
- **Repository integration** for business logic separation

The API is designed to be consumed exclusively by the `TollRepository`, which handles caching, error processing, and data transformation to domain models.